{"version": 3, "file": "ColladaExporter.js", "sources": ["../../src/exporters/ColladaExporter.ts"], "sourcesContent": ["import {\n  BufferAttribute,\n  BufferGeometry,\n  Color,\n  DoubleSide,\n  InterleavedBufferAttribute,\n  Material,\n  Matrix4,\n  Mesh,\n  MeshBasicMaterial,\n  MeshLambertMaterial,\n  MeshPhongMaterial,\n  Object3D,\n  Texture,\n} from 'three'\nimport type { TypedArray, TypedArrayConstructors } from '../types/shared'\nimport { UV1 } from '../_polyfill/uv1'\n\n/**\n * https://github.com/gkjohnson/collada-exporter-js\n *\n * Usage:\n *  const exporter = new ColladaExporter();\n *\n *  const data = exporter.parse(mesh);\n *\n * Format Definition:\n *  https://www.khronos.org/collada/\n */\n\nexport interface ColladaExporterOptions {\n  author?: string\n  textureDirectory?: string\n  version?: string\n}\n\nexport interface ColladaExporterResult {\n  data: string\n  textures: object[]\n}\n\ntype GeometryInfo = { meshid: string; bufferGeometry: BufferGeometry }\n\ntype MaterialRepresentation = MeshPhongMaterial | MeshBasicMaterial | MeshLambertMaterial\n\nclass ColladaExporter {\n  private options: {\n    version: string\n    author: string | null\n    textureDirectory: string\n    upAxis: string\n    unitName: string | null\n    unitMeter: string | null\n  }\n\n  private geometryInfo: WeakMap<BufferGeometry, GeometryInfo>\n  private materialMap: WeakMap<MaterialRepresentation, string>\n  private imageMap: WeakMap<Texture, string>\n  private textures: {\n    directory: string\n    name: string\n    ext: string\n    data: Uint8Array\n    original: Texture\n  }[]\n\n  private libraryImages: string[]\n  private libraryGeometries: string[]\n  private libraryEffects: string[]\n  private libraryMaterials: string[]\n\n  private canvas: HTMLCanvasElement | null\n  private ctx: CanvasRenderingContext2D | null\n\n  private transMat: Matrix4 | null\n\n  private getFuncs = ['getX', 'getY', 'getZ', 'getW'] as const\n\n  constructor() {\n    this.options = {\n      version: '1.4.1',\n      author: null,\n      textureDirectory: '',\n      upAxis: 'Y_UP',\n      unitName: null,\n      unitMeter: null,\n    }\n\n    this.geometryInfo = new WeakMap()\n    this.materialMap = new WeakMap()\n    this.imageMap = new WeakMap()\n    this.textures = []\n\n    this.libraryImages = []\n    this.libraryGeometries = []\n    this.libraryEffects = []\n    this.libraryMaterials = []\n\n    this.canvas = null\n    this.ctx = null\n\n    this.transMat = null\n  }\n\n  public parse(\n    object: Object3D,\n    onDone: (res: ColladaExporterResult) => void,\n    options: ColladaExporterOptions = {},\n  ): ColladaExporterResult | null {\n    this.options = { ...this.options, ...options }\n\n    if (this.options.upAxis.match(/^[XYZ]_UP$/) === null) {\n      console.error('ColladaExporter: Invalid upAxis: valid values are X_UP, Y_UP or Z_UP.')\n      return null\n    }\n\n    if (this.options.unitName !== null && this.options.unitMeter === null) {\n      console.error('ColladaExporter: unitMeter needs to be specified if unitName is specified.')\n      return null\n    }\n\n    if (this.options.unitMeter !== null && this.options.unitName === null) {\n      console.error('ColladaExporter: unitName needs to be specified if unitMeter is specified.')\n      return null\n    }\n\n    if (this.options.textureDirectory !== '') {\n      this.options.textureDirectory = `${this.options.textureDirectory}/`.replace(/\\\\/g, '/').replace(/\\/+/g, '/')\n    }\n\n    if (this.options.version !== '1.4.1' && this.options.version !== '1.5.0') {\n      console.warn(`ColladaExporter : Version ${this.options.version} not supported for export. Only 1.4.1 and 1.5.0.`)\n      return null\n    }\n\n    const libraryVisualScenes = this.processObject(object)\n\n    const specLink =\n      this.options.version === '1.4.1'\n        ? 'http://www.collada.org/2005/11/COLLADASchema'\n        : 'https://www.khronos.org/collada/'\n    let dae = `<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\" ?>${`<COLLADA xmlns=\"${specLink}\" version=\"${this.options.version}\">`}<asset><contributor><authoring_tool>three.js Collada Exporter</authoring_tool>${\n      this.options.author !== null ? `<author>${this.options.author}</author>` : ''\n    }</contributor>${`<created>${new Date().toISOString()}</created>`}${`<modified>${new Date().toISOString()}</modified>`}<up_axis>Y_UP</up_axis></asset>`\n\n    dae += `<library_images>${this.libraryImages.join('')}</library_images>`\n\n    dae += `<library_effects>${this.libraryEffects.join('')}</library_effects>`\n\n    dae += `<library_materials>${this.libraryMaterials.join('')}</library_materials>`\n\n    dae += `<library_geometries>${this.libraryGeometries.join('')}</library_geometries>`\n\n    dae += `<library_visual_scenes><visual_scene id=\"Scene\" name=\"scene\">${libraryVisualScenes}</visual_scene></library_visual_scenes>`\n\n    dae += '<scene><instance_visual_scene url=\"#Scene\"/></scene>'\n\n    dae += '</COLLADA>'\n\n    const res = {\n      data: this.format(dae),\n      textures: this.textures,\n    }\n\n    if (typeof onDone === 'function') {\n      requestAnimationFrame(() => onDone(res))\n    }\n\n    return res\n  }\n\n  // Convert the urdf xml into a well-formatted, indented format\n  private format(urdf: string): string {\n    const IS_END_TAG = /^<\\//\n    const IS_SELF_CLOSING = /(\\?>$)|(\\/>$)/\n    const HAS_TEXT = /<[^>]+>[^<]*<\\/[^<]+>/\n\n    const pad = (ch: string, num: number): string => (num > 0 ? ch + pad(ch, num - 1) : '')\n\n    let tagnum = 0\n\n    return (\n      urdf\n        .match(/(<[^>]+>[^<]+<\\/[^<]+>)|(<[^>]+>)/g)\n        ?.map((tag) => {\n          if (!HAS_TEXT.test(tag) && !IS_SELF_CLOSING.test(tag) && IS_END_TAG.test(tag)) {\n            tagnum--\n          }\n\n          const res = `${pad('  ', tagnum)}${tag}`\n\n          if (!HAS_TEXT.test(tag) && !IS_SELF_CLOSING.test(tag) && !IS_END_TAG.test(tag)) {\n            tagnum++\n          }\n\n          return res\n        })\n        .join('\\n') ?? ''\n    )\n  }\n\n  // Convert an image into a png format for saving\n  private base64ToBuffer(str: string): Uint8Array {\n    const b = atob(str)\n    const buf = new Uint8Array(b.length)\n\n    for (let i = 0, l = buf.length; i < l; i++) {\n      buf[i] = b.charCodeAt(i)\n    }\n\n    return buf\n  }\n\n  private imageToData(image: CanvasImageSource, ext: string): Uint8Array {\n    this.canvas = this.canvas || document.createElement('canvas')\n    this.ctx = this.ctx || this.canvas.getContext('2d')\n\n    this.canvas.width = image.width instanceof SVGAnimatedLength ? 0 : image.width\n    this.canvas.height = image.height instanceof SVGAnimatedLength ? 0 : image.height\n\n    this.ctx?.drawImage(image, 0, 0)\n\n    // Get the base64 encoded data\n    const base64data = this.canvas.toDataURL(`image/${ext}`, 1).replace(/^data:image\\/(png|jpg);base64,/, '')\n\n    // Convert to a uint8 array\n    return this.base64ToBuffer(base64data)\n  }\n\n  // gets the attribute array. Generate a new array if the attribute is interleaved\n  private attrBufferToArray(attr: InterleavedBufferAttribute | BufferAttribute): number[] | ArrayLike<number> {\n    if (attr instanceof InterleavedBufferAttribute && attr.isInterleavedBufferAttribute) {\n      // use the typed array constructor to save on memory\n      const TypedArrayConstructor: TypedArrayConstructors = attr.array.constructor\n      // @ts-ignore\n      const arr: number[] = new TypedArrayConstructor(attr.count * attr.itemSize)\n      const size = attr.itemSize\n\n      for (let i = 0, l = attr.count; i < l; i++) {\n        for (let j = 0; j < size; j++) {\n          arr[i * size + j] = attr[this.getFuncs[j]](i)\n        }\n      }\n\n      return arr\n    } else {\n      return attr.array\n    }\n  }\n\n  // Returns an array of the same type starting at the `st` index,\n  // and `ct` length\n  private subArray(arr: number[] | ArrayLike<number>, st: number, ct: number): TypedArray | number[] {\n    if (Array.isArray(arr)) {\n      return arr.slice(st, st + ct)\n    } else {\n      const TypedArrayConstructor: TypedArrayConstructors = arr.constructor\n      // @ts-ignore\n      return new TypedArrayConstructor(arr.buffer, st * arr.BYTES_PER_ELEMENT, ct)\n    }\n  }\n\n  // Returns the string for a geometry's attribute\n  private getAttribute(\n    attr: InterleavedBufferAttribute | BufferAttribute,\n    name: string,\n    params: string[],\n    type: string,\n  ): string {\n    const array = this.attrBufferToArray(attr)\n    const res = Array.isArray(array)\n      ? `${\n          `<source id=\"${name}\">` + `<float_array id=\"${name}-array\" count=\"${array.length}\">` + array.join(' ')\n        }</float_array><technique_common>${`<accessor source=\"#${name}-array\" count=\"${Math.floor(\n          array.length / attr.itemSize,\n        )}\" stride=\"${attr.itemSize}\">`}${params\n          .map((n) => `<param name=\"${n}\" type=\"${type}\" />`)\n          .join('')}</accessor></technique_common></source>`\n      : ''\n\n    return res\n  }\n\n  // Returns the string for a node's transform information\n  private getTransform(o: Object3D): string {\n    // ensure the object's matrix is up to date\n    // before saving the transform\n    o.updateMatrix()\n\n    this.transMat = this.transMat || new Matrix4()\n    this.transMat.copy(o.matrix)\n    this.transMat.transpose()\n    return `<matrix>${this.transMat.toArray().join(' ')}</matrix>`\n  }\n\n  // Process the given piece of geometry into the geometry library\n  // Returns the mesh id\n  private processGeometry(g: BufferGeometry): GeometryInfo {\n    let info = this.geometryInfo.get(g)\n\n    if (!info) {\n      // convert the geometry to bufferGeometry if it isn't already\n      const bufferGeometry = g\n\n      if (!bufferGeometry.isBufferGeometry) {\n        throw new Error('THREE.ColladaExporter: Geometry is not of type THREE.BufferGeometry.')\n      }\n\n      const meshid = `Mesh${this.libraryGeometries.length + 1}`\n\n      const indexCount = bufferGeometry.index\n        ? bufferGeometry.index.count * bufferGeometry.index.itemSize\n        : bufferGeometry.attributes.position.count\n\n      const groups =\n        bufferGeometry.groups != null && bufferGeometry.groups.length !== 0\n          ? bufferGeometry.groups\n          : [{ start: 0, count: indexCount, materialIndex: 0 }]\n\n      const gname = g.name ? ` name=\"${g.name}\"` : ''\n      let gnode = `<geometry id=\"${meshid}\"${gname}><mesh>`\n\n      // define the geometry node and the vertices for the geometry\n      const posName = `${meshid}-position`\n      const vertName = `${meshid}-vertices`\n      gnode += this.getAttribute(bufferGeometry.attributes.position, posName, ['X', 'Y', 'Z'], 'float')\n      gnode += `<vertices id=\"${vertName}\"><input semantic=\"POSITION\" source=\"#${posName}\" /></vertices>`\n\n      // NOTE: We're not optimizing the attribute arrays here, so they're all the same length and\n      // can therefore share the same triangle indices. However, MeshLab seems to have trouble opening\n      // models with attributes that share an offset.\n      // MeshLab Bug#424: https://sourceforge.net/p/meshlab/bugs/424/\n\n      // serialize normals\n      let triangleInputs = `<input semantic=\"VERTEX\" source=\"#${vertName}\" offset=\"0\" />`\n      if ('normal' in bufferGeometry.attributes) {\n        const normName = `${meshid}-normal`\n        gnode += this.getAttribute(bufferGeometry.attributes.normal, normName, ['X', 'Y', 'Z'], 'float')\n        triangleInputs += `<input semantic=\"NORMAL\" source=\"#${normName}\" offset=\"0\" />`\n      }\n\n      // serialize uvs\n      if ('uv' in bufferGeometry.attributes) {\n        const uvName = `${meshid}-texcoord`\n        gnode += this.getAttribute(bufferGeometry.attributes.uv, uvName, ['S', 'T'], 'float')\n        triangleInputs += `<input semantic=\"TEXCOORD\" source=\"#${uvName}\" offset=\"0\" set=\"0\" />`\n      }\n\n      // serialize lightmap uvs\n      if (UV1 in bufferGeometry.attributes) {\n        const uvName = `${meshid}-texcoord2`\n        gnode += this.getAttribute(bufferGeometry.attributes[UV1], uvName, ['S', 'T'], 'float')\n        triangleInputs += `<input semantic=\"TEXCOORD\" source=\"#${uvName}\" offset=\"0\" set=\"1\" />`\n      }\n\n      // serialize colors\n      if ('color' in bufferGeometry.attributes) {\n        const colName = `${meshid}-color`\n        gnode += this.getAttribute(bufferGeometry.attributes.color, colName, ['X', 'Y', 'Z'], 'uint8')\n        triangleInputs += `<input semantic=\"COLOR\" source=\"#${colName}\" offset=\"0\" />`\n      }\n\n      let indexArray: number[] | ArrayLike<number> | null = null\n      if (bufferGeometry.index) {\n        indexArray = this.attrBufferToArray(bufferGeometry.index)\n      } else {\n        indexArray = new Array(indexCount)\n        for (let i = 0, l = indexArray.length; i < l && Array.isArray(indexArray); i++) indexArray[i] = i\n      }\n\n      for (let i = 0, l = groups.length; i < l; i++) {\n        const group = groups[i]\n        const subarr = this.subArray(indexArray, group.start, group.count)\n        const polycount = subarr.length / 3\n        gnode += `<triangles material=\"MESH_MATERIAL_${group.materialIndex}\" count=\"${polycount}\">`\n        gnode += triangleInputs\n\n        gnode += `<p>${subarr.join(' ')}</p>`\n        gnode += '</triangles>'\n      }\n\n      gnode += '</mesh></geometry>'\n\n      this.libraryGeometries.push(gnode)\n\n      info = { meshid, bufferGeometry }\n      this.geometryInfo.set(g, info)\n    }\n\n    return info\n  }\n\n  // Process the given texture into the image library\n  // Returns the image library\n  private processTexture(tex: Texture): string {\n    let texid = this.imageMap.get(tex)\n    if (texid == null) {\n      texid = `image-${this.libraryImages.length + 1}`\n\n      const ext = 'png'\n      const name = tex.name || texid\n      let imageNode = `<image id=\"${texid}\" name=\"${name}\">`\n\n      if (this.options.version === '1.5.0') {\n        imageNode += `<init_from><ref>${this.options.textureDirectory}${name}.${ext}</ref></init_from>`\n      } else {\n        // version image node 1.4.1\n        imageNode += `<init_from>${this.options.textureDirectory}${name}.${ext}</init_from>`\n      }\n\n      imageNode += '</image>'\n\n      this.libraryImages.push(imageNode)\n      this.imageMap.set(tex, texid)\n      this.textures.push({\n        directory: this.options.textureDirectory,\n        name,\n        ext,\n        data: this.imageToData(tex.image, ext),\n        original: tex,\n      })\n    }\n\n    return texid\n  }\n\n  // Process the given material into the material and effect libraries\n  // Returns the material id\n  private processMaterial(m: MaterialRepresentation): string {\n    let matid = this.materialMap.get(m)\n\n    if (matid == null) {\n      matid = `Mat${this.libraryEffects.length + 1}`\n\n      let type = 'phong'\n\n      if (m instanceof MeshLambertMaterial) {\n        type = 'lambert'\n      } else if (m instanceof MeshBasicMaterial) {\n        type = 'constant'\n\n        if (m.map !== null) {\n          // The Collada spec does not support diffuse texture maps with the\n          // constant shader type.\n          // mrdoob/three.js#15469\n          console.warn('ColladaExporter: Texture maps not supported with MeshBasicMaterial.')\n        }\n      }\n\n      if (m instanceof MeshPhongMaterial) {\n        const emissive = m.emissive ? m.emissive : new Color(0, 0, 0)\n        const diffuse = m.color ? m.color : new Color(0, 0, 0)\n        const specular = m.specular ? m.specular : new Color(1, 1, 1)\n        const shininess = m.shininess || 0\n        const reflectivity = m.reflectivity || 0\n\n        // Do not export and alpha map for the reasons mentioned in issue (#13792)\n        // in three.js alpha maps are black and white, but collada expects the alpha\n        // channel to specify the transparency\n        let transparencyNode = ''\n        if (m.transparent) {\n          transparencyNode += `<transparent>${\n            m.map ? '<texture texture=\"diffuse-sampler\"></texture>' : '<float>1</float>'\n          }</transparent>`\n\n          if (m.opacity < 1) {\n            transparencyNode += `<transparency><float>${m.opacity}</float></transparency>`\n          }\n        }\n\n        const techniqueNode = `${`<technique sid=\"common\"><${type}>`}<emission>${\n          m.emissiveMap\n            ? '<texture texture=\"emissive-sampler\" texcoord=\"TEXCOORD\" />'\n            : `<color sid=\"emission\">${emissive.r} ${emissive.g} ${emissive.b} 1</color>`\n        }</emission>${\n          type !== 'constant'\n            ? `<diffuse>${\n                m.map\n                  ? '<texture texture=\"diffuse-sampler\" texcoord=\"TEXCOORD\" />'\n                  : `<color sid=\"diffuse\">${diffuse.r} ${diffuse.g} ${diffuse.b} 1</color>`\n              }</diffuse>`\n            : ''\n        }${\n          type !== 'constant'\n            ? `<bump>${m.normalMap ? '<texture texture=\"bump-sampler\" texcoord=\"TEXCOORD\" />' : ''}</bump>`\n            : ''\n        }${\n          type === 'phong'\n            ? `${`<specular><color sid=\"specular\">${specular.r} ${specular.g} ${specular.b} 1</color></specular>`}<shininess>${\n                m.specularMap\n                  ? '<texture texture=\"specular-sampler\" texcoord=\"TEXCOORD\" />'\n                  : `<float sid=\"shininess\">${shininess}</float>`\n              }</shininess>`\n            : ''\n        }${`<reflective><color>${diffuse.r} ${diffuse.g} ${diffuse.b} 1</color></reflective>`}${`<reflectivity><float>${reflectivity}</float></reflectivity>`}${transparencyNode}${`</${type}></technique>`}`\n\n        const effectnode = `${`<effect id=\"${matid}-effect\">`}<profile_COMMON>${\n          m.map\n            ? `<newparam sid=\"diffuse-surface\"><surface type=\"2D\">${`<init_from>${this.processTexture(\n                m.map,\n              )}</init_from>`}</surface></newparam><newparam sid=\"diffuse-sampler\"><sampler2D><source>diffuse-surface</source></sampler2D></newparam>`\n            : ''\n        }${\n          m.specularMap\n            ? `<newparam sid=\"specular-surface\"><surface type=\"2D\">${`<init_from>${this.processTexture(\n                m.specularMap,\n              )}</init_from>`}</surface></newparam><newparam sid=\"specular-sampler\"><sampler2D><source>specular-surface</source></sampler2D></newparam>`\n            : ''\n        }${\n          m.emissiveMap\n            ? `<newparam sid=\"emissive-surface\"><surface type=\"2D\">${`<init_from>${this.processTexture(\n                m.emissiveMap,\n              )}</init_from>`}</surface></newparam><newparam sid=\"emissive-sampler\"><sampler2D><source>emissive-surface</source></sampler2D></newparam>`\n            : ''\n        }${\n          m.normalMap\n            ? `<newparam sid=\"bump-surface\"><surface type=\"2D\">${`<init_from>${this.processTexture(\n                m.normalMap,\n              )}</init_from>`}</surface></newparam><newparam sid=\"bump-sampler\"><sampler2D><source>bump-surface</source></sampler2D></newparam>`\n            : ''\n        }${techniqueNode}${\n          m.side === DoubleSide\n            ? '<extra><technique profile=\"THREEJS\"><double_sided sid=\"double_sided\" type=\"int\">1</double_sided></technique></extra>'\n            : ''\n        }</profile_COMMON></effect>`\n\n        const materialName = m.name ? ` name=\"${m.name}\"` : ''\n        const materialNode = `<material id=\"${matid}\"${materialName}><instance_effect url=\"#${matid}-effect\" /></material>`\n\n        this.libraryMaterials.push(materialNode)\n        this.libraryEffects.push(effectnode)\n        this.materialMap.set(m, matid)\n      }\n    }\n\n    return matid\n  }\n\n  // Recursively process the object into a scene\n  private processObject(o: Object3D): string {\n    let node = `<node name=\"${o.name}\">`\n\n    node += this.getTransform(o)\n    const a: Mesh<BufferGeometry, Material | Material[]> = new Mesh()\n    a.geometry\n\n    if (o instanceof Mesh && o.isMesh && o.geometry !== null) {\n      // function returns the id associated with the mesh and a \"BufferGeometry\" version\n      // of the geometry in case it's not a geometry.\n      const geomInfo = this.processGeometry(o.geometry)\n      const meshid = geomInfo.meshid\n      const geometry = geomInfo.bufferGeometry\n\n      // ids of the materials to bind to the geometry\n      let matids = null\n      let matidsArray\n\n      // get a list of materials to bind to the sub groups of the geometry.\n      // If the amount of subgroups is greater than the materials, than reuse\n      // the materials.\n      const mat: MaterialRepresentation | MaterialRepresentation[] = o.material || new MeshBasicMaterial()\n      const materials = Array.isArray(mat) ? mat : [mat]\n\n      if (geometry.groups.length > materials.length) {\n        matidsArray = new Array(geometry.groups.length)\n      } else {\n        matidsArray = new Array(materials.length)\n      }\n\n      matids = matidsArray.fill(null).map((_, i) => this.processMaterial(materials[i % materials.length]))\n\n      node += `${\n        `<instance_geometry url=\"#${meshid}\">` +\n        (matids != null\n          ? `<bind_material><technique_common>${matids\n              .map(\n                (id, i) =>\n                  `${`<instance_material symbol=\"MESH_MATERIAL_${i}\" target=\"#${id}\" >`}<bind_vertex_input semantic=\"TEXCOORD\" input_semantic=\"TEXCOORD\" input_set=\"0\" /></instance_material>`,\n              )\n              .join('')}</technique_common></bind_material>`\n          : '')\n      }</instance_geometry>`\n    }\n\n    o.children.forEach((c) => (node += this.processObject(c)))\n\n    node += '</node>'\n\n    return node\n  }\n}\n\nexport { ColladaExporter }\n"], "names": [], "mappings": ";;;;;;;;AA6CA,MAAM,gBAAgB;AAAA,EAiCpB,cAAc;AAhCN;AASA;AACA;AACA;AACA;AAQA;AACA;AACA;AACA;AAEA;AACA;AAEA;AAEA,oCAAW,CAAC,QAAQ,QAAQ,QAAQ,MAAM;AAGhD,SAAK,UAAU;AAAA,MACb,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,kBAAkB;AAAA,MAClB,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,WAAW;AAAA,IAAA;AAGR,SAAA,mCAAmB;AACnB,SAAA,kCAAkB;AAClB,SAAA,+BAAe;AACpB,SAAK,WAAW;AAEhB,SAAK,gBAAgB;AACrB,SAAK,oBAAoB;AACzB,SAAK,iBAAiB;AACtB,SAAK,mBAAmB;AAExB,SAAK,SAAS;AACd,SAAK,MAAM;AAEX,SAAK,WAAW;AAAA,EAClB;AAAA,EAEO,MACL,QACA,QACA,UAAkC,CAAA,GACJ;AAC9B,SAAK,UAAU,EAAE,GAAG,KAAK,SAAS,GAAG;AAErC,QAAI,KAAK,QAAQ,OAAO,MAAM,YAAY,MAAM,MAAM;AACpD,cAAQ,MAAM,uEAAuE;AAC9E,aAAA;AAAA,IACT;AAEA,QAAI,KAAK,QAAQ,aAAa,QAAQ,KAAK,QAAQ,cAAc,MAAM;AACrE,cAAQ,MAAM,4EAA4E;AACnF,aAAA;AAAA,IACT;AAEA,QAAI,KAAK,QAAQ,cAAc,QAAQ,KAAK,QAAQ,aAAa,MAAM;AACrE,cAAQ,MAAM,4EAA4E;AACnF,aAAA;AAAA,IACT;AAEI,QAAA,KAAK,QAAQ,qBAAqB,IAAI;AACxC,WAAK,QAAQ,mBAAmB,GAAG,KAAK,QAAQ,oBAAoB,QAAQ,OAAO,GAAG,EAAE,QAAQ,QAAQ,GAAG;AAAA,IAC7G;AAEA,QAAI,KAAK,QAAQ,YAAY,WAAW,KAAK,QAAQ,YAAY,SAAS;AACxE,cAAQ,KAAK,6BAA6B,KAAK,QAAQ,yDAAyD;AACzG,aAAA;AAAA,IACT;AAEM,UAAA,sBAAsB,KAAK,cAAc,MAAM;AAErD,UAAM,WACJ,KAAK,QAAQ,YAAY,UACrB,iDACA;AACF,QAAA,MAAM,0DAA0D,mBAAmB,sBAAsB,KAAK,QAAQ,4FACxH,KAAK,QAAQ,WAAW,OAAO,WAAW,KAAK,QAAQ,oBAAoB,mBAC5D,aAAY,oBAAI,QAAO,4BAA4B,cAAa,oBAAI,KAAK,GAAE;AAE5F,WAAO,mBAAmB,KAAK,cAAc,KAAK,EAAE;AAEpD,WAAO,oBAAoB,KAAK,eAAe,KAAK,EAAE;AAEtD,WAAO,sBAAsB,KAAK,iBAAiB,KAAK,EAAE;AAE1D,WAAO,uBAAuB,KAAK,kBAAkB,KAAK,EAAE;AAE5D,WAAO,gEAAgE;AAEhE,WAAA;AAEA,WAAA;AAEP,UAAM,MAAM;AAAA,MACV,MAAM,KAAK,OAAO,GAAG;AAAA,MACrB,UAAU,KAAK;AAAA,IAAA;AAGb,QAAA,OAAO,WAAW,YAAY;AACV,4BAAA,MAAM,OAAO,GAAG,CAAC;AAAA,IACzC;AAEO,WAAA;AAAA,EACT;AAAA;AAAA,EAGQ,OAAO,MAAsB;;AACnC,UAAM,aAAa;AACnB,UAAM,kBAAkB;AACxB,UAAM,WAAW;AAEX,UAAA,MAAM,CAAC,IAAY,QAAyB,MAAM,IAAI,KAAK,IAAI,IAAI,MAAM,CAAC,IAAI;AAEpF,QAAI,SAAS;AAEb,YACE,gBACG,MAAM,oCAAoC,MAD7C,mBAEI,IAAI,CAAC,QAAQ;AACb,UAAI,CAAC,SAAS,KAAK,GAAG,KAAK,CAAC,gBAAgB,KAAK,GAAG,KAAK,WAAW,KAAK,GAAG,GAAG;AAC7E;AAAA,MACF;AAEA,YAAM,MAAM,GAAG,IAAI,MAAM,MAAM,IAAI;AAEnC,UAAI,CAAC,SAAS,KAAK,GAAG,KAAK,CAAC,gBAAgB,KAAK,GAAG,KAAK,CAAC,WAAW,KAAK,GAAG,GAAG;AAC9E;AAAA,MACF;AAEO,aAAA;AAAA,IACR,GACA,KAAK,UAfR,YAeiB;AAAA,EAErB;AAAA;AAAA,EAGQ,eAAe,KAAyB;AACxC,UAAA,IAAI,KAAK,GAAG;AAClB,UAAM,MAAM,IAAI,WAAW,EAAE,MAAM;AAEnC,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAI,GAAG,KAAK;AAC1C,UAAI,CAAC,IAAI,EAAE,WAAW,CAAC;AAAA,IACzB;AAEO,WAAA;AAAA,EACT;AAAA,EAEQ,YAAY,OAA0B,KAAyB;;AACrE,SAAK,SAAS,KAAK,UAAU,SAAS,cAAc,QAAQ;AAC5D,SAAK,MAAM,KAAK,OAAO,KAAK,OAAO,WAAW,IAAI;AAElD,SAAK,OAAO,QAAQ,MAAM,iBAAiB,oBAAoB,IAAI,MAAM;AACzE,SAAK,OAAO,SAAS,MAAM,kBAAkB,oBAAoB,IAAI,MAAM;AAE3E,eAAK,QAAL,mBAAU,UAAU,OAAO,GAAG;AAGxB,UAAA,aAAa,KAAK,OAAO,UAAU,SAAS,OAAO,CAAC,EAAE,QAAQ,kCAAkC,EAAE;AAGjG,WAAA,KAAK,eAAe,UAAU;AAAA,EACvC;AAAA;AAAA,EAGQ,kBAAkB,MAAkF;AACtG,QAAA,gBAAgB,8BAA8B,KAAK,8BAA8B;AAE7E,YAAA,wBAAgD,KAAK,MAAM;AAEjE,YAAM,MAAgB,IAAI,sBAAsB,KAAK,QAAQ,KAAK,QAAQ;AAC1E,YAAM,OAAO,KAAK;AAElB,eAAS,IAAI,GAAG,IAAI,KAAK,OAAO,IAAI,GAAG,KAAK;AAC1C,iBAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AACzB,cAAA,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,SAAS,CAAC,CAAC,EAAE,CAAC;AAAA,QAC9C;AAAA,MACF;AAEO,aAAA;AAAA,IAAA,OACF;AACL,aAAO,KAAK;AAAA,IACd;AAAA,EACF;AAAA;AAAA;AAAA,EAIQ,SAAS,KAAmC,IAAY,IAAmC;AAC7F,QAAA,MAAM,QAAQ,GAAG,GAAG;AACtB,aAAO,IAAI,MAAM,IAAI,KAAK,EAAE;AAAA,IAAA,OACvB;AACL,YAAM,wBAAgD,IAAI;AAE1D,aAAO,IAAI,sBAAsB,IAAI,QAAQ,KAAK,IAAI,mBAAmB,EAAE;AAAA,IAC7E;AAAA,EACF;AAAA;AAAA,EAGQ,aACN,MACA,MACA,QACA,MACQ;AACF,UAAA,QAAQ,KAAK,kBAAkB,IAAI;AACzC,UAAM,MAAM,MAAM,QAAQ,KAAK,IAC3B,GACE,eAAe,0BAA+B,sBAAsB,MAAM,aAAa,MAAM,KAAK,GAAG,oCACpE,sBAAsB,sBAAsB,KAAK;AAAA,MAClF,MAAM,SAAS,KAAK;AAAA,IAAA,cACR,KAAK,eAAe,OAC/B,IAAI,CAAC,MAAM,gBAAgB,YAAY,UAAU,EACjD,KAAK,EAAE,6CACV;AAEG,WAAA;AAAA,EACT;AAAA;AAAA,EAGQ,aAAa,GAAqB;AAGxC,MAAE,aAAa;AAEf,SAAK,WAAW,KAAK,YAAY,IAAI,QAAQ;AACxC,SAAA,SAAS,KAAK,EAAE,MAAM;AAC3B,SAAK,SAAS;AACd,WAAO,WAAW,KAAK,SAAS,QAAQ,EAAE,KAAK,GAAG;AAAA,EACpD;AAAA;AAAA;AAAA,EAIQ,gBAAgB,GAAiC;AACvD,QAAI,OAAO,KAAK,aAAa,IAAI,CAAC;AAElC,QAAI,CAAC,MAAM;AAET,YAAM,iBAAiB;AAEnB,UAAA,CAAC,eAAe,kBAAkB;AAC9B,cAAA,IAAI,MAAM,sEAAsE;AAAA,MACxF;AAEA,YAAM,SAAS,OAAO,KAAK,kBAAkB,SAAS;AAEhD,YAAA,aAAa,eAAe,QAC9B,eAAe,MAAM,QAAQ,eAAe,MAAM,WAClD,eAAe,WAAW,SAAS;AAEvC,YAAM,SACJ,eAAe,UAAU,QAAQ,eAAe,OAAO,WAAW,IAC9D,eAAe,SACf,CAAC,EAAE,OAAO,GAAG,OAAO,YAAY,eAAe,GAAG;AAExD,YAAM,QAAQ,EAAE,OAAO,UAAU,EAAE,UAAU;AACzC,UAAA,QAAQ,iBAAiB,UAAU;AAGvC,YAAM,UAAU,GAAG;AACnB,YAAM,WAAW,GAAG;AACX,eAAA,KAAK,aAAa,eAAe,WAAW,UAAU,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,OAAO;AAChG,eAAS,iBAAiB,iDAAiD;AAQ3E,UAAI,iBAAiB,qCAAqC;AACtD,UAAA,YAAY,eAAe,YAAY;AACzC,cAAM,WAAW,GAAG;AACX,iBAAA,KAAK,aAAa,eAAe,WAAW,QAAQ,UAAU,CAAC,KAAK,KAAK,GAAG,GAAG,OAAO;AAC/F,0BAAkB,qCAAqC;AAAA,MACzD;AAGI,UAAA,QAAQ,eAAe,YAAY;AACrC,cAAM,SAAS,GAAG;AACT,iBAAA,KAAK,aAAa,eAAe,WAAW,IAAI,QAAQ,CAAC,KAAK,GAAG,GAAG,OAAO;AACpF,0BAAkB,uCAAuC;AAAA,MAC3D;AAGI,UAAA,OAAO,eAAe,YAAY;AACpC,cAAM,SAAS,GAAG;AACT,iBAAA,KAAK,aAAa,eAAe,WAAW,GAAG,GAAG,QAAQ,CAAC,KAAK,GAAG,GAAG,OAAO;AACtF,0BAAkB,uCAAuC;AAAA,MAC3D;AAGI,UAAA,WAAW,eAAe,YAAY;AACxC,cAAM,UAAU,GAAG;AACV,iBAAA,KAAK,aAAa,eAAe,WAAW,OAAO,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,OAAO;AAC7F,0BAAkB,oCAAoC;AAAA,MACxD;AAEA,UAAI,aAAkD;AACtD,UAAI,eAAe,OAAO;AACX,qBAAA,KAAK,kBAAkB,eAAe,KAAK;AAAA,MAAA,OACnD;AACQ,qBAAA,IAAI,MAAM,UAAU;AACxB,iBAAA,IAAI,GAAG,IAAI,WAAW,QAAQ,IAAI,KAAK,MAAM,QAAQ,UAAU,GAAG;AAAK,qBAAW,CAAC,IAAI;AAAA,MAClG;AAEA,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AACvC,cAAA,QAAQ,OAAO,CAAC;AACtB,cAAM,SAAS,KAAK,SAAS,YAAY,MAAM,OAAO,MAAM,KAAK;AAC3D,cAAA,YAAY,OAAO,SAAS;AACzB,iBAAA,sCAAsC,MAAM,yBAAyB;AACrE,iBAAA;AAEA,iBAAA,MAAM,OAAO,KAAK,GAAG;AACrB,iBAAA;AAAA,MACX;AAES,eAAA;AAEJ,WAAA,kBAAkB,KAAK,KAAK;AAE1B,aAAA,EAAE,QAAQ;AACZ,WAAA,aAAa,IAAI,GAAG,IAAI;AAAA,IAC/B;AAEO,WAAA;AAAA,EACT;AAAA;AAAA;AAAA,EAIQ,eAAe,KAAsB;AAC3C,QAAI,QAAQ,KAAK,SAAS,IAAI,GAAG;AACjC,QAAI,SAAS,MAAM;AACT,cAAA,SAAS,KAAK,cAAc,SAAS;AAE7C,YAAM,MAAM;AACN,YAAA,OAAO,IAAI,QAAQ;AACrB,UAAA,YAAY,cAAc,gBAAgB;AAE1C,UAAA,KAAK,QAAQ,YAAY,SAAS;AACpC,qBAAa,mBAAmB,KAAK,QAAQ,mBAAmB,QAAQ;AAAA,MAAA,OACnE;AAEL,qBAAa,cAAc,KAAK,QAAQ,mBAAmB,QAAQ;AAAA,MACrE;AAEa,mBAAA;AAER,WAAA,cAAc,KAAK,SAAS;AAC5B,WAAA,SAAS,IAAI,KAAK,KAAK;AAC5B,WAAK,SAAS,KAAK;AAAA,QACjB,WAAW,KAAK,QAAQ;AAAA,QACxB;AAAA,QACA;AAAA,QACA,MAAM,KAAK,YAAY,IAAI,OAAO,GAAG;AAAA,QACrC,UAAU;AAAA,MAAA,CACX;AAAA,IACH;AAEO,WAAA;AAAA,EACT;AAAA;AAAA;AAAA,EAIQ,gBAAgB,GAAmC;AACzD,QAAI,QAAQ,KAAK,YAAY,IAAI,CAAC;AAElC,QAAI,SAAS,MAAM;AACT,cAAA,MAAM,KAAK,eAAe,SAAS;AAE3C,UAAI,OAAO;AAEX,UAAI,aAAa,qBAAqB;AAC7B,eAAA;AAAA,MAAA,WACE,aAAa,mBAAmB;AAClC,eAAA;AAEH,YAAA,EAAE,QAAQ,MAAM;AAIlB,kBAAQ,KAAK,qEAAqE;AAAA,QACpF;AAAA,MACF;AAEA,UAAI,aAAa,mBAAmB;AAC5B,cAAA,WAAW,EAAE,WAAW,EAAE,WAAW,IAAI,MAAM,GAAG,GAAG,CAAC;AACtD,cAAA,UAAU,EAAE,QAAQ,EAAE,QAAQ,IAAI,MAAM,GAAG,GAAG,CAAC;AAC/C,cAAA,WAAW,EAAE,WAAW,EAAE,WAAW,IAAI,MAAM,GAAG,GAAG,CAAC;AACtD,cAAA,YAAY,EAAE,aAAa;AAC3B,cAAA,eAAe,EAAE,gBAAgB;AAKvC,YAAI,mBAAmB;AACvB,YAAI,EAAE,aAAa;AACG,8BAAA,gBAClB,EAAE,MAAM,kDAAkD;AAGxD,cAAA,EAAE,UAAU,GAAG;AACjB,gCAAoB,wBAAwB,EAAE;AAAA,UAChD;AAAA,QACF;AAEA,cAAM,gBAAgB,GAAG,4BAA4B,oBACnD,EAAE,cACE,+DACA,yBAAyB,SAAS,KAAK,SAAS,KAAK,SAAS,2BAElE,SAAS,aACL,YACE,EAAE,MACE,8DACA,wBAAwB,QAAQ,KAAK,QAAQ,KAAK,QAAQ,4BAEhE,KAEJ,SAAS,aACL,SAAS,EAAE,YAAY,2DAA2D,cAClF,KAEJ,SAAS,UACL,GAAG,mCAAmC,SAAS,KAAK,SAAS,KAAK,SAAS,sCACzE,EAAE,cACE,+DACA,0BAA0B,oCAEhC,KACH,sBAAsB,QAAQ,KAAK,QAAQ,KAAK,QAAQ,6BAA6B,wBAAwB,wCAAwC,mBAAmB,KAAK;AAE1K,cAAA,aAAa,GAAG,eAAe,mCACnC,EAAE,MACE,sDAAsD,cAAc,KAAK;AAAA,UACvE,EAAE;AAAA,mJAEJ,KAEJ,EAAE,cACE,uDAAuD,cAAc,KAAK;AAAA,UACxE,EAAE;AAAA,qJAEJ,KAEJ,EAAE,cACE,uDAAuD,cAAc,KAAK;AAAA,UACxE,EAAE;AAAA,qJAEJ,KAEJ,EAAE,YACE,mDAAmD,cAAc,KAAK;AAAA,UACpE,EAAE;AAAA,6IAEJ,KACH,gBACD,EAAE,SAAS,aACP,yHACA;AAGN,cAAM,eAAe,EAAE,OAAO,UAAU,EAAE,UAAU;AAC9C,cAAA,eAAe,iBAAiB,SAAS,uCAAuC;AAEjF,aAAA,iBAAiB,KAAK,YAAY;AAClC,aAAA,eAAe,KAAK,UAAU;AAC9B,aAAA,YAAY,IAAI,GAAG,KAAK;AAAA,MAC/B;AAAA,IACF;AAEO,WAAA;AAAA,EACT;AAAA;AAAA,EAGQ,cAAc,GAAqB;AACrC,QAAA,OAAO,eAAe,EAAE;AAEpB,YAAA,KAAK,aAAa,CAAC;AACrB,UAAA,IAAiD,IAAI;AACzD,MAAA;AAEF,QAAI,aAAa,QAAQ,EAAE,UAAU,EAAE,aAAa,MAAM;AAGxD,YAAM,WAAW,KAAK,gBAAgB,EAAE,QAAQ;AAChD,YAAM,SAAS,SAAS;AACxB,YAAM,WAAW,SAAS;AAG1B,UAAI,SAAS;AACT,UAAA;AAKJ,YAAM,MAAyD,EAAE,YAAY,IAAI,kBAAkB;AACnG,YAAM,YAAY,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,GAAG;AAEjD,UAAI,SAAS,OAAO,SAAS,UAAU,QAAQ;AAC7C,sBAAc,IAAI,MAAM,SAAS,OAAO,MAAM;AAAA,MAAA,OACzC;AACS,sBAAA,IAAI,MAAM,UAAU,MAAM;AAAA,MAC1C;AAEA,eAAS,YAAY,KAAK,IAAI,EAAE,IAAI,CAAC,GAAG,MAAM,KAAK,gBAAgB,UAAU,IAAI,UAAU,MAAM,CAAC,CAAC;AAEnG,cAAQ,GACN,4BAA4B,cAC3B,UAAU,OACP,oCAAoC,OACjC;AAAA,QACC,CAAC,IAAI,MACH,GAAG,4CAA4C,eAAe;AAAA,MAAA,EAEjE,KAAK,EAAE,yCACV;AAAA,IAER;AAEE,MAAA,SAAS,QAAQ,CAAC,MAAO,QAAQ,KAAK,cAAc,CAAC,CAAE;AAEjD,YAAA;AAED,WAAA;AAAA,EACT;AACF;"}