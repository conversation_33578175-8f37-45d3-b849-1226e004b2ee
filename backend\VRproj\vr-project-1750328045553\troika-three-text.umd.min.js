/*
 Custom build of Typr.ts (https://github.com/fredli74/Typr.ts) for use in Troika text rendering.
  Original MIT license applies: https://github.com/fredli74/Typr.ts/blob/master/LICENSE
 Custom bundle of woff2otf (https://github.com/arty-name/woff2otf) with fflate
  (https://github.com/101arrowz/fflate) for use in Troika text rendering. 
  Original licenses apply: 
  - fflate: https://github.com/101arrowz/fflate/blob/master/LICENSE (MIT)
  - woff2otf.js: https://github.com/arty-name/woff2otf/blob/master/woff2otf.js (Apache2)
 Custom bundle of @unicode-font-resolver/client v1.0.2 (https://github.com/lojjic/unicode-font-resolver)
  for use in Troika text rendering. 
  Original MIT license applies
*/
'use strict';(function(J,z){"object"===typeof exports&&"undefined"!==typeof module?z(exports,require("three"),require("troika-worker-utils"),require("webgl-sdf-generator"),require("bidi-js"),require("troika-three-utils")):"function"===typeof define&&define.amd?define("exports three troika-worker-utils webgl-sdf-generator bidi-js troika-three-utils".split(" "),z):(J="undefined"!==typeof globalThis?globalThis:J||self,z(J.troika_three_text={},J.THREE,J.troika_worker_utils,J.webgl_sdf_generator,J.bidi_js,
J.troika_three_utils))})(this,function(J,z,ba,R,X,ja){function xa(e){return e&&"object"===typeof e&&"default"in e?e:{"default":e}}function Va(e,f,h,l,b,a,c,d,g,k,t=!0){return t?Wa(e,f,h,l,b,a,c,d,g,k).then(null,t=>{ya||(console.warn("WebGL SDF generation failed, falling back to JS",t),ya=!0);return za(e,f,h,l,b,a,c,d,g,k)}):za(e,f,h,l,b,a,c,d,g,k)}function Aa(){let e=S();for(;ka.length&&5>S()-e;)ka.shift()();pa=ka.length?setTimeout(Aa,0):0}function za(e,f,h,l,b,a,c,d,g,k){let t="TroikaTextSDFGenerator_JS_"+
Xa++%4,m=Ba[t];m||(m=Ba[t]={workerModule:ba.defineWorkerModule({name:t,workerId:t,dependencies:[Ca["default"],S],init(a,b){let c=a().javascript.generate;return function(...a){let d=b();return{textureData:c(...a),timing:b()-d}}},getTransferables(a){return[a.textureData.buffer]}}),requests:0,idleTimer:null});m.requests++;clearTimeout(m.idleTimer);return m.workerModule(e,f,h,l,b,a).then(({textureData:a,timing:b})=>{let h=S(),p=new Uint8Array(4*a.length);for(let b=0;b<a.length;b++)p[4*b+k]=a[b];la.webglUtils.renderImageData(c,
p,d,g,e,f,1<<3-k);b+=S()-h;0===--m.requests&&(m.idleTimer=setTimeout(()=>{ba.terminateWorker(t)},2E3));return{timing:b}})}function Y(){return(self.performance||Date).now()}function qa(e,f){Da=!0;e=Ea({},e);let h=Y();var {defaultFontURL:l}=Q,b=[];l&&b.push({label:"default",src:Fa(l)});e.font&&b.push({label:"user",src:Fa(e.font)});e.font=b;e.text=""+e.text;e.sdfGlyphSize=e.sdfGlyphSize||Q.sdfGlyphSize;e.unicodeFontsURL=e.unicodeFontsURL||Q.unicodeFontsURL;if(null!=e.colorRanges){l={};for(var a in e.colorRanges)e.colorRanges.hasOwnProperty(a)&&
(b=e.colorRanges[a],"number"!==typeof b&&(b=Ya.set(b).getHex()),l[a]=b);e.colorRanges=l}Object.freeze(e);let {textureWidth:c,sdfExponent:d}=Q,{sdfGlyphSize:g}=e,k=c/g*4,t=ma[g];t||(a=document.createElement("canvas"),a.width=c,a.height=256*g/k,t=ma[g]={glyphCount:0,sdfGlyphSize:g,sdfCanvas:a,sdfTexture:new z.Texture(a,void 0,void 0,void 0,z.LinearFilter,z.LinearFilter),contextLost:!1,glyphsByFont:new Map},t.sdfTexture.generateMipmaps=!1,Za(t));let {sdfTexture:m,sdfCanvas:p}=t;(Q.useWorker?Ga:$a)(e).then(a=>
{let {glyphIds:b,glyphFontIndices:l,fontData:y,glyphPositions:r,fontSize:v,timings:w}=a,u=[],G=new Float32Array(4*b.length),L=0,C=0;var A=Y();let H=y.map(a=>{let b=t.glyphsByFont.get(a.src);b||t.glyphsByFont.set(a.src,b=new Map);return b});b.forEach((c,d)=>{var f=l[d];let {src:k,unitsPerEm:e}=y[f];var m=H[f].get(c);if(!m){let {path:b,pathBounds:d}=a.glyphData[k][c];m=Math.max(d[2]-d[0],d[3]-d[1])/g*(Q.sdfMargin*g+.5);var h=t.glyphCount++;H[f].set(c,m={path:b,atlasIndex:h,sdfViewBox:[d[0]-m,d[1]-m,
d[2]+m,d[3]+m]});u.push(m)}({sdfViewBox:c}=m);f=r[C++];h=r[C++];let p=v/e;G[L++]=f+c[0]*p;G[L++]=h+c[1]*p;G[L++]=f+c[2]*p;G[L++]=h+c[3]*p;b[d]=m.atlasIndex});w.quads=(w.quads||0)+(Y()-A);let B=Y();w.sdf={};A=p.height;let Z=Math.pow(2,Math.ceil(Math.log2(Math.ceil(t.glyphCount/k)*g)));Z>A&&(console.info(`Increasing SDF texture size ${A}->${Z}`),ab(p,c,Z),m.dispose());Promise.all(u.map(a=>Ha(a,t,e.gpuAccelerateSDF).then(({timing:b})=>{w.sdf[a.atlasIndex]=b}))).then(()=>{u.length&&!t.contextLost&&(Ia(t),
m.needsUpdate=!0);w.sdfTotal=Y()-B;w.total=Y()-h;f(Object.freeze({parameters:e,sdfTexture:m,sdfGlyphSize:g,sdfExponent:d,glyphBounds:G,glyphAtlasIndices:b,glyphColors:a.glyphColors,caretPositions:a.caretPositions,chunkedBounds:a.chunkedBounds,ascender:a.ascender,descender:a.descender,lineHeight:a.lineHeight,capHeight:a.capHeight,xHeight:a.xHeight,topBaseline:a.topBaseline,blockBounds:a.blockBounds,visibleBounds:a.visibleBounds,timings:a.timings}))})});Promise.resolve().then(()=>{t.contextLost||p._warm||
(la.webgl.isSupported(p),p._warm=!0)})}function Ha({path:e,atlasIndex:f,sdfViewBox:h},{sdfGlyphSize:l,sdfCanvas:b,contextLost:a},c){if(a)return Promise.resolve({timing:-1});let {textureWidth:d,sdfExponent:g}=Q;a=Math.floor(f/4);return Va(l,l,e,h,Math.max(h[2]-h[0],h[3]-h[1]),g,b,a%(d/l)*l,Math.floor(a/(d/l))*l,f%4,c)}function Za(e){let f=e.sdfCanvas;f.addEventListener("webglcontextlost",f=>{console.log("Context Lost",f);f.preventDefault();e.contextLost=!0});f.addEventListener("webglcontextrestored",
f=>{console.log("Context Restored",f);e.contextLost=!1;let h=[];e.glyphsByFont.forEach(b=>{b.forEach(a=>{h.push(Ha(a,e,!0))})});Promise.all(h).then(()=>{Ia(e);e.sdfTexture.needsUpdate=!0})})}function Ea(e,f){for(let h in f)f.hasOwnProperty(h)&&(e[h]=f[h]);return e}function Fa(e){na||(na="undefined"===typeof document?{}:document.createElement("a"));na.href=e;return na.href}function Ia(e){if("function"!==typeof createImageBitmap){console.info("Safari<15: applying SDF canvas workaround");let {sdfCanvas:f,
sdfTexture:h}=e,{width:l,height:b}=f;e=e.sdfCanvas.getContext("webgl");let a=h.image.data;a&&a.length===l*b*4||(a=new Uint8Array(l*b*4),h.image={width:l,height:b,data:a},h.flipY=!1,h.isDataTexture=!0);e.readPixels(0,0,l,b,e.RGBA,e.UNSIGNED_BYTE,a)}}function bb(e){let f=Ja[e];f||(f=Ja[e]=(new z.PlaneGeometry(1,1,e,e)).translate(.5,.5,0));return f}function ra(e){e=ja.createDerivedMaterial(e,{chained:!0,extensions:{derivatives:!0},uniforms:{uTroikaSDFTexture:{value:null},uTroikaSDFTextureSize:{value:new z.Vector2},
uTroikaSDFGlyphSize:{value:0},uTroikaSDFExponent:{value:0},uTroikaTotalBounds:{value:new z.Vector4(0,0,0,0)},uTroikaClipRect:{value:new z.Vector4(0,0,0,0)},uTroikaEdgeOffset:{value:0},uTroikaFillOpacity:{value:1},uTroikaPositionOffset:{value:new z.Vector2},uTroikaCurveRadius:{value:0},uTroikaBlurRadius:{value:0},uTroikaStrokeWidth:{value:0},uTroikaStrokeColor:{value:new z.Color},uTroikaStrokeOpacity:{value:1},uTroikaOrient:{value:new z.Matrix3},uTroikaUseGlyphColors:{value:!0},uTroikaSDFDebug:{value:!1}},
vertexDefs:"\nuniform vec2 uTroikaSDFTextureSize;\nuniform float uTroikaSDFGlyphSize;\nuniform vec4 uTroikaTotalBounds;\nuniform vec4 uTroikaClipRect;\nuniform mat3 uTroikaOrient;\nuniform bool uTroikaUseGlyphColors;\nuniform float uTroikaEdgeOffset;\nuniform float uTroikaBlurRadius;\nuniform vec2 uTroikaPositionOffset;\nuniform float uTroikaCurveRadius;\nattribute vec4 aTroikaGlyphBounds;\nattribute float aTroikaGlyphIndex;\nattribute vec3 aTroikaGlyphColor;\nvarying vec2 vTroikaGlyphUV;\nvarying vec4 vTroikaTextureUVBounds;\nvarying float vTroikaTextureChannel;\nvarying vec3 vTroikaGlyphColor;\nvarying vec2 vTroikaGlyphDimensions;\n",
vertexTransform:"\nvec4 bounds = aTroikaGlyphBounds;\nbounds.xz += uTroikaPositionOffset.x;\nbounds.yw -= uTroikaPositionOffset.y;\n\nvec4 outlineBounds = vec4(\n  bounds.xy - uTroikaEdgeOffset - uTroikaBlurRadius,\n  bounds.zw + uTroikaEdgeOffset + uTroikaBlurRadius\n);\nvec4 clippedBounds = vec4(\n  clamp(outlineBounds.xy, uTroikaClipRect.xy, uTroikaClipRect.zw),\n  clamp(outlineBounds.zw, uTroikaClipRect.xy, uTroikaClipRect.zw)\n);\n\nvec2 clippedXY = (mix(clippedBounds.xy, clippedBounds.zw, position.xy) - bounds.xy) / (bounds.zw - bounds.xy);\n\nposition.xy = mix(bounds.xy, bounds.zw, clippedXY);\n\nuv = (position.xy - uTroikaTotalBounds.xy) / (uTroikaTotalBounds.zw - uTroikaTotalBounds.xy);\n\nfloat rad = uTroikaCurveRadius;\nif (rad != 0.0) {\n  float angle = position.x / rad;\n  position.xz = vec2(sin(angle) * rad, rad - cos(angle) * rad);\n  normal.xz = vec2(sin(angle), cos(angle));\n}\n  \nposition = uTroikaOrient * position;\nnormal = uTroikaOrient * normal;\n\nvTroikaGlyphUV = clippedXY.xy;\nvTroikaGlyphDimensions = vec2(bounds[2] - bounds[0], bounds[3] - bounds[1]);\n\n\nfloat txCols = uTroikaSDFTextureSize.x / uTroikaSDFGlyphSize;\nvec2 txUvPerSquare = uTroikaSDFGlyphSize / uTroikaSDFTextureSize;\nvec2 txStartUV = txUvPerSquare * vec2(\n  mod(floor(aTroikaGlyphIndex / 4.0), txCols),\n  floor(floor(aTroikaGlyphIndex / 4.0) / txCols)\n);\nvTroikaTextureUVBounds = vec4(txStartUV, vec2(txStartUV) + txUvPerSquare);\nvTroikaTextureChannel = mod(aTroikaGlyphIndex, 4.0);\n",
fragmentDefs:"\nuniform sampler2D uTroikaSDFTexture;\nuniform vec2 uTroikaSDFTextureSize;\nuniform float uTroikaSDFGlyphSize;\nuniform float uTroikaSDFExponent;\nuniform float uTroikaEdgeOffset;\nuniform float uTroikaFillOpacity;\nuniform float uTroikaBlurRadius;\nuniform vec3 uTroikaStrokeColor;\nuniform float uTroikaStrokeWidth;\nuniform float uTroikaStrokeOpacity;\nuniform bool uTroikaSDFDebug;\nvarying vec2 vTroikaGlyphUV;\nvarying vec4 vTroikaTextureUVBounds;\nvarying float vTroikaTextureChannel;\nvarying vec2 vTroikaGlyphDimensions;\n\nfloat troikaSdfValueToSignedDistance(float alpha) {\n  // Inverse of exponential encoding in webgl-sdf-generator\n  \n  float maxDimension = max(vTroikaGlyphDimensions.x, vTroikaGlyphDimensions.y);\n  float absDist = (1.0 - pow(2.0 * (alpha > 0.5 ? 1.0 - alpha : alpha), 1.0 / uTroikaSDFExponent)) * maxDimension;\n  float signedDist = absDist * (alpha > 0.5 ? -1.0 : 1.0);\n  return signedDist;\n}\n\nfloat troikaGlyphUvToSdfValue(vec2 glyphUV) {\n  vec2 textureUV = mix(vTroikaTextureUVBounds.xy, vTroikaTextureUVBounds.zw, glyphUV);\n  vec4 rgba = texture2D(uTroikaSDFTexture, textureUV);\n  float ch = floor(vTroikaTextureChannel + 0.5); //NOTE: can't use round() in WebGL1\n  return ch == 0.0 ? rgba.r : ch == 1.0 ? rgba.g : ch == 2.0 ? rgba.b : rgba.a;\n}\n\nfloat troikaGlyphUvToDistance(vec2 uv) {\n  return troikaSdfValueToSignedDistance(troikaGlyphUvToSdfValue(uv));\n}\n\nfloat troikaGetAADist() {\n  \n  #if defined(GL_OES_standard_derivatives) || __VERSION__ >= 300\n  return length(fwidth(vTroikaGlyphUV * vTroikaGlyphDimensions)) * 0.5;\n  #else\n  return vTroikaGlyphDimensions.x / 64.0;\n  #endif\n}\n\nfloat troikaGetFragDistValue() {\n  vec2 clampedGlyphUV = clamp(vTroikaGlyphUV, 0.5 / uTroikaSDFGlyphSize, 1.0 - 0.5 / uTroikaSDFGlyphSize);\n  float distance = troikaGlyphUvToDistance(clampedGlyphUV);\n \n  // Extrapolate distance when outside bounds:\n  distance += clampedGlyphUV == vTroikaGlyphUV ? 0.0 : \n    length((vTroikaGlyphUV - clampedGlyphUV) * vTroikaGlyphDimensions);\n\n  \n\n  return distance;\n}\n\nfloat troikaGetEdgeAlpha(float distance, float distanceOffset, float aaDist) {\n  #if defined(IS_DEPTH_MATERIAL) || defined(IS_DISTANCE_MATERIAL)\n  float alpha = step(-distanceOffset, -distance);\n  #else\n\n  float alpha = smoothstep(\n    distanceOffset + aaDist,\n    distanceOffset - aaDist,\n    distance\n  );\n  #endif\n\n  return alpha;\n}\n",
fragmentColorTransform:"\nfloat aaDist = troikaGetAADist();\nfloat fragDistance = troikaGetFragDistValue();\nfloat edgeAlpha = uTroikaSDFDebug ?\n  troikaGlyphUvToSdfValue(vTroikaGlyphUV) :\n  troikaGetEdgeAlpha(fragDistance, uTroikaEdgeOffset, max(aaDist, uTroikaBlurRadius));\n\n#if !defined(IS_DEPTH_MATERIAL) && !defined(IS_DISTANCE_MATERIAL)\nvec4 fillRGBA = gl_FragColor;\nfillRGBA.a *= uTroikaFillOpacity;\nvec4 strokeRGBA = uTroikaStrokeWidth == 0.0 ? fillRGBA : vec4(uTroikaStrokeColor, uTroikaStrokeOpacity);\nif (fillRGBA.a == 0.0) fillRGBA.rgb = strokeRGBA.rgb;\ngl_FragColor = mix(fillRGBA, strokeRGBA, smoothstep(\n  -uTroikaStrokeWidth - aaDist,\n  -uTroikaStrokeWidth + aaDist,\n  fragDistance\n));\ngl_FragColor.a *= edgeAlpha;\n#endif\n\nif (edgeAlpha == 0.0) {\n  discard;\n}\n",
customRewriter({vertexShader:f,fragmentShader:e}){let h=/\buniform\s+vec3\s+diffuse\b/;h.test(e)&&(e=e.replace(h,"varying vec3 vTroikaGlyphColor").replace(/\bdiffuse\b/g,"vTroikaGlyphColor"),h.test(f)||(f=f.replace(ja.voidMainRegExp,"uniform vec3 diffuse;\n$&\nvTroikaGlyphColor = uTroikaUseGlyphColors ? aTroikaGlyphColor / 255.0 : diffuse;\n")));return{vertexShader:f,fragmentShader:e}}});e.transparent=!0;e.forceSinglePass=!0;Object.defineProperties(e,{isTroikaTextMaterial:{value:!0},shadowSide:{get(){return this.side},
set(){}}});return e}function Ka(e){return Array.isArray(e)?e[0]:e}function sa(e,f){let h=new e.constructor(f);h.set(e.subarray(0,f));return h}function cb(e){let f=ja.createDerivedMaterial(e,{chained:!0,uniforms:{uTroikaMatricesTextureSize:{value:new z.Vector2},uTroikaMatricesTexture:{value:null}},vertexDefs:`
      uniform highp sampler2D ${"uTroikaMatricesTexture"};
      uniform vec2 ${"uTroikaMatricesTextureSize"};
      attribute float ${"aTroikaTextBatchMemberIndex"};

      vec4 troikaBatchTexel(float offset) {
        offset += ${"aTroikaTextBatchMemberIndex"} * ${(32).toFixed(1)} / 4.0;
        float w = ${"uTroikaMatricesTextureSize"}.x;
        vec2 uv = (vec2(mod(offset, w), floor(offset / w)) + 0.5) / ${"uTroikaMatricesTextureSize"};
        return texture2D(${"uTroikaMatricesTexture"}, uv);
      }
    `,vertexTransform:"\n      mat4 matrix = mat4(\n        troikaBatchTexel(0.0),\n        troikaBatchTexel(1.0),\n        troikaBatchTexel(2.0),\n        troikaBatchTexel(3.0)\n      );\n      position.xyz = (matrix * vec4(position, 1.0)).xyz;\n    "});f=ra(f);f=ja.createDerivedMaterial(f,{chained:!0,uniforms:{uTroikaIsOutline:{value:!1}},customRewriter(f){"uTroikaTotalBounds uTroikaClipRect uTroikaPositionOffset uTroikaEdgeOffset uTroikaBlurRadius uTroikaStrokeWidth uTroikaStrokeColor uTroikaStrokeOpacity uTroikaFillOpacity uTroikaCurveRadius diffuse".split(" ").forEach(e=>
{f=db(f,e)});return f},vertexDefs:"\n      uniform bool uTroikaIsOutline;\n      vec3 troikaFloatToColor(float v) {\n        return mod(floor(vec3(v / 65536.0, v / 256.0, v)), 256.0) / 256.0;\n      }\n    ",vertexTransform:"\n      uTroikaTotalBounds = troikaBatchTexel(4.0);\n      uTroikaClipRect = troikaBatchTexel(5.0);\n      \n      vec4 data = troikaBatchTexel(6.0);\n      diffuse = troikaFloatToColor(data.x);\n      uTroikaFillOpacity = data.y;\n      uTroikaCurveRadius = data.z;\n      \n      data = troikaBatchTexel(7.0);\n      if (uTroikaIsOutline) {\n        if (data == vec4(0.0)) { // degenerate if zero outline\n          position = vec3(0.0);\n        } else {\n          uTroikaPositionOffset = data.xy;\n          uTroikaEdgeOffset = data.z;\n          uTroikaBlurRadius = data.w;\n        }\n      } else {\n        uTroikaStrokeWidth = data.x;\n        uTroikaStrokeColor = troikaFloatToColor(data.y);\n        uTroikaStrokeOpacity = data.z;\n      }\n    "});
f.setMatrixTexture=e=>{f.uniforms.uTroikaMatricesTexture.value=e;f.uniforms.uTroikaMatricesTextureSize.value.set(e.image.width,e.image.height)};return f}function db({vertexShader:e,fragmentShader:f},h,l=h){h=new RegExp(`uniform\\s+(bool|float|vec[234]|mat[34])\\s+${h}\\b`);let b,a=!1;f=f.replace(h,(c,f)=>{a=!0;return`varying ${b=f} ${l}`});let c=!1;e=e.replace(h,(d,f)=>{c=!0;return`${a?"varying":""} ${b=f} ${l}`});c||(e=`${a?"varying":""} ${b} ${l};\n${e}`);return{vertexShader:e,fragmentShader:f}}
function eb(e){let f=La.get(e);if(!f){f=[];let {caretPositions:h}=e,l,b=(a,b,g,k)=>{(!l||g<(l.top+l.bottom)/2)&&f.push(l={bottom:b,top:g,carets:[]});g>l.top&&(l.top=g);b<l.bottom&&(l.bottom=b);l.carets.push({x:a,y:b,height:g-b,charIndex:k})},a=0;for(;a<h.length;a+=4)b(h[a],h[a+2],h[a+3],a/4);b(h[a-3],h[a-2],h[a-1],a/4)}La.set(e,f);return f}var Ca=xa(R);X=xa(X);R=ba.defineWorkerModule({name:"Typr Font Parser",dependencies:[function(){return"undefined"==typeof window&&(self.window=self),function(e){var f=
{parse:function(b){var a=f._bin;b=new Uint8Array(b);if("ttcf"==a.readASCII(b,0,4)){var c=4;a.readUshort(b,c);c+=2;a.readUshort(b,c);c+=2;var d=a.readUint(b,c);c+=4;for(var g=[],k=0;k<d;k++){var t=a.readUint(b,c);c+=4;g.push(f._readFont(b,t))}return g}return[f._readFont(b,0)]},_readFont:function(b,a){var c=f._bin,d=a;c.readFixed(b,a);a+=4;var g=c.readUshort(b,a);a+=2;c.readUshort(b,a);a+=2;c.readUshort(b,a);a+=2;c.readUshort(b,a);a+=2;var k="cmap;head;hhea;maxp;hmtx;name;OS/2;post;loca;glyf;kern;CFF ;GDEF;GPOS;GSUB;SVG ".split(";");
d={_data:b,_offset:d};for(var t={},e=0;e<g;e++){var p=c.readASCII(b,a,4);a+=4;c.readUint(b,a);a+=4;var h=c.readUint(b,a);a+=4;var l=c.readUint(b,a);a+=4;t[p]={offset:h,length:l}}for(e=0;e<k.length;e++)a=k[e],t[a]&&(d[a.trim()]=f[a.trim()].parse(b,t[a].offset,t[a].length,d));return d},_tabOffset:function(b,a,c){var d=f._bin,g=d.readUshort(b,c+4);c+=12;for(var k=0;k<g;k++){var t=d.readASCII(b,c,4);c+=4;d.readUint(b,c);c+=4;var e=d.readUint(b,c);if(c+=4,d.readUint(b,c),c+=4,t==a)return e}return 0}};
f._bin={readFixed:function(b,a){return(b[a]<<8|b[a+1])+(b[a+2]<<8|b[a+3])/65540},readF2dot14:function(b,a){return f._bin.readShort(b,a)/16384},readInt:function(b,a){return f._bin._view(b).getInt32(a)},readInt8:function(b,a){return f._bin._view(b).getInt8(a)},readShort:function(b,a){return f._bin._view(b).getInt16(a)},readUshort:function(b,a){return f._bin._view(b).getUint16(a)},readUshorts:function(b,a,c){for(var d=[],g=0;g<c;g++)d.push(f._bin.readUshort(b,a+2*g));return d},readUint:function(b,a){return f._bin._view(b).getUint32(a)},
readUint64:function(b,a){return 4294967296*f._bin.readUint(b,a)+f._bin.readUint(b,a+4)},readASCII:function(b,a,c){for(var d="",f=0;f<c;f++)d+=String.fromCharCode(b[a+f]);return d},readUnicode:function(b,a,c){for(var d="",f=0;f<c;f++){var k=b[a++]<<8|b[a++];d+=String.fromCharCode(k)}return d},_tdec:"undefined"!=typeof window&&window.TextDecoder?new window.TextDecoder:null,readUTF8:function(b,a,c){var d=f._bin._tdec;return d&&0==a&&c==b.length?d.decode(b):f._bin.readASCII(b,a,c)},readBytes:function(b,
a,c){for(var d=[],f=0;f<c;f++)d.push(b[a+f]);return d},readASCIIArray:function(b,a,c){for(var d=[],f=0;f<c;f++)d.push(String.fromCharCode(b[a+f]));return d},_view:function(b){return b._dataView||(b._dataView=b.buffer?new DataView(b.buffer,b.byteOffset,b.byteLength):new DataView((new Uint8Array(b)).buffer))}};f._lctf={};f._lctf.parse=function(b,a,c,d,g){var k=f._bin;c={};d=a;k.readFixed(b,a);a+=4;var t=k.readUshort(b,a);a+=2;var e=k.readUshort(b,a);a=k.readUshort(b,a+2);return c.scriptList=f._lctf.readScriptList(b,
d+t),c.featureList=f._lctf.readFeatureList(b,d+e),c.lookupList=f._lctf.readLookupList(b,d+a,g),c};f._lctf.readLookupList=function(b,a,c){var d=f._bin,g=a,k=[],t=d.readUshort(b,a);a+=2;for(var e=0;e<t;e++){var p=d.readUshort(b,a);a+=2;p=f._lctf.readLookupTable(b,g+p,c);k.push(p)}return k};f._lctf.readLookupTable=function(b,a,c){var d=f._bin,g=a,k={tabs:[]};k.ltype=d.readUshort(b,a);a+=2;k.flag=d.readUshort(b,a);a+=2;var t=d.readUshort(b,a);a+=2;for(var e=k.ltype,p=0;p<t;p++){var h=d.readUshort(b,a);
a+=2;h=c(b,e,g+h,k);k.tabs.push(h)}return k};f._lctf.numOfOnes=function(b){for(var a=0,c=0;32>c;c++)0!=(b>>>c&1)&&a++;return a};f._lctf.readClassDef=function(b,a){var c=f._bin,d=[],g=c.readUshort(b,a);if(a+=2,1==g){var k=c.readUshort(b,a);a+=2;var t=c.readUshort(b,a);a+=2;for(var e=0;e<t;e++)d.push(k+e),d.push(k+e),d.push(c.readUshort(b,a)),a+=2}if(2==g)for(g=c.readUshort(b,a),a+=2,e=0;e<g;e++)d.push(c.readUshort(b,a)),a+=2,d.push(c.readUshort(b,a)),a+=2,d.push(c.readUshort(b,a)),a+=2;return d};f._lctf.getInterval=
function(b,a){for(var c=0;c<b.length;c+=3){var d=b[c],f=b[c+1];if(b[c+2],d<=a&&a<=f)return c}return-1};f._lctf.readCoverage=function(b,a){var c=f._bin,d={};d.fmt=c.readUshort(b,a);a+=2;var g=c.readUshort(b,a);return a+=2,1==d.fmt&&(d.tab=c.readUshorts(b,a,g)),2==d.fmt&&(d.tab=c.readUshorts(b,a,3*g)),d};f._lctf.coverageIndex=function(b,a){var c=b.tab;return 1==b.fmt?c.indexOf(a):2==b.fmt&&(b=f._lctf.getInterval(c,a),-1!=b)?c[b+2]+(a-c[b]):-1};f._lctf.readFeatureList=function(b,a){var c=f._bin,d=a,
g=[],k=c.readUshort(b,a);a+=2;for(var t=0;t<k;t++){var e=c.readASCII(b,a,4);a+=4;var p=c.readUshort(b,a);a+=2;p=f._lctf.readFeatureTable(b,d+p);p.tag=e.trim();g.push(p)}return g};f._lctf.readFeatureTable=function(b,a){var c=f._bin,d=a,g={},k=c.readUshort(b,a);a+=2;0<k&&(g.featureParams=d+k);d=c.readUshort(b,a);a+=2;g.tab=[];for(k=0;k<d;k++)g.tab.push(c.readUshort(b,a+2*k));return g};f._lctf.readScriptList=function(b,a){var c=f._bin,d=a,g={},k=c.readUshort(b,a);a+=2;for(var t=0;t<k;t++){var e=c.readASCII(b,
a,4);a+=4;var p=c.readUshort(b,a);a+=2;g[e.trim()]=f._lctf.readScriptTable(b,d+p)}return g};f._lctf.readScriptTable=function(b,a){var c=f._bin,d=a,g={},k=c.readUshort(b,a);a+=2;0<k&&(g.default=f._lctf.readLangSysTable(b,d+k));k=c.readUshort(b,a);a+=2;for(var t=0;t<k;t++){var e=c.readASCII(b,a,4);a+=4;var p=c.readUshort(b,a);a+=2;g[e.trim()]=f._lctf.readLangSysTable(b,d+p)}return g};f._lctf.readLangSysTable=function(b,a){var c=f._bin,d={};c.readUshort(b,a);a+=2;d.reqFeature=c.readUshort(b,a);a+=2;
var g=c.readUshort(b,a);return a+=2,d.features=c.readUshorts(b,a,g),d};f.CFF={};f.CFF.parse=function(b,a,c){var d=f._bin;(b=new Uint8Array(b.buffer,a,c))[a=0];b[++a];b[++a];b[++a];a++;var g=[];a=f.CFF.readIndex(b,a,g);var k=[];for(c=0;c<g.length-1;c++)k.push(d.readASCII(b,a+g[c],g[c+1]-g[c]));a+=g[g.length-1];g=[];a=f.CFF.readIndex(b,a,g);k=[];for(c=0;c<g.length-1;c++)k.push(f.CFF.readDict(b,a+g[c],a+g[c+1]));a+=g[g.length-1];g=k[0];var e=[];a=f.CFF.readIndex(b,a,e);k=[];for(c=0;c<e.length-1;c++)k.push(d.readASCII(b,
a+e[c],e[c+1]-e[c]));if(a+=e[e.length-1],f.CFF.readSubrs(b,a,g),g.CharStrings){a=g.CharStrings;e=[];a=f.CFF.readIndex(b,a,e);var m=[];for(c=0;c<e.length-1;c++)m.push(d.readBytes(b,a+e[c],e[c+1]-e[c]));g.CharStrings=m}if(g.ROS){a=g.FDArray;e=[];a=f.CFF.readIndex(b,a,e);g.FDArray=[];for(c=0;c<e.length-1;c++)m=f.CFF.readDict(b,a+e[c],a+e[c+1]),f.CFF._readFDict(b,m,k),g.FDArray.push(m);a=g.FDSelect;g.FDSelect=[];c=b[a];if(a++,3!=c)throw c;e=d.readUshort(b,a);a+=2;for(c=0;c<e+1;c++)g.FDSelect.push(d.readUshort(b,
a),b[a+2]),a+=3}return g.Encoding&&(g.Encoding=f.CFF.readEncoding(b,g.Encoding,g.CharStrings.length)),g.charset&&(g.charset=f.CFF.readCharset(b,g.charset,g.CharStrings.length)),f.CFF._readFDict(b,g,k),g};f.CFF._readFDict=function(b,a,c){var d,g;for(g in a.Private&&(d=a.Private[1],a.Private=f.CFF.readDict(b,d,d+a.Private[0]),a.Private.Subrs&&f.CFF.readSubrs(b,d+a.Private.Subrs,a.Private)),a)-1!="FamilyName FontName FullName Notice version Copyright".split(" ").indexOf(g)&&(a[g]=c[a[g]-426+35])};f.CFF.readSubrs=
function(b,a,c){var d=f._bin,g=[];a=f.CFF.readIndex(b,a,g);var k=g.length;c.Bias=1240>k?107:33900>k?1131:32768;c.Subrs=[];for(k=0;k<g.length-1;k++)c.Subrs.push(d.readBytes(b,a+g[k],g[k+1]-g[k]))};f.CFF.tableSE=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,
80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,0,111,112,113,114,0,115,116,117,118,119,120,121,122,0,123,0,124,125,126,127,128,129,130,131,0,132,133,0,134,135,136,137,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,138,0,139,0,0,0,0,140,141,142,143,0,0,0,0,0,144,0,0,0,145,0,0,146,147,148,149,0,0,0,0];f.CFF.glyphByUnicode=function(b,a){for(var c=0;c<b.charset.length;c++)if(b.charset[c]==a)return c;
return-1};f.CFF.glyphBySE=function(b,a){return 0>a||255<a?-1:f.CFF.glyphByUnicode(b,f.CFF.tableSE[a])};f.CFF.readEncoding=function(b,a,c){f._bin;c=[".notdef"];var d=b[a];if(a++,0!=d)throw"error: unknown encoding format: "+d;d=b[a];a++;for(var g=0;g<d;g++)c.push(b[a+g]);return c};f.CFF.readCharset=function(b,a,c){var d=f._bin,g=[".notdef"],k=b[a];if(a++,0==k)for(var e=0;e<c;e++){var m=d.readUshort(b,a);a+=2;g.push(m)}else{if(1!=k&&2!=k)throw"error: format: "+k;for(;g.length<c;){m=d.readUshort(b,a);
a+=2;var p=0;1==k?(p=b[a],a++):(p=d.readUshort(b,a),a+=2);for(e=0;e<=p;e++)g.push(m),m++}}return g};f.CFF.readIndex=function(b,a,c){var d=f._bin,g=d.readUshort(b,a)+1,k=b[a+=2];if(a++,1==k)for(var e=0;e<g;e++)c.push(b[a+e]);else if(2==k)for(e=0;e<g;e++)c.push(d.readUshort(b,a+2*e));else if(3==k)for(e=0;e<g;e++)c.push(16777215&d.readUint(b,a+3*e-1));else if(1!=g)throw"unsupported offset size: "+k+", count: "+g;return a+g*k-1};f.CFF.getCharString=function(b,a,c){var d=f._bin,g=b[a],k=b[a+1];b[a+2];
b[a+3];b[a+4];var e=1,m=null,p=null;20>=g&&(m=g,e=1);12==g&&(m=100*g+k,e=2);21<=g&&27>=g&&(m=g,e=1);28==g&&(p=d.readShort(b,a+1),e=3);29<=g&&31>=g&&(m=g,e=1);32<=g&&246>=g&&(p=g-139,e=1);247<=g&&250>=g&&(p=256*(g-247)+k+108,e=2);251<=g&&254>=g&&(p=256*-(g-251)-k-108,e=2);255==g&&(p=d.readInt(b,a+1)/65535,e=5);c.val=null!=p?p:"o"+m;c.size=e};f.CFF.readCharString=function(b,a,c){c=a+c;for(var d=f._bin,g=[];a<c;){var k=b[a],e=b[a+1];b[a+2];b[a+3];b[a+4];var m=1,p=null,h=null;20>=k&&(p=k,m=1);12==k&&
(p=100*k+e,m=2);19!=k&&20!=k||(p=k,m=2);21<=k&&27>=k&&(p=k,m=1);28==k&&(h=d.readShort(b,a+1),m=3);29<=k&&31>=k&&(p=k,m=1);32<=k&&246>=k&&(h=k-139,m=1);247<=k&&250>=k&&(h=256*(k-247)+e+108,m=2);251<=k&&254>=k&&(h=256*-(k-251)-e-108,m=2);255==k&&(h=d.readInt(b,a+1)/65535,m=5);g.push(null!=h?h:"o"+p);a+=m}return g};f.CFF.readDict=function(b,a,c){for(var d=f._bin,g={},k=[];a<c;){var e=b[a],m=b[a+1];b[a+2];b[a+3];b[a+4];var h=1,l=null,x=null;if(28==e&&(x=d.readShort(b,a+1),h=3),29==e&&(x=d.readInt(b,a+
1),h=5),32<=e&&246>=e&&(x=e-139,h=1),247<=e&&250>=e&&(x=256*(e-247)+m+108,h=2),251<=e&&254>=e&&(x=256*-(e-251)-m-108,h=2),255==e)throw d.readInt(b,a+1),"unknown number";if(30==e){x=[];for(h=1;;){var q=b[a+h];h++;var n=q>>4;q&=15;if(15!=n&&x.push(n),15!=q&&x.push(q),15==q)break}n="";q=[0,1,2,3,4,5,6,7,8,9,".","e","e-","reserved","-","endOfNumber"];for(var r=0;r<x.length;r++)n+=q[x[r]];x=parseFloat(n)}21>=e&&(l="version Notice FullName FamilyName Weight FontBBox BlueValues OtherBlues FamilyBlues FamilyOtherBlues StdHW StdVW escape UniqueID XUID charset Encoding CharStrings Private Subrs defaultWidthX nominalWidthX".split(" ")[e],
h=1,12==e)&&(l=["Copyright","isFixedPitch","ItalicAngle","UnderlinePosition","UnderlineThickness","PaintType","CharstringType","FontMatrix","StrokeWidth","BlueScale","BlueShift","BlueFuzz","StemSnapH","StemSnapV","ForceBold",0,0,"LanguageGroup","ExpansionFactor","initialRandomSeed","SyntheticBase","PostScript","BaseFontName","BaseFontBlend",0,0,0,0,0,0,"ROS","CIDFontVersion","CIDFontRevision","CIDFontType","CIDCount","UIDBase","FDArray","FDSelect","FontName"][m],h=2);null!=l?(g[l]=1==k.length?k[0]:
k,k=[]):k.push(x);a+=h}return g};f.cmap={};f.cmap.parse=function(b,a,c){b=new Uint8Array(b.buffer,a,c);a=0;c=f._bin;var d={};c.readUshort(b,a);a+=2;var g=c.readUshort(b,a);a+=2;var k=[];d.tables=[];for(var e=0;e<g;e++){var m=c.readUshort(b,a);a+=2;var h=c.readUshort(b,a);a+=2;var l=c.readUint(b,a);a+=4;var x="p"+m+"e"+h,q=k.indexOf(l);if(-1==q){var n;q=d.tables.length;k.push(l);var r=c.readUshort(b,l);0==r?n=f.cmap.parse0(b,l):4==r?n=f.cmap.parse4(b,l):6==r?n=f.cmap.parse6(b,l):12==r?n=f.cmap.parse12(b,
l):console.debug("unknown format: "+r,m,h,l);d.tables.push(n)}if(null!=d[x])throw"multiple tables for one platform+encoding";d[x]=q}return d};f.cmap.parse0=function(b,a){var c=f._bin,d={};d.format=c.readUshort(b,a);a+=2;var g=c.readUshort(b,a);a+=2;c.readUshort(b,a);a+=2;d.map=[];for(c=0;c<g-6;c++)d.map.push(b[a+c]);return d};f.cmap.parse4=function(b,a){var c=f._bin,d=a,g={};g.format=c.readUshort(b,a);a+=2;var k=c.readUshort(b,a);a+=2;c.readUshort(b,a);a+=2;var e=c.readUshort(b,a);a+=2;e/=2;g.searchRange=
c.readUshort(b,a);a+=2;g.entrySelector=c.readUshort(b,a);a+=2;g.rangeShift=c.readUshort(b,a);a+=2;g.endCount=c.readUshorts(b,a,e);a=a+2*e+2;g.startCount=c.readUshorts(b,a,e);a+=2*e;g.idDelta=[];for(var m=0;m<e;m++)g.idDelta.push(c.readShort(b,a)),a+=2;g.idRangeOffset=c.readUshorts(b,a,e);a+=2*e;for(g.glyphIdArray=[];a<d+k;)g.glyphIdArray.push(c.readUshort(b,a)),a+=2;return g};f.cmap.parse6=function(b,a){var c=f._bin,d={};d.format=c.readUshort(b,a);a+=2;c.readUshort(b,a);a+=2;c.readUshort(b,a);a+=
2;d.firstCode=c.readUshort(b,a);a+=2;var g=c.readUshort(b,a);a+=2;d.glyphIdArray=[];for(var k=0;k<g;k++)d.glyphIdArray.push(c.readUshort(b,a)),a+=2;return d};f.cmap.parse12=function(b,a){var c=f._bin,d={};d.format=c.readUshort(b,a);a=a+2+2;c.readUint(b,a);a+=4;c.readUint(b,a);a+=4;var g=c.readUint(b,a);a+=4;d.groups=[];for(var k=0;k<g;k++){var e=a+12*k,m=c.readUint(b,e+0),h=c.readUint(b,e+4);e=c.readUint(b,e+8);d.groups.push([m,h,e])}return d};f.glyf={};f.glyf.parse=function(b,a,c,d){b=[];for(a=0;a<
d.maxp.numGlyphs;a++)b.push(null);return b};f.glyf._parseGlyf=function(b,a){var c=f._bin,d=b._data,g=f._tabOffset(d,"glyf",b._offset)+b.loca[a];if(b.loca[a]==b.loca[a+1])return null;b={};if(b.noc=c.readShort(d,g),g+=2,b.xMin=c.readShort(d,g),g+=2,b.yMin=c.readShort(d,g),g+=2,b.xMax=c.readShort(d,g),g+=2,b.yMax=c.readShort(d,g),g+=2,b.xMin>=b.xMax||b.yMin>=b.yMax)return null;if(0<b.noc){b.endPts=[];for(a=0;a<b.noc;a++)b.endPts.push(c.readUshort(d,g)),g+=2;a=c.readUshort(d,g);if(g+=2,d.length-g<a)return null;
b.instructions=c.readBytes(d,g,a);g+=a;var k=b.endPts[b.noc-1]+1;b.flags=[];for(a=0;a<k;a++){var e=d[g];if(g++,b.flags.push(e),0!=(8&e)){var m=d[g];g++;for(var h=0;h<m;h++)b.flags.push(e),a++}}b.xs=[];for(a=0;a<k;a++)e=0!=(2&b.flags[a]),m=0!=(16&b.flags[a]),e?(b.xs.push(m?d[g]:-d[g]),g++):m?b.xs.push(0):(b.xs.push(c.readShort(d,g)),g+=2);b.ys=[];for(a=0;a<k;a++)e=0!=(4&b.flags[a]),m=0!=(32&b.flags[a]),e?(b.ys.push(m?d[g]:-d[g]),g++):m?b.ys.push(0):(b.ys.push(c.readShort(d,g)),g+=2);for(a=g=d=0;a<
k;a++)d+=b.xs[a],g+=b.ys[a],b.xs[a]=d,b.ys[a]=g}else{b.parts=[];do a=c.readUshort(d,g),g+=2,k={m:{a:1,b:0,c:0,d:1,tx:0,ty:0},p1:-1,p2:-1},(b.parts.push(k),k.glyphIndex=c.readUshort(d,g),g+=2,1&a)?(e=c.readShort(d,g),g+=2,m=c.readShort(d,g),g+=2):(e=c.readInt8(d,g),g++,m=c.readInt8(d,g),g++),2&a?(k.m.tx=e,k.m.ty=m):(k.p1=e,k.p2=m),8&a?(k.m.a=k.m.d=c.readF2dot14(d,g),g+=2):64&a?(k.m.a=c.readF2dot14(d,g),g+=2,k.m.d=c.readF2dot14(d,g),g+=2):128&a&&(k.m.a=c.readF2dot14(d,g),g+=2,k.m.b=c.readF2dot14(d,
g),g+=2,k.m.c=c.readF2dot14(d,g),g+=2,k.m.d=c.readF2dot14(d,g),g+=2);while(32&a);if(256&a)for(c=c.readUshort(d,g),g+=2,b.instr=[],a=0;a<c;a++)b.instr.push(d[g]),g++}return b};f.GDEF={};f.GDEF.parse=function(b,a,c,d){c=f._bin.readUshort(b,a+4);return{glyphClassDef:0===c?null:f._lctf.readClassDef(b,a+c)}};f.GPOS={};f.GPOS.parse=function(b,a,c,d){return f._lctf.parse(b,a,c,d,f.GPOS.subt)};f.GPOS.subt=function(b,a,c,d){var g=f._bin,k=c,e={};if(e.fmt=g.readUshort(b,c),c+=2,1==a||2==a||3==a||7==a||8==a&&
2>=e.fmt){var m=g.readUshort(b,c);c+=2;e.coverage=f._lctf.readCoverage(b,m+k)}if(1==a&&1==e.fmt)d=g.readUshort(b,c),0!=d&&(e.pos=f.GPOS.readValueRecord(b,c+2,d));else if(2==a&&1<=e.fmt&&2>=e.fmt){d=g.readUshort(b,c);c+=2;a=g.readUshort(b,c);c+=2;m=f._lctf.numOfOnes(d);var h=f._lctf.numOfOnes(a);if(1==e.fmt){e.pairsets=[];var l=g.readUshort(b,c);c+=2;for(var x=0;x<l;x++){var q=k+g.readUshort(b,c);c+=2;var n=g.readUshort(b,q);q+=2;for(var r=[],v=0;v<n;v++){var w=g.readUshort(b,q);q+=2;0!=d&&(u=f.GPOS.readValueRecord(b,
q,d),q+=2*m);0!=a&&(G=f.GPOS.readValueRecord(b,q,a),q+=2*h);r.push({gid2:w,val1:u,val2:G})}e.pairsets.push(r)}}if(2==e.fmt)for(u=g.readUshort(b,c),c+=2,G=g.readUshort(b,c),c+=2,l=g.readUshort(b,c),c+=2,g=g.readUshort(b,c),c+=2,e.classDef1=f._lctf.readClassDef(b,k+u),e.classDef2=f._lctf.readClassDef(b,k+G),e.matrix=[],x=0;x<l;x++){k=[];for(v=0;v<g;v++){var u=null,G=null;0!=d&&(u=f.GPOS.readValueRecord(b,c,d),c+=2*m);0!=a&&(G=f.GPOS.readValueRecord(b,c,a),c+=2*h);k.push({val1:u,val2:G})}e.matrix.push(k)}}else if(4==
a&&1==e.fmt)e.markCoverage=f._lctf.readCoverage(b,g.readUshort(b,c)+k),e.baseCoverage=f._lctf.readCoverage(b,g.readUshort(b,c+2)+k),e.markClassCount=g.readUshort(b,c+4),e.markArray=f.GPOS.readMarkArray(b,g.readUshort(b,c+6)+k),e.baseArray=f.GPOS.readBaseArray(b,g.readUshort(b,c+8)+k,e.markClassCount);else if(6==a&&1==e.fmt)e.mark1Coverage=f._lctf.readCoverage(b,g.readUshort(b,c)+k),e.mark2Coverage=f._lctf.readCoverage(b,g.readUshort(b,c+2)+k),e.markClassCount=g.readUshort(b,c+4),e.mark1Array=f.GPOS.readMarkArray(b,
g.readUshort(b,c+6)+k),e.mark2Array=f.GPOS.readBaseArray(b,g.readUshort(b,c+8)+k,e.markClassCount);else{if(9==a&&1==e.fmt){e=g.readUshort(b,c);c=g.readUint(b,c+2);if(9==d.ltype)d.ltype=e;else if(d.ltype!=e)throw"invalid extension substitution";return f.GPOS.subt(b,d.ltype,k+c)}console.debug("unsupported GPOS table LookupType",a,"format",e.fmt)}return e};f.GPOS.readValueRecord=function(b,a,c){var d=f._bin,e=[];return e.push(1&c?d.readShort(b,a):0),a+=1&c?2:0,e.push(2&c?d.readShort(b,a):0),a+=2&c?2:
0,e.push(4&c?d.readShort(b,a):0),a+=4&c?2:0,e.push(8&c?d.readShort(b,a):0),e};f.GPOS.readBaseArray=function(b,a,c){var d=f._bin,e=[],k=a,h=d.readUshort(b,a);a+=2;for(var m=0;m<h;m++){for(var p=[],l=0;l<c;l++)p.push(f.GPOS.readAnchorRecord(b,k+d.readUshort(b,a))),a+=2;e.push(p)}return e};f.GPOS.readMarkArray=function(b,a){var c=f._bin,d=[],e=a,k=c.readUshort(b,a);a+=2;for(var h=0;h<k;h++){var m=f.GPOS.readAnchorRecord(b,c.readUshort(b,a+2)+e);m.markClass=c.readUshort(b,a);d.push(m);a+=4}return d};
f.GPOS.readAnchorRecord=function(b,a){var c=f._bin,d={};return d.fmt=c.readUshort(b,a),d.x=c.readShort(b,a+2),d.y=c.readShort(b,a+4),d};f.GSUB={};f.GSUB.parse=function(b,a,c,d){return f._lctf.parse(b,a,c,d,f.GSUB.subt)};f.GSUB.subt=function(b,a,c,d){var e=f._bin,k=c,h={};if(h.fmt=e.readUshort(b,c),c+=2,1!=a&&2!=a&&4!=a&&5!=a&&6!=a)return null;if(1==a||2==a||4==a||5==a&&2>=h.fmt||6==a&&2>=h.fmt){var m=e.readUshort(b,c);c+=2;h.coverage=f._lctf.readCoverage(b,k+m)}if(1==a&&1<=h.fmt&&2>=h.fmt)1==h.fmt?
h.delta=e.readShort(b,c):2==h.fmt&&(a=e.readUshort(b,c),h.newg=e.readUshorts(b,c+2,a));else if(2==a&&1==h.fmt)for(a=e.readUshort(b,c),c+=2,h.seqs=[],d=0;d<a;d++){m=e.readUshort(b,c)+k;c+=2;var p=e.readUshort(b,m);h.seqs.push(e.readUshorts(b,m+2,p))}else if(4==a)for(h.vals=[],a=e.readUshort(b,c),c+=2,d=0;d<a;d++)m=e.readUshort(b,c),c+=2,h.vals.push(f.GSUB.readLigatureSet(b,k+m));else if(5==a&&2==h.fmt){if(2==h.fmt)for(d=e.readUshort(b,c),c+=2,h.cDef=f._lctf.readClassDef(b,k+d),h.scset=[],a=e.readUshort(b,
c),c+=2,d=0;d<a;d++)m=e.readUshort(b,c),c+=2,h.scset.push(0==m?null:f.GSUB.readSubClassSet(b,k+m))}else if(6==a&&3==h.fmt){if(3==h.fmt){for(d=0;3>d;d++){a=e.readUshort(b,c);c+=2;m=[];for(p=0;p<a;p++)m.push(f._lctf.readCoverage(b,k+e.readUshort(b,c+2*p)));c+=2*a;0==d&&(h.backCvg=m);1==d&&(h.inptCvg=m);2==d&&(h.ahedCvg=m)}a=e.readUshort(b,c);h.lookupRec=f.GSUB.readSubstLookupRecords(b,c+2,a)}}else{if(7==a&&1==h.fmt){h=e.readUshort(b,c);c=e.readUint(b,c+2);if(9==d.ltype)d.ltype=h;else if(d.ltype!=h)throw"invalid extension substitution";
return f.GSUB.subt(b,d.ltype,k+c)}console.debug("unsupported GSUB table LookupType",a,"format",h.fmt)}return h};f.GSUB.readSubClassSet=function(b,a){var c=f._bin.readUshort,d=a,e=[],k=c(b,a);a+=2;for(var h=0;h<k;h++){var m=c(b,a);a+=2;e.push(f.GSUB.readSubClassRule(b,d+m))}return e};f.GSUB.readSubClassRule=function(b,a){var c=f._bin.readUshort,d={},e=c(b,a),k=c(b,a+=2);a+=2;d.input=[];for(var h=0;h<e-1;h++)d.input.push(c(b,a)),a+=2;return d.substLookupRecords=f.GSUB.readSubstLookupRecords(b,a,k),
d};f.GSUB.readSubstLookupRecords=function(b,a,c){for(var d=f._bin.readUshort,e=[],k=0;k<c;k++)e.push(d(b,a),d(b,a+2)),a+=4;return e};f.GSUB.readChainSubClassSet=function(b,a){var c=f._bin,d=a,e=[],k=c.readUshort(b,a);a+=2;for(var h=0;h<k;h++){var m=c.readUshort(b,a);a+=2;e.push(f.GSUB.readChainSubClassRule(b,d+m))}return e};f.GSUB.readChainSubClassRule=function(b,a){for(var c=f._bin,d={},e=["backtrack","input","lookahead"],k=0;k<e.length;k++){var h=c.readUshort(b,a);a+=2;1==k&&h--;d[e[k]]=c.readUshorts(b,
a,h);a+=2*d[e[k]].length}h=c.readUshort(b,a);return a+=2,d.subst=c.readUshorts(b,a,2*h),d};f.GSUB.readLigatureSet=function(b,a){var c=f._bin,d=a,e=[],k=c.readUshort(b,a);a+=2;for(var h=0;h<k;h++){var m=c.readUshort(b,a);a+=2;e.push(f.GSUB.readLigature(b,d+m))}return e};f.GSUB.readLigature=function(b,a){var c=f._bin,d={chain:[]};d.nglyph=c.readUshort(b,a);a+=2;var e=c.readUshort(b,a);a+=2;for(var k=0;k<e-1;k++)d.chain.push(c.readUshort(b,a)),a+=2;return d};f.head={};f.head.parse=function(b,a,c){c=
f._bin;var d={};return c.readFixed(b,a),a+=4,d.fontRevision=c.readFixed(b,a),a+=4,c.readUint(b,a),a+=4,c.readUint(b,a),a+=4,d.flags=c.readUshort(b,a),a+=2,d.unitsPerEm=c.readUshort(b,a),a+=2,d.created=c.readUint64(b,a),a+=8,d.modified=c.readUint64(b,a),a+=8,d.xMin=c.readShort(b,a),a+=2,d.yMin=c.readShort(b,a),a+=2,d.xMax=c.readShort(b,a),a+=2,d.yMax=c.readShort(b,a),a+=2,d.macStyle=c.readUshort(b,a),a+=2,d.lowestRecPPEM=c.readUshort(b,a),a+=2,d.fontDirectionHint=c.readShort(b,a),a+=2,d.indexToLocFormat=
c.readShort(b,a),a+=2,d.glyphDataFormat=c.readShort(b,a),d};f.hhea={};f.hhea.parse=function(b,a,c){c=f._bin;var d={};return c.readFixed(b,a),a+=4,d.ascender=c.readShort(b,a),a+=2,d.descender=c.readShort(b,a),a+=2,d.lineGap=c.readShort(b,a),a+=2,d.advanceWidthMax=c.readUshort(b,a),a+=2,d.minLeftSideBearing=c.readShort(b,a),a+=2,d.minRightSideBearing=c.readShort(b,a),a+=2,d.xMaxExtent=c.readShort(b,a),a+=2,d.caretSlopeRise=c.readShort(b,a),a+=2,d.caretSlopeRun=c.readShort(b,a),a+=2,d.caretOffset=c.readShort(b,
a),a+=2,a+=8,d.metricDataFormat=c.readShort(b,a),a+=2,d.numberOfHMetrics=c.readUshort(b,a),d};f.hmtx={};f.hmtx.parse=function(b,a,c,d){c=f._bin;for(var e={aWidth:[],lsBearing:[]},k=0,h=0,m=0;m<d.maxp.numGlyphs;m++)m<d.hhea.numberOfHMetrics&&(k=c.readUshort(b,a),a+=2,h=c.readShort(b,a),a+=2),e.aWidth.push(k),e.lsBearing.push(h);return e};f.kern={};f.kern.parse=function(b,a,c,d){var e=f._bin,k=e.readUshort(b,a);if(a+=2,1==k)return f.kern.parseV1(b,a-2,c,d);c=e.readUshort(b,a);a+=2;d={glyph1:[],rval:[]};
for(k=0;k<c;k++){a+=2;e.readUshort(b,a);a+=2;var h=e.readUshort(b,a);a+=2;h>>>=8;if(0!=(h&=15))throw"unknown kern table format: "+h;a=f.kern.readFormat0(b,a,d)}return d};f.kern.parseV1=function(b,a,c,d){c=f._bin;c.readFixed(b,a);a+=4;d=c.readUint(b,a);a+=4;for(var e={glyph1:[],rval:[]},k=0;k<d;k++){c.readUint(b,a);a+=4;var h=c.readUshort(b,a);a+=2;c.readUshort(b,a);a+=2;h>>>=8;if(0!=(h&=15))throw"unknown kern table format: "+h;a=f.kern.readFormat0(b,a,e)}return e};f.kern.readFormat0=function(b,a,
c){var d=f._bin,e=-1,k=d.readUshort(b,a);a+=2;d.readUshort(b,a);a+=2;d.readUshort(b,a);a+=2;d.readUshort(b,a);a+=2;for(var h=0;h<k;h++){var m=d.readUshort(b,a);a+=2;var p=d.readUshort(b,a);a+=2;var l=d.readShort(b,a);a+=2;m!=e&&(c.glyph1.push(m),c.rval.push({glyph2:[],vals:[]}));e=c.rval[c.rval.length-1];e.glyph2.push(p);e.vals.push(l);e=m}return a};f.loca={};f.loca.parse=function(b,a,c,d){c=f._bin;var e=[],k=d.head.indexToLocFormat;d=d.maxp.numGlyphs+1;if(0==k)for(var h=0;h<d;h++)e.push(c.readUshort(b,
a+(h<<1))<<1);if(1==k)for(h=0;h<d;h++)e.push(c.readUint(b,a+(h<<2)));return e};f.maxp={};f.maxp.parse=function(b,a,c){c=f._bin;var d={},e=c.readUint(b,a);return a+=4,d.numGlyphs=c.readUshort(b,a),a+=2,65536==e&&(d.maxPoints=c.readUshort(b,a),a+=2,d.maxContours=c.readUshort(b,a),a+=2,d.maxCompositePoints=c.readUshort(b,a),a+=2,d.maxCompositeContours=c.readUshort(b,a),a+=2,d.maxZones=c.readUshort(b,a),a+=2,d.maxTwilightPoints=c.readUshort(b,a),a+=2,d.maxStorage=c.readUshort(b,a),a+=2,d.maxFunctionDefs=
c.readUshort(b,a),a+=2,d.maxInstructionDefs=c.readUshort(b,a),a+=2,d.maxStackElements=c.readUshort(b,a),a+=2,d.maxSizeOfInstructions=c.readUshort(b,a),a+=2,d.maxComponentElements=c.readUshort(b,a),a+=2,d.maxComponentDepth=c.readUshort(b,a)),d};f.name={};f.name.parse=function(b,a,c){c=f._bin;var d={};c.readUshort(b,a);a+=2;var e=c.readUshort(b,a);a+=2;c.readUshort(b,a);for(var k,h="copyright fontFamily fontSubfamily ID fullName version postScriptName trademark manufacturer designer description urlVendor urlDesigner licence licenceURL --- typoFamilyName typoSubfamilyName compatibleFull sampleText postScriptCID wwsFamilyName wwsSubfamilyName lightPalette darkPalette".split(" "),
m=a+=2,p=0;p<e;p++){var l=c.readUshort(b,a);a+=2;var x=c.readUshort(b,a);a+=2;var q=c.readUshort(b,a);a+=2;var n=c.readUshort(b,a);a+=2;var r=c.readUshort(b,a);a+=2;var v=c.readUshort(b,a);a+=2;var w=h[n];v=m+12*e+v;if(0==l)r=c.readUnicode(b,v,r/2);else if(3==l&&0==x)r=c.readUnicode(b,v,r/2);else if(0==x)r=c.readASCII(b,v,r);else if(1==x)r=c.readUnicode(b,v,r/2);else if(3==x)r=c.readUnicode(b,v,r/2);else{if(1!=l)throw"unknown encoding "+x+", platformID: "+l;r=c.readASCII(b,v,r);console.debug("reading unknown MAC encoding "+
x+" as ASCII")}l="p"+l+","+q.toString(16);null==d[l]&&(d[l]={});d[l][void 0!==w?w:n]=r;d[l]._lang=q}for(var u in d)if(null!=d[u].postScriptName&&1033==d[u]._lang)return d[u];for(u in d)if(null!=d[u].postScriptName&&0==d[u]._lang)return d[u];for(u in d)if(null!=d[u].postScriptName&&3084==d[u]._lang)return d[u];for(u in d)if(null!=d[u].postScriptName)return d[u];for(u in d){k=u;break}return console.debug("returning name table with languageID "+d[k]._lang),d[k]};f["OS/2"]={};f["OS/2"].parse=function(b,
a,c){c=f._bin.readUshort(b,a);a+=2;var d={};if(0==c)f["OS/2"].version0(b,a,d);else if(1==c)f["OS/2"].version1(b,a,d);else if(2==c||3==c||4==c)f["OS/2"].version2(b,a,d);else{if(5!=c)throw"unknown OS/2 table version: "+c;f["OS/2"].version5(b,a,d)}return d};f["OS/2"].version0=function(b,a,c){var d=f._bin;return c.xAvgCharWidth=d.readShort(b,a),a+=2,c.usWeightClass=d.readUshort(b,a),a+=2,c.usWidthClass=d.readUshort(b,a),a+=2,c.fsType=d.readUshort(b,a),a+=2,c.ySubscriptXSize=d.readShort(b,a),a+=2,c.ySubscriptYSize=
d.readShort(b,a),a+=2,c.ySubscriptXOffset=d.readShort(b,a),a+=2,c.ySubscriptYOffset=d.readShort(b,a),a+=2,c.ySuperscriptXSize=d.readShort(b,a),a+=2,c.ySuperscriptYSize=d.readShort(b,a),a+=2,c.ySuperscriptXOffset=d.readShort(b,a),a+=2,c.ySuperscriptYOffset=d.readShort(b,a),a+=2,c.yStrikeoutSize=d.readShort(b,a),a+=2,c.yStrikeoutPosition=d.readShort(b,a),a+=2,c.sFamilyClass=d.readShort(b,a),a+=2,c.panose=d.readBytes(b,a,10),a+=10,c.ulUnicodeRange1=d.readUint(b,a),a+=4,c.ulUnicodeRange2=d.readUint(b,
a),a+=4,c.ulUnicodeRange3=d.readUint(b,a),a+=4,c.ulUnicodeRange4=d.readUint(b,a),a+=4,c.achVendID=[d.readInt8(b,a),d.readInt8(b,a+1),d.readInt8(b,a+2),d.readInt8(b,a+3)],a+=4,c.fsSelection=d.readUshort(b,a),a+=2,c.usFirstCharIndex=d.readUshort(b,a),a+=2,c.usLastCharIndex=d.readUshort(b,a),a+=2,c.sTypoAscender=d.readShort(b,a),a+=2,c.sTypoDescender=d.readShort(b,a),a+=2,c.sTypoLineGap=d.readShort(b,a),a+=2,c.usWinAscent=d.readUshort(b,a),a+=2,c.usWinDescent=d.readUshort(b,a),a+2};f["OS/2"].version1=
function(b,a,c){var d=f._bin;return a=f["OS/2"].version0(b,a,c),c.ulCodePageRange1=d.readUint(b,a),a+=4,c.ulCodePageRange2=d.readUint(b,a),a+4};f["OS/2"].version2=function(b,a,c){var d=f._bin;return a=f["OS/2"].version1(b,a,c),c.sxHeight=d.readShort(b,a),a+=2,c.sCapHeight=d.readShort(b,a),a+=2,c.usDefault=d.readUshort(b,a),a+=2,c.usBreak=d.readUshort(b,a),a+=2,c.usMaxContext=d.readUshort(b,a),a+2};f["OS/2"].version5=function(b,a,c){var d=f._bin;return a=f["OS/2"].version2(b,a,c),c.usLowerOpticalPointSize=
d.readUshort(b,a),a+=2,c.usUpperOpticalPointSize=d.readUshort(b,a),a+2};f.post={};f.post.parse=function(b,a,c){c=f._bin;var d={};return d.version=c.readFixed(b,a),a+=4,d.italicAngle=c.readFixed(b,a),a+=4,d.underlinePosition=c.readShort(b,a),a+=2,d.underlineThickness=c.readShort(b,a),d};null==f&&(f={});null==f.U&&(f.U={});f.U.codeToGlyph=function(b,a){b=b.cmap;var c=-1;if(null!=b.p0e4?c=b.p0e4:null!=b.p3e1?c=b.p3e1:null!=b.p1e0?c=b.p1e0:null!=b.p0e3&&(c=b.p0e3),-1==c)throw"no familiar platform and encoding!";
b=b.tables[c];if(0==b.format)return a>=b.map.length?0:b.map[a];if(4==b.format){var d=-1;for(c=0;c<b.endCount.length;c++)if(a<=b.endCount[c]){d=c;break}return-1==d||b.startCount[d]>a?0:65535&(0!=b.idRangeOffset[d]?b.glyphIdArray[a-b.startCount[d]+(b.idRangeOffset[d]>>1)-(b.idRangeOffset.length-d)]:a+b.idDelta[d])}if(12==b.format){if(a>b.groups[b.groups.length-1][1])return 0;for(c=0;c<b.groups.length;c++)if(d=b.groups[c],d[0]<=a&&a<=d[1])return d[2]+(a-d[0]);return 0}throw"unknown cmap table format "+
b.format;};f.U.glyphToPath=function(b,a){var c={cmds:[],crds:[]};if(b.SVG&&b.SVG.entries[a]){var d=b.SVG.entries[a];return null==d?c:("string"==typeof d&&(d=f.SVG.toPath(d),b.SVG.entries[a]=d),d)}if(b.CFF){d={x:0,y:0,stack:[],nStems:0,haveWidth:!1,width:b.CFF.Private?b.CFF.Private.defaultWidthX:0,open:!1};var e=b.CFF,k=b.CFF.Private;if(e.ROS){for(k=0;e.FDSelect[k+2]<=a;)k+=2;k=e.FDArray[e.FDSelect[k+1]].Private}f.U._drawCFF(b.CFF.CharStrings[a],d,e,k,c)}else b.glyf&&f.U._drawGlyf(a,b,c);return c};
f.U._drawGlyf=function(b,a,c){var d=a.glyf[b];null==d&&(d=a.glyf[b]=f.glyf._parseGlyf(a,b));null!=d&&(-1<d.noc?f.U._simpleGlyph(d,c):f.U._compoGlyph(d,a,c))};f.U._simpleGlyph=function(b,a){for(var c=0;c<b.noc;c++){for(var d=0==c?0:b.endPts[c-1]+1,e=b.endPts[c],k=d;k<=e;k++){var h=k==d?e:k-1,m=k==e?d:k+1,p=1&b.flags[k],l=1&b.flags[h],x=1&b.flags[m],q=b.xs[k],n=b.ys[k];if(k==d)if(p){if(!l){f.U.P.moveTo(a,q,n);continue}f.U.P.moveTo(a,b.xs[h],b.ys[h])}else l?f.U.P.moveTo(a,b.xs[h],b.ys[h]):f.U.P.moveTo(a,
(b.xs[h]+q)/2,(b.ys[h]+n)/2);p?l&&f.U.P.lineTo(a,q,n):x?f.U.P.qcurveTo(a,q,n,b.xs[m],b.ys[m]):f.U.P.qcurveTo(a,q,n,(q+b.xs[m])/2,(n+b.ys[m])/2)}f.U.P.closePath(a)}};f.U._compoGlyph=function(b,a,c){for(var d=0;d<b.parts.length;d++){var e={cmds:[],crds:[]},k=b.parts[d];f.U._drawGlyf(k.glyphIndex,a,e);k=k.m;for(var h=0;h<e.crds.length;h+=2){var m=e.crds[h],l=e.crds[h+1];c.crds.push(m*k.a+l*k.b+k.tx);c.crds.push(m*k.c+l*k.d+k.ty)}for(h=0;h<e.cmds.length;h++)c.cmds.push(e.cmds[h])}};f.U._getGlyphClass=
function(b,a){b=f._lctf.getInterval(a,b);return-1==b?0:a[b+2]};f.U._applySubs=function(b,a,c,d){for(var e=b.length-a-1,k=0;k<c.tabs.length;k++)if(null!=c.tabs[k]){var h,m=c.tabs[k];if(!m.coverage||-1!=(h=f._lctf.coverageIndex(m.coverage,b[a])))if(1==c.ltype)b[a],1==m.fmt?b[a]+=m.delta:b[a]=m.newg[h];else if(4==c.ltype)for(var l=m.vals[h],y=0;y<l.length;y++){var x=l[y];m=x.chain.length;if(!(m>e)){for(var q=!0,n=0,r=0;r<m;r++){for(;-1==b[a+n+(1+r)];)n++;x.chain[r]!=b[a+n+(1+r)]&&(q=!1)}if(q){b[a]=x.nglyph;
for(r=0;r<m+n;r++)b[a+r+1]=-1;break}}}else if(5==c.ltype&&2==m.fmt)for(l=f._lctf.getInterval(m.cDef,b[a]),n=m.scset[m.cDef[l+2]],x=0;x<n.length;x++){y=n[x];var v=y.input;if(!(v.length>e)){q=!0;for(r=0;r<v.length;r++){var w=f._lctf.getInterval(m.cDef,b[a+1+r]);if(-1==l&&m.cDef[w+2]!=v[r]){q=!1;break}}if(q)for(q=y.substLookupRecords,y=0;y<q.length;y+=2)q[y],q[y+1]}}else if(6==c.ltype&&3==m.fmt&&f.U._glsCovered(b,m.backCvg,a-m.backCvg.length)&&f.U._glsCovered(b,m.inptCvg,a)&&f.U._glsCovered(b,m.ahedCvg,
a+m.inptCvg.length))for(q=m.lookupRec,x=0;x<q.length;x+=2)l=q[x],f.U._applySubs(b,a+l,d[q[x+1]],d)}};f.U._glsCovered=function(b,a,c){for(var d=0;d<a.length;d++)if(-1==f._lctf.coverageIndex(a[d],b[c+d]))return!1;return!0};f.U.glyphsToPath=function(b,a,c){for(var d={cmds:[],crds:[]},e=0,k=0;k<a.length;k++){var h=a[k];if(-1!=h){for(var m=k<a.length-1&&-1!=a[k+1]?a[k+1]:0,l=f.U.glyphToPath(b,h),y=0;y<l.crds.length;y+=2)d.crds.push(l.crds[y]+e),d.crds.push(l.crds[y+1]);c&&d.cmds.push(c);for(y=0;y<l.cmds.length;y++)d.cmds.push(l.cmds[y]);
c&&d.cmds.push("X");e+=b.hmtx.aWidth[h];k<a.length-1&&(e+=f.U.getPairAdjustment(b,h,m))}}return d};f.U.P={};f.U.P.moveTo=function(b,a,c){b.cmds.push("M");b.crds.push(a,c)};f.U.P.lineTo=function(b,a,c){b.cmds.push("L");b.crds.push(a,c)};f.U.P.curveTo=function(b,a,c,e,f,k,h){b.cmds.push("C");b.crds.push(a,c,e,f,k,h)};f.U.P.qcurveTo=function(b,a,c,e,f){b.cmds.push("Q");b.crds.push(a,c,e,f)};f.U.P.closePath=function(b){b.cmds.push("Z")};f.U._drawCFF=function(b,a,c,e,g){for(var d=a.stack,h=a.nStems,m=
a.haveWidth,l=a.width,y=a.open,x=0,q=a.x,n=a.y,r=0,v=0,w=0,u=0,G=0,L=0,C=0,A=0,H=0,B=0,Z={val:0,size:0};x<b.length;){f.CFF.getCharString(b,x,Z);var D=Z.val;if(x+=Z.size,"o1"==D||"o18"==D)0!=d.length%2&&!m&&(l=d.shift()+e.nominalWidthX),h+=d.length>>1,d.length=0,m=!0;else if("o3"==D||"o23"==D)0!=d.length%2&&!m&&(l=d.shift()+e.nominalWidthX),h+=d.length>>1,d.length=0,m=!0;else if("o4"==D)1<d.length&&!m&&(l=d.shift()+e.nominalWidthX,m=!0),y&&f.U.P.closePath(g),n+=d.pop(),f.U.P.moveTo(g,q,n),y=!0;else if("o5"==
D)for(;0<d.length;)q+=d.shift(),n+=d.shift(),f.U.P.lineTo(g,q,n);else if("o6"==D||"o7"==D){var I=d.length,K="o6"==D;for(D=0;D<I;D++){var F=d.shift();K?q+=F:n+=F;K=!K;f.U.P.lineTo(g,q,n)}}else if("o8"==D||"o24"==D){I=d.length;for(K=0;K+6<=I;)r=q+d.shift(),v=n+d.shift(),w=r+d.shift(),u=v+d.shift(),q=w+d.shift(),n=u+d.shift(),f.U.P.curveTo(g,r,v,w,u,q,n),K+=6;"o24"==D&&(q+=d.shift(),n+=d.shift(),f.U.P.lineTo(g,q,n))}else{if("o11"==D)break;if("o1234"==D||"o1235"==D||"o1236"==D||"o1237"==D)"o1234"==D&&
(v=n,w=(r=q+d.shift())+d.shift(),B=u=v+d.shift(),L=u,A=n,q=(C=(G=(H=w+d.shift())+d.shift())+d.shift())+d.shift(),f.U.P.curveTo(g,r,v,w,u,H,B),f.U.P.curveTo(g,G,L,C,A,q,n)),"o1235"==D&&(r=q+d.shift(),v=n+d.shift(),w=r+d.shift(),u=v+d.shift(),H=w+d.shift(),B=u+d.shift(),G=H+d.shift(),L=B+d.shift(),C=G+d.shift(),A=L+d.shift(),q=C+d.shift(),n=A+d.shift(),d.shift(),f.U.P.curveTo(g,r,v,w,u,H,B),f.U.P.curveTo(g,G,L,C,A,q,n)),"o1236"==D&&(r=q+d.shift(),v=n+d.shift(),w=r+d.shift(),B=u=v+d.shift(),L=u,C=(G=
(H=w+d.shift())+d.shift())+d.shift(),A=L+d.shift(),q=C+d.shift(),f.U.P.curveTo(g,r,v,w,u,H,B),f.U.P.curveTo(g,G,L,C,A,q,n)),"o1237"==D&&(r=q+d.shift(),v=n+d.shift(),w=r+d.shift(),u=v+d.shift(),H=w+d.shift(),B=u+d.shift(),G=H+d.shift(),L=B+d.shift(),C=G+d.shift(),A=L+d.shift(),Math.abs(C-q)>Math.abs(A-n)?q=C+d.shift():n=A+d.shift(),f.U.P.curveTo(g,r,v,w,u,H,B),f.U.P.curveTo(g,G,L,C,A,q,n));else if("o14"==D){if(0<d.length&&!m&&(l=d.shift()+c.nominalWidthX,m=!0),4==d.length)I=d.shift(),K=d.shift(),F=
d.shift(),D=d.shift(),F=f.CFF.glyphBySE(c,F),D=f.CFF.glyphBySE(c,D),f.U._drawCFF(c.CharStrings[F],a,c,e,g),a.x=I,a.y=K,f.U._drawCFF(c.CharStrings[D],a,c,e,g);y&&(f.U.P.closePath(g),y=!1)}else if("o19"==D||"o20"==D)0!=d.length%2&&!m&&(l=d.shift()+e.nominalWidthX),h+=d.length>>1,d.length=0,m=!0,x+=h+7>>3;else if("o21"==D)2<d.length&&!m&&(l=d.shift()+e.nominalWidthX,m=!0),n+=d.pop(),q+=d.pop(),y&&f.U.P.closePath(g),f.U.P.moveTo(g,q,n),y=!0;else if("o22"==D)1<d.length&&!m&&(l=d.shift()+e.nominalWidthX,
m=!0),q+=d.pop(),y&&f.U.P.closePath(g),f.U.P.moveTo(g,q,n),y=!0;else if("o25"==D){for(;6<d.length;)q+=d.shift(),n+=d.shift(),f.U.P.lineTo(g,q,n);r=q+d.shift();v=n+d.shift();w=r+d.shift();u=v+d.shift();q=w+d.shift();n=u+d.shift();f.U.P.curveTo(g,r,v,w,u,q,n)}else if("o26"==D)for(d.length%2&&(q+=d.shift());0<d.length;)r=q,v=n+d.shift(),q=w=r+d.shift(),n=(u=v+d.shift())+d.shift(),f.U.P.curveTo(g,r,v,w,u,q,n);else if("o27"==D)for(d.length%2&&(n+=d.shift());0<d.length;)v=n,w=(r=q+d.shift())+d.shift(),
u=v+d.shift(),q=w+d.shift(),n=u,f.U.P.curveTo(g,r,v,w,u,q,n);else if("o10"==D||"o29"==D)I="o10"==D?e:c,0==d.length?console.debug("error: empty stack"):(K=d.pop(),I=I.Subrs[K+I.Bias],a.x=q,a.y=n,a.nStems=h,a.haveWidth=m,a.width=l,a.open=y,f.U._drawCFF(I,a,c,e,g),q=a.x,n=a.y,h=a.nStems,m=a.haveWidth,l=a.width,y=a.open);else if("o30"==D||"o31"==D)for(I=d.length,D=(K=0,"o31"==D),K+=I-(I&=-3);K<I;)D?(v=n,w=(r=q+d.shift())+d.shift(),n=(u=v+d.shift())+d.shift(),5==I-K?(q=w+d.shift(),K++):q=w,D=!1):(r=q,
v=n+d.shift(),w=r+d.shift(),u=v+d.shift(),q=w+d.shift(),5==I-K?(n=u+d.shift(),K++):n=u,D=!0),f.U.P.curveTo(g,r,v,w,u,q,n),K+=4;else{if("o"==(D+"").charAt(0))throw console.debug("Unknown operation: "+D,b),D;d.push(D)}}}a.x=q;a.y=n;a.nStems=h;a.haveWidth=m;a.width=l;a.open=y};var h=f,l={Typr:h};return e.Typr=h,e.default=l,Object.defineProperty(e,"__esModule",{value:!0}),e}({}).Typr},function(){return function(e){var f=Uint8Array,h=Uint16Array,l=Uint32Array,b=new f([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,
3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),a=new f([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),c=new f([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),d=function(a,b){for(var c=new h(31),d=0;31>d;++d)c[d]=b+=1<<a[d-1];a=new l(c[30]);for(d=1;30>d;++d)for(b=c[d];b<c[d+1];++b)a[b]=b-c[d]<<5|d;return[c,a]},g=d(b,2),k=g[0];g=g[1];k[28]=258;g[258]=28;var t=d(a,0)[0],m=new h(32768);for(d=0;32768>d;++d)g=(43690&d)>>>1|(21845&d)<<1,g=(61680&(g=(52428&g)>>>2|(13107&g)<<2))>>>4|(3855&g)<<
4,m[d]=((65280&g)>>>8|(255&g)<<8)>>>1;var p=function(a,b,c){for(var d=a.length,e=0,f=new h(b);e<d;++e)++f[a[e]-1];var g=new h(b);for(e=0;e<b;++e)g[e]=g[e-1]+f[e-1]<<1;if(c)for(c=new h(1<<b),f=15-b,e=0;e<d;++e){if(a[e]){var k=e<<4|a[e],l=b-a[e],r=g[a[e]-1]++<<l;for(l=r|(1<<l)-1;r<=l;++r)c[m[r]>>>f]=k}}else for(c=new h(d),e=0;e<d;++e)a[e]&&(c[e]=m[g[a[e]-1]++]>>>15-a[e]);return c};g=new f(288);for(d=0;144>d;++d)g[d]=8;for(d=144;256>d;++d)g[d]=9;for(d=256;280>d;++d)g[d]=7;for(d=280;288>d;++d)g[d]=8;
var y=new f(32);for(d=0;32>d;++d)y[d]=5;var x=p(g,9,1),q=p(y,5,1),n=function(a){for(var b=a[0],c=1;c<a.length;++c)a[c]>b&&(b=a[c]);return b},r=function(a,b,c){var d=b/8|0;return(a[d]|a[d+1]<<8)>>(7&b)&c},v=function(a,b){var c=b/8|0;return(a[c]|a[c+1]<<8|a[c+2]<<16)>>(7&b)},w=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long",
"stream finishing","invalid zip data"],u=function(a,b,c){b=Error(b||w[a]);if(b.code=a,Error.captureStackTrace&&Error.captureStackTrace(b,u),!c)throw b;return b},G=function(d,e,g){var m=d.length;if(!m||g&&!g.l&&5>m)return e||new f(0);var w=!e||g,y=!g||g.i;g||(g={});e||(e=new f(3*m));var A,L=function(a){var b=e.length;a>b&&(a=new f(Math.max(2*b,a)),a.set(e),e=a)},G=g.f||0,F=g.p||0,C=g.b||0,N=g.l,O=g.d,U=g.m,M=g.n,z=8*m;do{if(!N){g.f=G=r(d,F,1);var E=r(d,F+1,3);if(F+=3,!E){F=d[(V=((A=F)/8|0)+(7&A&&1)+
4)-4]|d[V-3]<<8;E=V+F;if(E>m){y&&u(0);break}w&&L(C+F);e.set(d.subarray(V,E),C);g.b=C+=F;g.p=F=8*E;continue}if(1==E)N=x,O=q,U=9,M=5;else if(2==E){U=r(d,F,31)+257;O=r(d,F+10,15)+4;N=U+r(d,F+5,31)+1;F+=14;M=new f(N);var P=new f(19);for(E=0;E<O;++E)P[c[E]]=r(d,F+3*E,7);F+=3*O;E=n(P);O=(1<<E)-1;var ta=p(P,E,1);for(E=0;E<N;){var V;V=ta[r(d,F,O)];if(F+=15&V,16>(V>>>=4))M[E++]=V;else{var fa=P=0;for(16==V?(fa=3+r(d,F,3),F+=2,P=M[E-1]):17==V?(fa=3+r(d,F,7),F+=3):18==V&&(fa=11+r(d,F,127),F+=7);fa--;)M[E++]=
P}}N=M.subarray(0,U);E=M.subarray(U);U=n(N);M=n(E);N=p(N,U,1);O=p(E,M,1)}else u(1);if(F>z){y&&u(0);break}}w&&L(C+131072);ta=(1<<U)-1;fa=(1<<M)-1;for(var ua=F;;ua=F){E=(P=N[v(d,F)&ta])>>>4;if((F+=15&P)>z){y&&u(0);break}if(P||u(2),256>E)e[C++]=E;else{if(256==E){ua=F;N=null;break}P=E-254;if(264<E){var aa=b[E-=257];P=r(d,F,(1<<aa)-1)+k[E];F+=aa}E=O[v(d,F)&fa];aa=E>>>4;E||u(3);F+=15&E;E=t[aa];3<aa&&(aa=a[aa],E+=v(d,F)&(1<<aa)-1,F+=aa);if(F>z){y&&u(0);break}w&&L(C+131072);for(P=C+P;C<P;C+=4)e[C]=e[C-E],
e[C+1]=e[C+1-E],e[C+2]=e[C+2-E],e[C+3]=e[C+3-E];C=P}}g.l=N;g.p=ua;g.b=C;N&&(G=1,g.m=U,g.d=O,g.n=M)}while(!G);return C==e.length?e:function(a,b,c){(null==b||0>b)&&(b=0);(null==c||c>a.length)&&(c=a.length);var d=new (a instanceof h?h:a instanceof l?l:f)(c-b);return d.set(a.subarray(b,c)),d}(e,0,C)};d=new f(0);g="undefined"!=typeof TextDecoder&&new TextDecoder;try{g.decode(d,{stream:!0}),1}catch(L){}return e.convert_streams=function(a){function b(){var a=f.getUint16(g);return g+=2,a}function c(){var a=
f.getUint32(g);return g+=4,a}function d(a){u.setUint16(y,a);y+=2}function e(a){u.setUint32(y,a);y+=4}var f=new DataView(a),g=0;c();var h=c();c();var k=b();b();c();b();b();c();c();c();c();c();for(var m=0;Math.pow(2,m)<=k;)m++;m--;for(var l=16*Math.pow(2,m),r=16*k-l,p=12,q=[],v=0;v<k;v++)q.push({tag:c(),offset:c(),compLength:c(),origLength:c(),origChecksum:c()}),p+=16;var w,n=new Uint8Array(12+16*q.length+q.reduce(function(a,b){return a+b.origLength+4},0));v=n.buffer;var u=new DataView(v),y=0;return e(h),
d(k),d(l),d(m),d(r),q.forEach(function(a){e(a.tag);e(a.origChecksum);e(p);e(a.origLength);a.outOffset=p;0!=(p+=a.origLength)%4&&(p+=4-p%4)}),q.forEach(function(b){var c=a.slice(b.offset,b.offset+b.compLength);if(b.compLength!=b.origLength){var d=new Uint8Array(b.origLength);c=new Uint8Array(c,2);G(c,d)}else d=new Uint8Array(c);n.set(d,b.outOffset);d=0;0!=(p=b.outOffset+b.origLength)%4&&(d=4-p%4);n.set((new Uint8Array(d)).buffer,b.outOffset+b.origLength);w=p+d}),v.slice(0,w)},Object.defineProperty(e,
"__esModule",{value:!0}),e}({}).convert_streams},function(e,f){function h(a){if(!t){let a={R:2,L:1,D:4,C:16,U:32,T:8};t=new Map;for(let b in k){let c=0;k[b].split(",").forEach(d=>{let [e,f]=d.split("+");e=parseInt(e,36);f=f?parseInt(f,36):0;t.set(c+=e,a[b]);for(d=f;d--;)t.set(++c,a[b])})}}return t.get(a)||32}function l(a,b){let c=[];for(let d=0;d<b.length;d++){let f=b.codePointAt(d);65535<f&&d++;c.push(e.U.codeToGlyph(a,f))}if(a=a.GSUB){let {lookupList:d,featureList:f}=a,g,k=/^(rlig|liga|mset|isol|init|fina|medi|half|pres|blws|ccmp)$/,
l=[];f.forEach(a=>{if(k.test(a.tag))for(let k=0;k<a.tab.length;k++){if(l[a.tab[k]])continue;l[a.tab[k]]=!0;let r=d[a.tab[k]],p=/^(isol|init|fina|medi)$/.test(a.tag);if(p&&!g){{var f=b;let a=new Uint8Array(f.length),c=32,d=1,e=-1;for(let b=0;b<f.length;b++){let g=f.codePointAt(b),k=h(g)|0,m=1;k&8||(c&21?k&22?(m=3,1!==d&&3!==d||a[e]++):k&33&&(2===d||4===d)&&a[e]--:c&34&&(2===d||4===d)&&a[e]--,d=a[b]=m,c=k,e=b,65535<g&&b++)}g=a}}for(f=0;f<c.length;f++)g&&p&&m[g[f]]!==a.tag||e.U._applySubs(c,f,r,d)}})}return c}
function b(b,c){function d(a){for(let b=h-1;0<=b;b--)if(-1!==c[b]&&(!a||a(c[b])))return b;return-1}function f(c){return 1===a(b,c)}function g(a,b){for(let c=0;3>c;c++)k[3*b+c]+=a[c]||0}let k=new Int16Array(3*c.length),h=0;for(;h<c.length;h++){var m=c[h];if(-1!==m){k[3*h+2]=b.hmtx.aWidth[m];var l=b.GPOS;if(l){l=l.lookupList;for(var p=0;p<l.length;p++){var t=l[p];for(var y=0;y<t.tabs.length;y++){var A=t.tabs[y];if(1===t.ltype){if(-1!==e._lctf.coverageIndex(A.coverage,m)&&A.pos){g(A.pos,h);break}}else if(2===
t.ltype){var H=null,B=d();if(-1!==B){var z=e._lctf.coverageIndex(A.coverage,c[B]);if(-1!==z){if(1===A.fmt)for(A=A.pairsets[z],z=0;z<A.length;z++)A[z].gid2===m&&(H=A[z]);else 2===A.fmt&&(H=e.U._getGlyphClass(c[B],A.classDef1),z=e.U._getGlyphClass(m,A.classDef2),H=A.matrix[H][z]);if(H){H.val1&&g(H.val1,B);H.val2&&g(H.val2,h);break}}}}else if(4===t.ltype){if(z=e._lctf.coverageIndex(A.markCoverage,m),-1!==z&&(B=d(f),H=-1===B?-1:e._lctf.coverageIndex(A.baseCoverage,c[B]),-1!==H)){t=A.markArray[z];y=A.baseArray[H][t.markClass];
k[3*h]=y.x-t.x+k[3*B]-k[3*B+2];k[3*h+1]=y.y-t.y+k[3*B+1];break}}else if(6===t.ltype&&(H=e._lctf.coverageIndex(A.mark1Coverage,m),-1!==H&&(B=d(),-1!==B&&(z=c[B],3===a(b,z)&&(z=e._lctf.coverageIndex(A.mark2Coverage,z),-1!==z))))){t=A.mark1Array[H];y=A.mark2Array[z][t.markClass];k[3*h]=y.x-t.x+k[3*B]-k[3*B+2];k[3*h+1]=y.y-t.y+k[3*B+1];break}}}}else b.kern&&!b.cff&&(l=d(),-1!==l&&(p=b.kern.glyph1.indexOf(c[l]),-1!==p&&(m=b.kern.rval[p].glyph2.indexOf(m),-1!==m&&(k[3*l+2]+=b.kern.rval[p].vals[m]))))}}return k}
function a(a,b){return(a=a.GDEF&&a.GDEF.glyphClassDef)?e.U._getGlyphClass(b,a):0}function c(...a){for(let b=0;b<a.length;b++)if("number"===typeof a[b])return a[b]}function d(a){let d=Object.create(null),f=a["OS/2"],k=a.hhea,h=a.head.unitsPerEm,m=c(f&&f.sTypoAscender,k&&k.ascender,h),p={unitsPerEm:h,ascender:m,descender:c(f&&f.sTypoDescender,k&&k.descender,0),capHeight:c(f&&f.sCapHeight,m),xHeight:c(f&&f.sxHeight,m),lineGap:c(f&&f.sTypoLineGap,k&&k.lineGap),supportsCodePoint(b){return 0<e.U.codeToGlyph(a,
b)},forEachGlyph(c,f,k,h){let m=0;const r=1/p.unitsPerEm*f,q=l(a,c);let v=0;const n=b(a,q);q.forEach((b,l)=>{if(-1!==b){var p=d[b];if(!p){const {cmds:c,crds:f}=e.U.glyphToPath(a,b);p="";var q=0;for(let a=0,b=c.length;a<b;a++){var w=g[c[a]];p+=c[a];for(var t=1;t<=w;t++)p+=(1<t?",":"")+f[q++]}let k;if(f.length){q=w=Infinity;t=k=-Infinity;for(let a=0,b=f.length;a<b;a+=2){let b=f[a],c=f[a+1];b<q&&(q=b);c<w&&(w=c);b>t&&(t=b);c>k&&(k=c)}}else q=t=w=k=0;p=d[b]={index:b,advanceWidth:a.hmtx.aWidth[b],xMin:q,
yMin:w,xMax:t,yMax:k,path:p}}h.call(null,p,m+n[3*l]*r,n[3*l+1]*r,v);m+=n[3*l+2]*r;k&&(m+=k*f)}v+=65535<c.codePointAt(v)?2:1});return m}};return p}let g={M:2,L:2,Q:4,C:6,Z:0},k={C:"18g,ca,368,1kz",D:"17k,6,2,2+4,5+c,2+6,2+1,10+1,9+f,j+11,2+1,a,2,2+1,15+2,3,j+2,6+3,2+8,2,2,2+1,w+a,4+e,3+3,2,3+2,3+5,23+w,2f+4,3,2+9,2,b,2+3,3,1k+9,6+1,3+1,2+2,2+d,30g,p+y,1,1+1g,f+x,2,sd2+1d,jf3+4,f+3,2+4,2+2,b+3,42,2,4+2,2+1,2,3,t+1,9f+w,2,el+2,2+g,d+2,2l,2+1,5,3+1,2+1,2,3,6,16wm+1v",R:"17m+3,2,2,6+3,m,15+2,2+2,h+h,13,3+8,2,2,3+1,2,p+1,x,5+4,5,a,2,2,3,u,c+2,g+1,5,2+1,4+1,5j,6+1,2,b,2+2,f,2+1,1s+2,2,3+1,7,1ez0,2,2+1,4+4,b,4,3,b,42,2+2,4,3,2+1,2,o+3,ae,ep,x,2o+2,3+1,3,5+1,6",
L:"x9u,jff,a,fd,jv",T:"4t,gj+33,7o+4,1+1,7c+18,2,2+1,2+1,2,21+a,2,1b+k,h,2u+6,3+5,3+1,2+3,y,2,v+q,2k+a,1n+8,a,p+3,2+8,2+2,2+4,18+2,3c+e,2+v,1k,2,5+7,5,4+6,b+1,u,1n,5+3,9,l+1,r,3+1,1m,5+1,5+1,3+2,4,v+1,4,c+1,1m,5+4,2+1,5,l+1,n+5,2,1n,3,2+3,9,8+1,c+1,v,1q,d,1f,4,1m+2,6+2,2+3,8+1,c+1,u,1n,3,7,6+1,l+1,t+1,1m+1,5+3,9,l+1,u,21,8+2,2,2j,3+6,d+7,2r,3+8,c+5,23+1,s,2,2,1k+d,2+4,2+1,6+a,2+z,a,2v+3,2+5,2+1,3+1,q+1,5+2,h+3,e,3+1,7,g,jk+2,qb+2,u+2,u+1,v+1,1t+1,2+6,9,3+a,a,1a+2,3c+1,z,3b+2,5+1,a,7+2,64+1,3,1n,2+6,2,2,3+7,7+9,3,1d+d,1,1+1,1s+3,1d,2+4,2,6,15+8,d+1,x+3,3+1,2+2,1l,2+1,4,2+2,1n+7,3+1,49+2,2+c,2+6,5,7,4+1,5j+1l,2+4,ek,3+1,r+4,1e+4,6+5,2p+c,1+3,1,1+2,1+b,2db+2,3y,2p+v,ff+3,30+1,n9x,1+2,2+9,x+1,29+1,7l,4,5,q+1,6,48+1,r+h,e,13+7,q+a,1b+2,1d,3+3,3+1,14,1w+5,3+1,3+1,d,9,1c,1g,2+2,3+1,6+1,2,17+1,9,6n,3,5,fn5,ki+f,h+f,5s,6y+2,ea,6b,46+4,1af+2,2+1,6+3,15+2,5,4m+1,fy+3,as+1,4a+a,4x,1j+e,1l+2,1e+3,3+1,1y+2,11+4,2+7,1r,d+1,1h+8,b+3,3,2o+2,3,2+1,7,4h,4+7,m+1,1m+1,4,12+6,4+4,5g+7,3+2,2,o,2d+5,2,5+1,2+1,6n+3,7+1,2+1,s+1,2e+7,3,2+1,2z,2,3+5,2,2u+2,3+3,2+4,78+8,2+1,75+1,2,5,41+3,3+1,5,x+9,15+5,3+3,9,a+5,3+2,1b+c,2+1,bb+6,2+5,2,2b+l,3+6,2+1,2+1,3f+5,4,2+1,2+6,2,21+1,4,2,9o+1,470+8,at4+4,1o+6,t5,1s+3,2a,f5l+1,2+3,43o+2,a+7,1+7,3+6,v+3,45+2,1j0+1i,5+1d,9,f,n+4,2+e,11t+6,2+g,3+6,2+1,2+4,7a+6,c6+3,15t+6,32+6,1,gzau,v+2n,3l+6n"},
t,m=[null,"isol","init","fina","medi"];return function(a){var b=new Uint8Array(a,0,4);b=e._bin.readASCII(b,0,4);if("wOFF"===b)a=f(a);else if("wOF2"===b)throw Error("woff2 fonts not supported");return d(e.parse(a)[0])}}],init(e,f,h){e=e();f=f();return h(e,f)}});R=ba.defineWorkerModule({name:"FontResolver",dependencies:[function(e,f){function h(a,b){let c=b=>{console.error(`Failure loading font ${a}`,b)};try{let d=new XMLHttpRequest;d.open("get",a,!0);d.responseType="arraybuffer";d.onload=function(){if(400<=
d.status)c(Error(d.statusText));else if(0<d.status)try{let c=e(d.response);c.src=a;b(c)}catch(t){c(t)}};d.onerror=c;d.send()}catch(k){c(k)}}function l(c,d){let e=b[c];e?d(e):a[c]?a[c].push(d):(a[c]=[d],h(c,d=>{d.src=c;b[c]=d;a[c].forEach(a=>a(d));delete a[c]}))}let b=Object.create(null),a=Object.create(null);return function(a,d,{lang:e,fonts:k=[],style:h="normal",weight:m="normal",unicodeFontsURL:p}={}){function c(){if(w.length){let b=w.map(b=>a.substring(b[0],b[1]+1)).join("\n");f.getFontsForString(b,
{lang:e||void 0,style:h,weight:m,dataUrl:p}).then(({fontUrls:a,chars:b})=>{let c=r.length,d=0;w.forEach(a=>{for(let e=0,f=a[1]-a[0];e<=f;e++)n[a[0]+e]=b[d++]+c;d++});let e=0;a.forEach((b,d)=>{l(b,b=>{r[d+c]=b;++e===a.length&&g()})})})}else g()}function g(){d({chars:n,fonts:r})}function q(a,b){for(let c=0;c<b.length;c++){let [d,e=d]=b[c];if(d<=a&&a<=e)return!0}return!1}let n=new Uint8Array(a.length),r=[];a.length||g();let v=new Map,w=[];"italic"!==h&&(h="normal");"number"!==typeof m&&(m="bold"===m?
700:400);k&&!Array.isArray(k)&&(k=[k]);k=k.slice().filter(a=>!a.lang||a.lang.test(e)).reverse();if(k.length){let d=0;(function C(e=0){for(let c=e,f=a.length;c<f;c++){e=a.codePointAt(c);if(1===d&&r[n[c-1]].supportsCodePoint(e)||0<c&&/\s/.test(a[c]))n[c]=n[c-1],2===d&&(w[w.length-1][1]=c);else for(let a=n[c],f=k.length;a<=f;a++)if(a===f)(2===d?w[w.length-1]:w[w.length]=[c,c])[1]=c,d=2;else{n[c]=a;let {src:f,unicodeRange:g}=k[a];if(!g||q(e,g)){let a=b[f];if(!a){l(f,()=>{C(c)});return}if(a.supportsCodePoint(e)){let b=
v.get(a);"number"!==typeof b&&(b=r.length,r.push(a),v.set(a,b));n[c]=b;d=1;break}}}65535<e&&c+1<f&&(n[c+1]=n[c],c++,2===d&&(w[w.length-1][1]=c))}c()})()}else w.push([0,a.length-1]),c()}},R,function(){return function(e){function f(a){var b=(a&m).toString(16),c=((a&m)+k-1).toString(16);return"codepoint-index/plane"+(a>>16)+"/"+b+"-"+c+".json"}function h(a,b){a&=t;return 0!=((b.codePointAt(a/6|0)||48)-48&1<<a%6)}function l(a,b){a.replace(/U\+/gi,"").replace(/^,+|,+$/g,"").split(/,+/).map(function(a){return a.split("-").map(function(a){return parseInt(a.trim(),
16)})}).forEach(function(a){var c=a[0];a=a[1];void 0===a&&(a=c);b(c,a)})}function b(a,b){l(a,function(a,c){for(;a<=c;a++)b(a)})}function a(a){var c=x.get(a);return c||(c=new g,b(a.ranges,function(a){return c.add(a)}),x.set(a,c)),c}function c(a,b,c){if(a[b])a=b;else if(a[c])a=c;else a:{for(var d in a){a=d;break a}a=void 0}return a}function d(a){return q||(q=new Set,b("9-D,20,85,A0,1680,2000-200A,2028-202F,205F,3000",function(a){q.add(a)})),q.has(a)}var g=function(){this.buckets=new Map};g.prototype.add=
function(a){var b=a>>5;this.buckets.set(b,(this.buckets.get(b)||0)|1<<(31&a))};g.prototype.has=function(a){var b=this.buckets.get(a>>5);return void 0!==b&&0!=(b&1<<(31&a))};g.prototype.serialize=function(){var a=[];return this.buckets.forEach(function(b,c){a.push((+c).toString(36)+":"+b.toString(36))}),a.join(",")};g.prototype.deserialize=function(a){var b=this;this.buckets.clear();a.split(",").forEach(function(a){a=a.split(":");b.buckets.set(parseInt(a[0],36),parseInt(a[1],36))})};var k=Math.pow(2,
8),t=k-1,m=~t,p={},y={},x=new WeakMap,q,n=new Map;return e.CodePointSet=g,e.clearCache=function(){p={};y={}},e.getFontsForString=function(b,e){function g(a){var b=n.get(a);return b||(b=fetch(r+"/"+a).then(function(a){if(!a.ok)throw Error(a.statusText);return a.json().then(function(a){if(!Array.isArray(a)||1!==a[0])throw Error("Incorrect schema version; need 1, got "+a[0]);return a[1]})}).catch(function(b){if("https://cdn.jsdelivr.net/gh/lojjic/unicode-font-resolver@v1.0.1/packages/data"!==r)return F||
(console.error('unicode-font-resolver: Failed loading from dataUrl "'+r+'", trying default CDN. '+b.message),F=!0),r="https://cdn.jsdelivr.net/gh/lojjic/unicode-font-resolver@v1.0.1/packages/data",n.delete(a),g(a);throw b;}),n.set(a,b)),b}void 0===e&&(e={});var k=e.lang;void 0===k&&(k=/\p{Script=Hangul}/u.test(b)?"ko":/\p{Script=Hiragana}|\p{Script=Katakana}/u.test(b)?"ja":"en");var m=e.category;void 0===m&&(m="sans-serif");var l=e.style;void 0===l&&(l="normal");var q=e.weight;void 0===q&&(q=400);
var r=(e.dataUrl||"https://cdn.jsdelivr.net/gh/lojjic/unicode-font-resolver@v1.0.1/packages/data").replace(/\/$/g,""),t=new Map,v=new Uint8Array(b.length),x={},z={},I=Array(b.length),K=new Map,F=!1;e=function(a){var c=b.codePointAt(a),d=f(c);I[a]=d;p[d]||K.has(d)||K.set(d,g(d).then(function(a){p[d]=a}));65535<c&&(a++,J=a)};for(var J=0;J<b.length;J++)e(J);return Promise.all(K.values()).then(function(){K.clear();for(var a=function(a){var d=b.codePointAt(a),e=null,f=p[I[a]],m=void 0,l;for(l in f){var q=
z[l];if(void 0===q&&(q=z[l]=(new RegExp(l)).test(k||"en")),q){for(var r in m=l,f[l])if(h(d,f[l][r])){e=r;break}break}}if(!e){var t;a:for(t in f)if(t!==m)for(var n in f[t])if(h(d,f[t][n])){e=n;break a}}e||(console.debug("No font coverage for U+"+d.toString(16)),e="latin");I[a]=e;y[e]||K.has(e)||K.set(e,g("font-meta/"+e+".json").then(function(a){y[e]=a}));65535<d&&(a++,c=a)},c=0;c<b.length;c++)a(c);return Promise.all(K.values())}).then(function(){for(var e,f=null,g=0;g<b.length;g++){var k=b.codePointAt(g);
if(f&&(d(k)||a(f).has(k)))v[g]=v[g-1];else{f=y[I[g]];var h=x[f.id];if(!h){var p=f.typeforms;h=c(p,m,"sans-serif");var n=c(p[h],l,"normal");p=null===(e=p[h])||void 0===e?void 0:e[n];var w=q,u=w;if(!p.includes(u)){u=1/0;for(var z=0;z<p.length;z++)Math.abs(p[z]-w)<Math.abs(u-w)&&(u=p[z])}h=x[f.id]=r+"/font-files/"+f.id+"/"+h+"."+n+"."+u+".woff"}n=t.get(h);null==n&&(n=t.size,t.set(h,n));v[g]=n}65535<k&&(g++,v[g]=v[g-1])}return{fontUrls:Array.from(t.keys()),chars:v}})},Object.defineProperty(e,"__esModule",
{value:!0}),e}({})}],init(e,f,h){return e(f,h())}});let S=()=>(self.performance||Date).now(),la=Ca["default"](),ya,ka=[],pa=0,Wa=(...e)=>new Promise((f,h)=>{ka.push(()=>{const l=S();try{la.webgl.generateIntoCanvas(...e),f({timing:S()-l})}catch(b){h(b)}});pa||(pa=setTimeout(Aa,0))}),Ba={},Xa=0,ab=la.webglUtils.resizeWebGLCanvasWithoutClearing,Q={defaultFontURL:null,unicodeFontsURL:null,sdfGlyphSize:64,sdfMargin:.0625,sdfExponent:9,textureWidth:2048,useWorker:!0},Ya=new z.Color,Da=!1,ma=Object.create(null),
na;X=ba.defineWorkerModule({name:"Typesetter",dependencies:[function(e,f){function h({text:a,lang:b,fonts:c,style:d,weight:f,preResolvedFonts:g,unicodeFontsURL:h},k){let m=({chars:a,fonts:b})=>{let c,d;const e=[];for(let f=0;f<a.length;f++)a[f]!==d?(d=a[f],e.push(c={start:f,end:f,fontObj:b[a[f]]})):c.end=f;k(e)};g?m(g):e(a,m,{lang:b,fonts:c,style:d,weight:f,unicodeFontsURL:h})}function l({text:e="",font:l,lang:t,fontSize:x=400,fontWeight:q=1,fontStyle:n="normal",letterSpacing:r=0,lineHeight:v="normal",
maxWidth:w=Infinity,direction:u,textAlign:z="left",textIndent:J=0,whiteSpace:C="normal",overflowWrap:A="normal",anchorX:H=0,anchorY:B=0,metricsOnly:Z=!1,unicodeFontsURL:D,preResolvedFonts:I=null,includeCaretPositions:K=!1,chunkedBoundsSize:F=8192,colorRanges:Q=null},N){let m=c(),p={fontLoad:0,typesetting:0};-1<e.indexOf("\r")&&(console.info("Typesetter: got text with \\r chars; normalizing to \\n"),e=e.replace(/\r\n/g,"\n").replace(/\r/g,"\n"));x=+x;r=+r;w=+w;v=v||"normal";J=+J;h({text:e,lang:t,style:n,
weight:q,fonts:"string"===typeof l?[{src:l}]:l,unicodeFontsURL:D,preResolvedFonts:I},h=>{p.fontLoad=c()-m;let l=isFinite(w),q=null,n=null,t=null,y=null,D=null,G=null,I=null,L=null,M=0,O=0,U="nowrap"!==C,S=new Map,ba=c(),R=J,X=0,T=new d,W=[T];h.forEach(a=>{let {fontObj:b}=a,{ascender:c,descender:f,unitsPerEm:h,lineGap:m,capHeight:p,xHeight:q}=b,t=S.get(b);if(!t){var n=x/h;let a="normal"===v?(c-f+m)*n:v*x,d=Math.min(a,(c-f)*n),e=(c+f)/2*n+d/2;t={index:S.size,src:b.src,fontObj:b,fontSizeMult:n,unitsPerEm:h,
ascender:c*n,descender:f*n,capHeight:p*n,xHeight:q*n,lineHeight:a,baseline:-((a-(c-f)*n)/2)-c*n,caretTop:e,caretBottom:e-d};S.set(b,t)}let {fontSizeMult:y}=t;n=e.slice(a.start,a.end+1);let u,z;b.forEachGlyph(n,x,r,(b,c,f,h)=>{c+=X;h+=a.start;u=c;z=b;let m=e.charAt(h),p=b.advanceWidth*y;var q=T.count;"isEmpty"in b||(b.isWhitespace=!!m&&/[^\S\u00A0]/.test(m),b.canBreakAfter=!!m&&k.test(m),b.isEmpty=b.xMin===b.xMax||b.yMin===b.yMax||g.test(m));b.isWhitespace||b.isEmpty||O++;if(U&&l&&!b.isWhitespace&&
c+p+R>w&&q){if(T.glyphAt(q-1).glyphObj.canBreakAfter){var n=new d;R=-c}else for(;q--;)if(0===q&&"break-word"===A){n=new d;R=-c;break}else if(T.glyphAt(q).glyphObj.canBreakAfter){n=T.splitAt(q+1);q=n.glyphAt(0).x;R-=q;for(let a=n.count;a--;)n.glyphAt(a).x-=q;break}n&&(T.isSoftWrapped=!0,T=n,W.push(T),M=w)}n=T.glyphAt(T.count);n.glyphObj=b;n.x=c+R;n.y=f;n.width=p;n.charIndex=h;n.fontData=t;"\n"===m&&(T=new d,W.push(T),R=-(c+p+r*x)+J)});X=u+z.advanceWidth*y+r*x});let ca=0;W.forEach(a=>{let b=!0;for(let d=
a.count;d--;){var c=a.glyphAt(d);b&&!c.glyphObj.isWhitespace&&(a.width=c.x+c.width,a.width>M&&(M=a.width),b=!1);let {lineHeight:e,capHeight:f,xHeight:g,baseline:h}=c.fontData;e>a.lineHeight&&(a.lineHeight=e);c=h-a.baseline;0>c&&(a.baseline+=c,a.cap+=c,a.ex+=c);a.cap=Math.max(a.cap,a.baseline+f);a.ex=Math.max(a.ex,a.baseline+g)}a.baseline-=ca;a.cap-=ca;a.ex-=ca;ca+=a.lineHeight});let ea=0,da=0;H&&("number"===typeof H?ea=-H:"string"===typeof H&&(ea=-M*("left"===H?0:"center"===H?.5:"right"===H?1:b(H))));
B&&("number"===typeof B?da=-B:"string"===typeof B&&(da="top"===B?0:"top-baseline"===B?-W[0].baseline:"top-cap"===B?-W[0].cap:"top-ex"===B?-W[0].ex:"middle"===B?ca/2:"bottom"===B?ca:"bottom-baseline"===B?-W[W.length-1].baseline:b(B)*ca));if(!Z){let b=f.getEmbeddingLevels(e,u);q=new Uint16Array(O);n=new Uint8Array(O);t=new Float32Array(2*O);y={};I=[Infinity,Infinity,-Infinity,-Infinity];L=[];K&&(G=new Float32Array(4*e.length));Q&&(D=new Uint8Array(3*O));let c=0,d=-1,g=-1,k,m;W.forEach((h,l)=>{let {count:p,
width:r}=h;if(0<p){l=0;for(var w=p;w--&&h.glyphAt(w).glyphObj.isWhitespace;)l++;var v=w=0;if("center"===z)w=(M-r)/2;else if("right"===z)w=M-r;else if("justify"===z&&h.isSoftWrapped){v=0;for(var u=p-l;u--;)h.glyphAt(u).glyphObj.isWhitespace&&v++;v=(M-r)/v}if(v||w){u=0;for(var x=0;x<p;x++){var A=h.glyphAt(x),B=A.glyphObj;A.x+=w+u;0!==v&&B.isWhitespace&&x<p-l&&(u+=v,A.width+=v)}}w=f.getReorderSegments(e,b,h.glyphAt(0).charIndex,h.glyphAt(h.count-1).charIndex);for(v=0;v<w.length;v++){let [a,b]=w[v];u=
Infinity;x=-Infinity;for(A=0;A<p;A++)if(h.glyphAt(A).charIndex>=a){for(B=A;A<p;A++){var C=h.glyphAt(A);if(C.charIndex>b)break;A<p-l&&(u=Math.min(u,C.x),x=Math.max(x,C.x+C.width))}for(;B<A;B++)C=h.glyphAt(B),C.x=x-(C.x+C.width-u);break}}let E;l=a=>E=a;for(w=0;w<p;w++){u=h.glyphAt(w);E=u.glyphObj;v=E.index;(x=b.levels[u.charIndex]&1)&&(A=f.getMirroredCharacter(e[u.charIndex]))&&u.fontData.fontObj.forEachGlyph(A,0,0,l);if(K){let {charIndex:b,fontData:c}=u;A=u.x+ea;B=u.x+u.width+ea;G[4*b]=x?B:A;G[4*b+
1]=x?A:B;G[4*b+2]=h.baseline+c.caretBottom+da;G[4*b+3]=h.baseline+c.caretTop+da;x=b-d;1<x&&a(G,d,x);d=b}if(Q)for({charIndex:x}=u;x>g;)g++,Q.hasOwnProperty(g)&&(m=Q[g]);if(!E.isWhitespace&&!E.isEmpty){x=c++;let {fontSizeMult:a,src:b,index:d}=u.fontData;A=y[b]||(y[b]={});A[v]||(A[v]={path:E.path,pathBounds:[E.xMin,E.yMin,E.xMax,E.yMax]});B=u.x+ea;C=u.y+h.baseline+da;t[2*x]=B;t[2*x+1]=C;u=B+E.xMin*a;A=C+E.yMin*a;B+=E.xMax*a;C+=E.yMax*a;u<I[0]&&(I[0]=u);A<I[1]&&(I[1]=A);B>I[2]&&(I[2]=B);C>I[3]&&(I[3]=
C);0===x%F&&(k={start:x,end:x,rect:[Infinity,Infinity,-Infinity,-Infinity]},L.push(k));k.end++;let e=k.rect;u<e[0]&&(e[0]=u);A<e[1]&&(e[1]=A);B>e[2]&&(e[2]=B);C>e[3]&&(e[3]=C);q[x]=v;n[x]=d;Q&&(v=3*x,D[v]=m>>16&255,D[v+1]=m>>8&255,D[v+2]=m&255)}}}});G&&(h=e.length-d,1<h&&a(G,d,h))}let Y=[];S.forEach(({index:a,src:b,unitsPerEm:c,ascender:d,descender:e,lineHeight:f,capHeight:g,xHeight:h})=>{Y[a]={src:b,unitsPerEm:c,ascender:d,descender:e,lineHeight:f,capHeight:g,xHeight:h}});p.typesetting=c()-ba;N({glyphIds:q,
glyphFontIndices:n,glyphPositions:t,glyphData:y,fontData:Y,caretPositions:G,glyphColors:D,chunkedBounds:L,fontSize:x,topBaseline:da+W[0].baseline,blockBounds:[ea,da-ca,ea+M,da],visibleBounds:I,timings:p})})}function b(a){a=(a=a.match(/^([\d.]+)%$/))?parseFloat(a[1]):NaN;return isNaN(a)?0:a/100}function a(a,b,c){let d=a[4*b],e=a[4*b+2],f=a[4*b+3],g=(a[4*b+1]-d)/c;for(let h=0;h<c;h++){let c=4*(b+h);a[c]=d+g*h;a[c+1]=d+g*(h+1);a[c+2]=e;a[c+3]=f}}function c(){return(self.performance||Date).now()}function d(){this.data=
[]}let g=/[\u00AD\u034F\u061C\u115F-\u1160\u17B4-\u17B5\u180B-\u180E\u200B-\u200F\u202A-\u202E\u2060-\u206F\u3164\uFE00-\uFE0F\uFEFF\uFFA0\uFFF0-\uFFF8]/,k=/[^\S\u00A0]|[\-\u007C\u00AD\u2010\u2012-\u2014\u2027\u2056\u2E17\u2E40]/,t="glyphObj x y width charIndex fontData".split(" ");d.prototype={width:0,lineHeight:0,baseline:0,cap:0,ex:0,isSoftWrapped:!1,get count(){return Math.ceil(this.data.length/t.length)},glyphAt(a){let b=d.flyweight;b.data=this.data;b.index=a;return b},splitAt(a){let b=new d;
b.data=this.data.splice(a*t.length);return b}};d.flyweight=t.reduce((a,b,c,d)=>{Object.defineProperty(a,b,{get(){return this.data[this.index*t.length+c]},set(a){this.data[this.index*t.length+c]=a}});return a},{data:null,index:0});return{typeset:l,measure:function(a,b){l({...a,metricsOnly:!0},a=>{let [c,d,e,f]=a.blockBounds;b({width:e-c,height:f-d})})}}},R,X["default"]],init(e,f,h){return e(f,h())}});let Ga=ba.defineWorkerModule({name:"Typesetter",dependencies:[X],init(e){return function(f){return new Promise(h=>
{e.typeset(f,h)})}},getTransferables(e){const f=[];for(let h in e)e[h]&&e[h].buffer&&f.push(e[h].buffer);return f}}),$a=Ga.onMainThread,Ja={};class Ma extends z.InstancedBufferGeometry{constructor(){super();this.detail=1;this.curveRadius=0;this.groups=[{start:0,count:Infinity,materialIndex:0},{start:0,count:Infinity,materialIndex:1}];this.boundingSphere=new z.Sphere;this.boundingBox=new z.Box3}computeBoundingSphere(){}computeBoundingBox(){}set detail(e){if(e!==this._detail){this._detail=e;if("number"!==
typeof e||1>e)e=1;let f=bb(e);["position","normal","uv"].forEach(e=>{this.attributes[e]=f.attributes[e].clone()});this.setIndex(f.getIndex().clone())}}get detail(){return this._detail}set curveRadius(e){e!==this._curveRadius&&(this._curveRadius=e,this._updateBounds())}get curveRadius(){return this._curveRadius}updateGlyphs(e,f,h,l,b){this.updateAttributeData("aTroikaGlyphBounds",e,4);this.updateAttributeData("aTroikaGlyphIndex",f,1);this.updateAttributeData("aTroikaGlyphColor",b,3);this._blockBounds=
h;this._chunkedBounds=l;this.instanceCount=f.length;this._updateBounds()}_updateBounds(){let e=this._blockBounds;if(e){let {curveRadius:l,boundingBox:b}=this;if(l){let {PI:a,floor:c,min:d,max:g,sin:k,cos:t}=Math;var f=a/2,h=2*a;let m=Math.abs(l),p=e[0]/m,y=e[2]/m,x=c((p+f)/h)!==c((y+f)/h)?-m:d(k(p)*m,k(y)*m);f=c((p-f)/h)!==c((y-f)/h)?m:g(k(p)*m,k(y)*m);h=c((p+a)/h)!==c((y+a)/h)?2*m:g(m-t(p)*m,m-t(y)*m);b.min.set(x,e[1],0>l?-h:0);b.max.set(f,e[3],0>l?0:h)}else b.min.set(e[0],e[1],0),b.max.set(e[2],
e[3],0);b.getBoundingSphere(this.boundingSphere)}}applyClipRect(e){let f=this.getAttribute("aTroikaGlyphIndex").count,h=this._chunkedBounds;if(h)for(let l=h.length;l--;){f=h[l].end;let b=h[l].rect;if(b[1]<e.w&&b[3]>e.y&&b[0]<e.z&&b[2]>e.x)break}this.instanceCount=f}updateAttributeData(e,f,h){let l=this.getAttribute(e);f?l&&l.array.length===f.length?(l.array.set(f),l.needsUpdate=!0):(this.setAttribute(e,new z.InstancedBufferAttribute(f,h)),delete this._maxInstanceCount,this.dispose()):l&&this.deleteAttribute(e)}}
let va=new z.MeshBasicMaterial({color:16777215,side:z.DoubleSide,transparent:!0}),Na=new z.Matrix4,oa=new z.Vector3,wa=new z.Vector3,ha=[],fb=new z.Vector3,Oa=()=>{let e=new z.Mesh(new z.PlaneGeometry(1,1),va);Oa=()=>e;return e},Pa=()=>{let e=new z.Mesh(new z.PlaneGeometry(1,1,32,1),va);Pa=()=>e;return e},gb={type:"syncstart"},hb={type:"synccomplete"},Qa="font fontSize fontStyle fontWeight lang letterSpacing lineHeight maxWidth overflowWrap text direction textAlign textIndent whiteSpace anchorX anchorY colorRanges sdfGlyphSize".split(" "),
ib=Qa.concat("material","color","depthOffset","clipRect","curveRadius","orientation","glyphGeometryDetail");class ia extends z.Mesh{constructor(){super(new Ma,null);this.text="";this.curveRadius=this.anchorY=this.anchorX=0;this.direction="auto";this.unicodeFontsURL=this.font=null;this.fontSize=.1;this.fontStyle=this.fontWeight="normal";this.lang=null;this.letterSpacing=0;this.lineHeight="normal";this.maxWidth=Infinity;this.overflowWrap="normal";this.textAlign="left";this.textIndent=0;this.whiteSpace=
"normal";this.colorRanges=this.color=this.material=null;this.outlineColor=this.outlineWidth=0;this.outlineOpacity=1;this.strokeWidth=this.outlineOffsetY=this.outlineOffsetX=this.outlineBlur=0;this.strokeColor=8421504;this.fillOpacity=this.strokeOpacity=1;this.depthOffset=0;this.clipRect=null;this.orientation="+x+y";this.glyphGeometryDetail=1;this.sdfGlyphSize=null;this.gpuAccelerateSDF=!0;this.debugSDF=!1}sync(e){this._needsSync&&(this._needsSync=!1,this._isSyncing?(this._queuedSyncs||(this._queuedSyncs=
[])).push(e):(this._isSyncing=!0,this.dispatchEvent(gb),qa({text:this.text,font:this.font,lang:this.lang,fontSize:this.fontSize||.1,fontWeight:this.fontWeight||"normal",fontStyle:this.fontStyle||"normal",letterSpacing:this.letterSpacing||0,lineHeight:this.lineHeight||"normal",maxWidth:this.maxWidth,direction:this.direction||"auto",textAlign:this.textAlign,textIndent:this.textIndent,whiteSpace:this.whiteSpace,overflowWrap:this.overflowWrap,anchorX:this.anchorX,anchorY:this.anchorY,colorRanges:this.colorRanges,
includeCaretPositions:!0,sdfGlyphSize:this.sdfGlyphSize,gpuAccelerateSDF:this.gpuAccelerateSDF,unicodeFontsURL:this.unicodeFontsURL},f=>{this._isSyncing=!1;this._textRenderInfo=f;this.geometry.updateGlyphs(f.glyphBounds,f.glyphAtlasIndices,f.blockBounds,f.chunkedBounds,f.glyphColors);let h=this._queuedSyncs;h&&(this._queuedSyncs=null,this._needsSync=!0,this.sync(()=>{h.forEach(e=>e&&e())}));this.dispatchEvent(hb);e&&e()})))}onBeforeRender(e,f,h,l,b,a){this.sync();b.isTroikaTextMaterial&&this._prepareForRender(b)}dispose(){this.geometry.dispose()}get textRenderInfo(){return this._textRenderInfo||
null}createDerivedMaterial(e){return ra(e)}get material(){let e=this._derivedMaterial,f=this._baseMaterial||this._defaultMaterial||(this._defaultMaterial=va.clone());e&&e.isDerivedFrom(f)||(e=this._derivedMaterial=this.createDerivedMaterial(f),f.addEventListener("dispose",function l(){f.removeEventListener("dispose",l);e.dispose()}));if(this.hasOutline()){let f=e._outlineMtl;f||(f=e._outlineMtl=Object.create(e,{id:{value:e.id+.1}}),f.isTextOutlineMaterial=!0,f.depthWrite=!1,f.map=null,e.addEventListener("dispose",
function b(){e.removeEventListener("dispose",b);f.dispose()}));return[f,e]}return e}set material(e){e&&e.isTroikaTextMaterial?(this._derivedMaterial=e,this._baseMaterial=e.baseMaterial):this._baseMaterial=e}hasOutline(){return!!(this.outlineWidth||this.outlineBlur||this.outlineOffsetX||this.outlineOffsetY)}get glyphGeometryDetail(){return this.geometry.detail}set glyphGeometryDetail(e){this.geometry.detail=e}get curveRadius(){return this.geometry.curveRadius}set curveRadius(e){this.geometry.curveRadius=
e}get customDepthMaterial(){return Ka(this.material).getDepthMaterial()}get customDistanceMaterial(){return Ka(this.material).getDistanceMaterial()}_prepareForRender(e){var f=e.isTextOutlineMaterial,h=e.uniforms,l=this.textRenderInfo;if(l){let {sdfTexture:a,blockBounds:c}=l;h.uTroikaSDFTexture.value=a;h.uTroikaSDFTextureSize.value.set(a.image.width,a.image.height);h.uTroikaSDFGlyphSize.value=l.sdfGlyphSize;h.uTroikaSDFExponent.value=l.sdfExponent;h.uTroikaTotalBounds.value.fromArray(c);h.uTroikaUseGlyphColors.value=
!f&&!!l.glyphColors;let d=l=0,e=0,k;let t=0,m=0;if(f){let {outlineWidth:a,outlineOffsetX:b,outlineOffsetY:c,outlineBlur:e,outlineOpacity:f}=this;l=this._parsePercent(a)||0;d=Math.max(0,this._parsePercent(e)||0);k=f;t=this._parsePercent(b)||0;m=this._parsePercent(c)||0}else{if(e=Math.max(0,this._parsePercent(this.strokeWidth)||0)){var b=this.strokeColor;h.uTroikaStrokeColor.value.set(null==b?8421504:b);b=this.strokeOpacity;null==b&&(b=1)}k=this.fillOpacity}h.uTroikaEdgeOffset.value=l;h.uTroikaPositionOffset.value.set(t,
m);h.uTroikaBlurRadius.value=d;h.uTroikaStrokeWidth.value=e;h.uTroikaStrokeOpacity.value=b;h.uTroikaFillOpacity.value=null==k?1:k;h.uTroikaCurveRadius.value=this.curveRadius||0;(l=this.clipRect)&&Array.isArray(l)&&4===l.length?h.uTroikaClipRect.value.fromArray(l):(l=100*(this.fontSize||.1),h.uTroikaClipRect.value.set(c[0]-l,c[1]-l,c[2]+l,c[3]+l));this.geometry.applyClipRect(h.uTroikaClipRect.value)}h.uTroikaSDFDebug.value=!!this.debugSDF;e.polygonOffset=!!this.depthOffset;e.polygonOffsetFactor=e.polygonOffsetUnits=
this.depthOffset||0;f=f?this.outlineColor||0:this.color;null==f?delete e.color:(l=e.hasOwnProperty("color")?e.color:e.color=new z.Color,(f!==l._input||"object"===typeof f)&&l.set(l._input=f));f=this.orientation||"+x+y";if(f!==e._orientation){h=h.uTroikaOrient.value;f=f.replace(/[^-+xyz]/g,"");if(l="+x+y"!==f&&f.match(/^([-+])([xyz])([-+])([xyz])$/)){let [,a,b,d,e]=l;oa.set(0,0,0)[b]="-"===a?1:-1;wa.set(0,0,0)[e]="-"===d?-1:1;Na.lookAt(fb,oa.cross(wa),wa);h.setFromMatrix4(Na)}else h.identity();e._orientation=
f}}_parsePercent(e){"string"===typeof e&&(e=(e=e.match(/^(-?[\d.]+)%$/))?parseFloat(e[1]):NaN,e=(isNaN(e)?0:e/100)*this.fontSize);return e}localPositionToTextCoords(e,f=new z.Vector2){f.copy(e);let h=this.curveRadius;h&&(f.x=Math.atan2(e.x,Math.abs(h)-Math.abs(e.z))*Math.abs(h));return f}worldPositionToTextCoords(e,f=new z.Vector2){oa.copy(e);return this.localPositionToTextCoords(this.worldToLocal(oa),f)}raycast(e,f){let {textRenderInfo:h,curveRadius:l}=this;if(h){let b=h.blockBounds,a=l?Pa():Oa(),
c=a.geometry,{position:d,uv:g}=c.attributes;for(let a=0;a<g.count;a++){let c=b[0]+g.getX(a)*(b[2]-b[0]),e=b[1]+g.getY(a)*(b[3]-b[1]),f=0;l&&(f=l-Math.cos(c/l)*l,c=Math.sin(c/l)*l);d.setXYZ(a,c,e,f)}c.boundingSphere=this.geometry.boundingSphere;c.boundingBox=this.geometry.boundingBox;a.matrixWorld=this.matrixWorld;a.material.side=this.material.side;ha.length=0;a.raycast(e,ha);for(e=0;e<ha.length;e++)ha[e].object=this,f.push(ha[e])}}copy(e){let f=this.geometry;super.copy(e);this.geometry=f;ib.forEach(f=>
{this[f]=e[f]});return this}clone(){return(new this.constructor).copy(this)}}Qa.forEach(e=>{let f="_private_"+e;Object.defineProperty(ia.prototype,e,{get(){return this[f]},set(e){e!==this[f]&&(this[f]=e,this._needsSync=!0)}})});let jb={type:"syncstart"},kb={type:"synccomplete"},Ra=new z.Box3,Sa=new z.Color;class Ta extends ia{constructor(){super();this._members=new Map;this._dataTextures={};this._onMemberSynced=e=>{this._members.get(e.target).dirty=!0}}add(...e){for(let f=0;f<e.length;f++)e[f]instanceof
ia?this.addText(e[f]):super.add(e[f]);return this}remove(...e){for(let f=0;f<e.length;f++)e[f]instanceof ia?this.removeText(e[f]):super.remove(e[f]);return this}addText(e){this._members.has(e)||(this._members.set(e,{index:-1,glyphCount:-1,dirty:!0}),e.addEventListener("synccomplete",this._onMemberSynced))}removeText(e){this._needsRepack=!0;e.removeEventListener("synccomplete",this._onMemberSynced);this._members.delete(e)}createDerivedMaterial(e){return cb(e)}updateMatrixWorld(e){super.updateMatrixWorld(e);
this.updateBounds()}updateBounds(){let e=this.geometry.boundingBox.makeEmpty();this._members.forEach((f,h)=>{h.matrixAutoUpdate&&h.updateMatrix();Ra.copy(h.geometry.boundingBox).applyMatrix4(h.matrix);e.union(Ra)});e.getBoundingSphere(this.geometry.boundingSphere)}hasOutline(){for(let e of this._members.keys())if(e.hasOutline())return!0;return!1}_prepareForRender(e){let f=e.isTextOutlineMaterial;e.uniforms.uTroikaIsOutline.value=f;let h=this._dataTextures[f?"outline":"main"],l=Math.pow(2,Math.ceil(Math.log2(32*
this._members.size)));if(!h||l!==h.image.data.length){h&&h.dispose();let a=Math.min(l/4,1024);h=this._dataTextures[f?"outline":"main"]=new z.DataTexture(new Float32Array(l),a,l/4/a,z.RGBAFormat,z.FloatType)}let b=h.image.data,a=(a,d)=>{d!==b[a]&&(b[a]=d,h.needsUpdate=!0)};this._members.forEach(({index:b},d)=>{if(-1<b){b*=32;var c=d.matrix.elements;for(let d=0;16>d;d++)a(b+d,c[d]);d._prepareForRender(e);let {uTroikaTotalBounds:g,uTroikaClipRect:h,uTroikaPositionOffset:m,uTroikaEdgeOffset:l,uTroikaBlurRadius:y,
uTroikaStrokeWidth:x,uTroikaStrokeColor:q,uTroikaStrokeOpacity:n,uTroikaFillOpacity:r,uTroikaCurveRadius:v}=e.uniforms;for(c=0;4>c;c++)a(b+16+c,g.value.getComponent(c));for(c=0;4>c;c++)a(b+20+c,h.value.getComponent(c));d=f?d.outlineColor||0:d.color;null==d&&(d=this.color);null==d&&(d=this.material.color);null==d&&(d=16777215);a(b+24,Sa.set(d).getHex());a(b+25,r.value);a(b+26,v.value);f?(a(b+28,m.value.x),a(b+29,m.value.y),a(b+30,l.value),a(b+31,y.value)):(a(b+28,x.value),a(b+29,Sa.set(q.value).getHex()),
a(b+30,n.value))}});e.setMatrixTexture(h);super._prepareForRender(e)}sync(e){let f=this._needsRepack?[]:null;this._needsRepack=!1;this._members.forEach((e,l)=>{if(e.dirty||l._needsSync)e.dirty=!1,(f||(f=[])).push(new Promise(b=>{l._needsSync?l.sync(b):b()}))});f&&(this.dispatchEvent(jb),Promise.all(f).then(()=>{let {geometry:f}=this,l=f.attributes,b=l.aTroikaTextBatchMemberIndex&&l.aTroikaTextBatchMemberIndex.array||new Uint16Array(0),a=l.aTroikaGlyphIndex&&l.aTroikaGlyphIndex.array||new Float32Array(0),
c=l.aTroikaGlyphBounds&&l.aTroikaGlyphBounds.array||new Float32Array(0),d=0;this._members.forEach((a,{textRenderInfo:b})=>{b&&(d+=b.glyphAtlasIndices.length,this._textRenderInfo=b)});d!==b.length&&(b=sa(b,d),a=sa(a,d),c=sa(c,4*d));let g=0,k=0;this._members.forEach((d,{textRenderInfo:e})=>{if(e){let f=e.glyphAtlasIndices.length;b.fill(g,k,k+f);a.set(e.glyphAtlasIndices,k,k+f);c.set(e.glyphBounds,4*k,4*(k+f));k+=f;d.index=g++}});f.updateAttributeData("aTroikaTextBatchMemberIndex",b,1);f.getAttribute("aTroikaTextBatchMemberIndex").setUsage(z.DynamicDrawUsage);
f.updateAttributeData("aTroikaGlyphIndex",a,1);f.updateAttributeData("aTroikaGlyphBounds",c,4);this.updateBounds();this.dispatchEvent(kb);e&&e()}))}copy(e){e instanceof Ta&&(super.copy(e),this._members.forEach((e,h)=>this.removeText(h)),e._members.forEach((e,h)=>this.addText(h)));return this}dispose(){super.dispose();Object.values(this._dataTextures).forEach(e=>e.dispose())}}let Ua=new WeakMap,La=new WeakMap;J.BatchedText=Ta;J.GlyphsGeometry=Ma;J.Text=ia;J.configureTextBuilder=function(e){Da?console.warn("configureTextBuilder called after first font request; will be ignored."):
Ea(Q,e)};J.createTextDerivedMaterial=ra;J.dumpSDFTextures=function(){Object.keys(ma).forEach(e=>{e=ma[e].sdfCanvas;let {width:f,height:h}=e;console.log("%c.",`
      background: url(${e.toDataURL()});
      background-size: ${f}px ${h}px;
      color: transparent;
      font-size: 0;
      line-height: ${h}px;
      padding-left: ${f}px;
    `)})};J.fontResolverWorkerModule=R;J.getCaretAtPoint=function(e,f,h){let l=null,b=null;eb(e).forEach(a=>{if(!b||Math.abs(h-(a.top+a.bottom)/2)<Math.abs(h-(b.top+b.bottom)/2))b=a});b.carets.forEach(a=>{if(!l||Math.abs(f-a.x)<Math.abs(f-l.x))l=a});return l};J.getSelectionRects=function(e,f,h){var l;if(e){if((l=Ua.get(e))&&l.start===f&&l.end===h)return l.rects;var {caretPositions:b}=e;h<f&&(l=f,f=h,h=l);f=Math.max(f,0);h=Math.min(h,b.length+1);l=[];var a=null;for(var c=f;c<h;c++){var d=b[4*c],g=
b[4*c+1];let e=Math.min(d,g);d=Math.max(d,g);g=b[4*c+2];let f=b[4*c+3];if(!a||g!==a.bottom||f!==a.top||e>a.right||d<a.left)a={left:Infinity,right:-Infinity,bottom:g,top:f},l.push(a);a.left=Math.min(e,a.left);a.right=Math.max(d,a.right)}l.sort((a,b)=>b.bottom-a.bottom||a.left-b.left);for(b=l.length-1;0<b--;)a=l[b],c=l[b+1],a.bottom===c.bottom&&a.top===c.top&&a.left<=c.right&&a.right>=c.left&&(c.left=Math.min(c.left,a.left),c.right=Math.max(c.right,a.right),l.splice(b,1));Ua.set(e,{start:f,end:h,rects:l})}return l};
J.getTextRenderInfo=qa;J.preloadFont=function({font:e,characters:f,sdfGlyphSize:h},l){f=Array.isArray(f)?f.join("\n"):""+f;qa({font:e,sdfGlyphSize:h,text:f},l)};J.typesetterWorkerModule=X;Object.defineProperty(J,"__esModule",{value:!0})})
