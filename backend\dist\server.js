"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const mongoose_1 = __importDefault(require("mongoose"));
const dotenv_1 = __importDefault(require("dotenv"));
const cors_1 = __importDefault(require("cors")); // Import CORS middleware
const articleRoutes_1 = __importDefault(require("./routes/articleRoutes")); // Import article routes
const photoRoutes_1 = __importDefault(require("./routes/photoRoutes")); // Import photo routes
const videoRoutes_1 = __importDefault(require("./routes/videoRoutes")); // Import video routes
const userRoutes_1 = __importDefault(require("./routes/userRoutes")); // Import user routes
const settingRoutes_1 = __importDefault(require("./routes/settingRoutes")); // Import setting routes
const authRoutes_1 = __importDefault(require("./routes/authRoutes")); // Import auth routes
const analyticRoutes_1 = __importDefault(require("./routes/analyticRoutes")); // Import analytic routes
const experienceRoutes_1 = __importDefault(require("./routes/experienceRoutes")); // Import experience routes
const notificationRoutes_1 = __importDefault(require("./routes/notificationRoutes")); // Import notification routes
const placeRoutes_1 = __importDefault(require("./routes/placeRoutes")); // Import place routes
const vrSyncService_1 = require("./services/vrSyncService"); // Import VR sync service
dotenv_1.default.config();
const app = (0, express_1.default)();
const port = process.env.PORT || 5000;
// Middleware
app.use(express_1.default.json({ limit: '100mb' })); // Increase JSON payload limit
app.use(express_1.default.urlencoded({ extended: true, limit: '100mb' })); // Increase URL-encoded payload limit
// Enhanced CORS configuration for file uploads
app.use((0, cors_1.default)({
    origin: [
        'http://localhost:8080', 'http://127.0.0.1:8080', // Admin frontend
        'http://localhost:8081', 'http://127.0.0.1:8081', // Djerba frontend
        'http://localhost:8082', 'http://127.0.0.1:8082', // Admin/Djerba frontend (alternate port)
        'http://localhost:8083', 'http://127.0.0.1:8083' // Djerba frontend (alternate port)
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    maxAge: 86400 // Cache preflight for 24 hours
}));
// Serve VR projects from VRproj folder
app.use('/vr', express_1.default.static('VRproj'));
// MongoDB Connection
mongoose_1.default.connect(process.env.MONGODB_URI)
    .then(() => __awaiter(void 0, void 0, void 0, function* () {
    console.log('MongoDB connected');
    // Sync VR projects after database connection
    setTimeout(() => __awaiter(void 0, void 0, void 0, function* () {
        console.log('Starting VR projects sync...');
        yield (0, vrSyncService_1.autoSyncVRProjects)();
    }), 2000); // Wait 2 seconds after server start
}))
    .catch((err) => console.error('MongoDB connection error:', err));
// Routes
app.use('/api/articles', articleRoutes_1.default); // Use article routes
app.use('/api/photos', photoRoutes_1.default); // Use photo routes
app.use('/api/videos', videoRoutes_1.default); // Use video routes
// Log request body before user routes
app.use('/api/users', (req, res, next) => {
    console.log('Request to /api/users. Method:', req.method);
    console.log('Request Body:', req.body);
    next();
}, userRoutes_1.default); // Use user routes
app.use('/api/settings', settingRoutes_1.default); // Use setting routes
app.use('/api/auth', authRoutes_1.default); // Use auth routes
app.use('/api/analytics', analyticRoutes_1.default); // Use analytic routes
// Log requests to experiences endpoint
app.use('/api/experiences', (req, res, next) => {
    console.log(`Request to /api/experiences${req.path}. Method: ${req.method}`);
    console.log('Headers:', req.headers);
    if (req.method === 'POST') {
        console.log('Body keys:', Object.keys(req.body || {}));
        console.log('Files:', req.files ? `${req.files.length} files` : 'No files');
    }
    next();
}, experienceRoutes_1.default); // Use experience routes
app.use('/api/notifications', notificationRoutes_1.default); // Use notification routes
app.use('/api/places', placeRoutes_1.default); // Use place routes
// Basic route
app.get('/', (req, res) => {
    res.send('Backend is running!');
});
// Test route for VR upload connectivity
app.post('/api/test-upload', (req, res) => {
    console.log('Test upload endpoint hit');
    res.json({ message: 'Upload endpoint is working', timestamp: new Date().toISOString() });
});
app.listen(port, () => {
    console.log(`Server running on port ${port}`);
});
