"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const ExperienceSchema = new mongoose_1.Schema({
    title: { type: String, required: true },
    description: { type: String },
    status: { type: String, enum: ['draft', 'published', 'archived'], default: 'draft' },
    category: { type: String },
    tags: [{ type: String }],
    isFeatured: { type: Boolean, default: false },
    isVR: { type: Boolean, default: false },
    isAR: { type: Boolean, default: false },
    vrProject: {
        folderPath: { type: String }, // Path to VR project folder in VRproj directory
        mainFile: { type: String }, // Name of main HTML file (e.g., "index.html")
        description: { type: String }, // Description of the VR project
        controls: { type: String }, // Instructions for VR controls
    },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
});
// Update the updatedAt field on save
ExperienceSchema.pre('save', function (next) {
    this.updatedAt = new Date();
    next();
});
const Experience = mongoose_1.default.model('Experience', ExperienceSchema);
exports.default = Experience;
