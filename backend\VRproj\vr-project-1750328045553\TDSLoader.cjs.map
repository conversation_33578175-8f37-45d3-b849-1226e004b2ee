{"version": 3, "file": "TDSLoader.cjs", "sources": ["../../src/loaders/TDSLoader.js"], "sourcesContent": ["import {\n  AdditiveBlending,\n  BufferGeometry,\n  Color,\n  DoubleSide,\n  FileLoader,\n  Float32BufferAttribute,\n  Group,\n  Loader,\n  LoaderUtils,\n  Matrix4,\n  Mesh,\n  MeshPhongMaterial,\n  TextureLoader,\n} from 'three'\n\n/**\n * Autodesk 3DS three.js file loader, based on lib3ds.\n *\n * Loads geometry with uv and materials basic properties with texture support.\n *\n * @class TDSLoader\n * @constructor\n */\n\nclass TDSLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n\n    this.debug = false\n\n    this.group = null\n    this.position = 0\n\n    this.materials = []\n    this.meshes = []\n  }\n\n  /**\n   * Load 3ds file from url.\n   *\n   * @method load\n   * @param {[type]} url URL for the file.\n   * @param {Function} onLoad onLoad callback, receives group Object3D as argument.\n   * @param {Function} onProgress onProgress callback.\n   * @param {Function} onError onError callback.\n   */\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const path = this.path === '' ? LoaderUtils.extractUrlBase(url) : this.path\n\n    const loader = new FileLoader(this.manager)\n    loader.setPath(this.path)\n    loader.setResponseType('arraybuffer')\n    loader.setRequestHeader(this.requestHeader)\n    loader.setWithCredentials(this.withCredentials)\n\n    loader.load(\n      url,\n      function (data) {\n        try {\n          onLoad(scope.parse(data, path))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  /**\n   * Parse arraybuffer data and load 3ds file.\n   *\n   * @method parse\n   * @param {ArrayBuffer} arraybuffer Arraybuffer data to be loaded.\n   * @param {String} path Path for external resources.\n   * @return {Group} Group loaded from 3ds file.\n   */\n  parse(arraybuffer, path) {\n    this.group = new Group()\n    this.position = 0\n    this.materials = []\n    this.meshes = []\n\n    this.readFile(arraybuffer, path)\n\n    for (let i = 0; i < this.meshes.length; i++) {\n      this.group.add(this.meshes[i])\n    }\n\n    return this.group\n  }\n\n  /**\n   * Decode file content to read 3ds data.\n   *\n   * @method readFile\n   * @param {ArrayBuffer} arraybuffer Arraybuffer data to be loaded.\n   * @param {String} path Path for external resources.\n   */\n  readFile(arraybuffer, path) {\n    const data = new DataView(arraybuffer)\n    const chunk = this.readChunk(data)\n\n    if (chunk.id === MLIBMAGIC || chunk.id === CMAGIC || chunk.id === M3DMAGIC) {\n      let next = this.nextChunk(data, chunk)\n\n      while (next !== 0) {\n        if (next === M3D_VERSION) {\n          const version = this.readDWord(data)\n          this.debugMessage('3DS file version: ' + version)\n        } else if (next === MDATA) {\n          this.resetPosition(data)\n          this.readMeshData(data, path)\n        } else {\n          this.debugMessage('Unknown main chunk: ' + next.toString(16))\n        }\n\n        next = this.nextChunk(data, chunk)\n      }\n    }\n\n    this.debugMessage('Parsed ' + this.meshes.length + ' meshes')\n  }\n\n  /**\n   * Read mesh data chunk.\n   *\n   * @method readMeshData\n   * @param {Dataview} data Dataview in use.\n   * @param {String} path Path for external resources.\n   */\n  readMeshData(data, path) {\n    const chunk = this.readChunk(data)\n    let next = this.nextChunk(data, chunk)\n\n    while (next !== 0) {\n      if (next === MESH_VERSION) {\n        const version = +this.readDWord(data)\n        this.debugMessage('Mesh Version: ' + version)\n      } else if (next === MASTER_SCALE) {\n        const scale = this.readFloat(data)\n        this.debugMessage('Master scale: ' + scale)\n        this.group.scale.set(scale, scale, scale)\n      } else if (next === NAMED_OBJECT) {\n        this.debugMessage('Named Object')\n        this.resetPosition(data)\n        this.readNamedObject(data)\n      } else if (next === MAT_ENTRY) {\n        this.debugMessage('Material')\n        this.resetPosition(data)\n        this.readMaterialEntry(data, path)\n      } else {\n        this.debugMessage('Unknown MDATA chunk: ' + next.toString(16))\n      }\n\n      next = this.nextChunk(data, chunk)\n    }\n  }\n\n  /**\n   * Read named object chunk.\n   *\n   * @method readNamedObject\n   * @param {Dataview} data Dataview in use.\n   */\n  readNamedObject(data) {\n    const chunk = this.readChunk(data)\n    const name = this.readString(data, 64)\n    chunk.cur = this.position\n\n    let next = this.nextChunk(data, chunk)\n    while (next !== 0) {\n      if (next === N_TRI_OBJECT) {\n        this.resetPosition(data)\n        const mesh = this.readMesh(data)\n        mesh.name = name\n        this.meshes.push(mesh)\n      } else {\n        this.debugMessage('Unknown named object chunk: ' + next.toString(16))\n      }\n\n      next = this.nextChunk(data, chunk)\n    }\n\n    this.endChunk(chunk)\n  }\n\n  /**\n   * Read material data chunk and add it to the material list.\n   *\n   * @method readMaterialEntry\n   * @param {Dataview} data Dataview in use.\n   * @param {String} path Path for external resources.\n   */\n  readMaterialEntry(data, path) {\n    const chunk = this.readChunk(data)\n    let next = this.nextChunk(data, chunk)\n    const material = new MeshPhongMaterial()\n\n    while (next !== 0) {\n      if (next === MAT_NAME) {\n        material.name = this.readString(data, 64)\n        this.debugMessage('   Name: ' + material.name)\n      } else if (next === MAT_WIRE) {\n        this.debugMessage('   Wireframe')\n        material.wireframe = true\n      } else if (next === MAT_WIRE_SIZE) {\n        const value = this.readByte(data)\n        material.wireframeLinewidth = value\n        this.debugMessage('   Wireframe Thickness: ' + value)\n      } else if (next === MAT_TWO_SIDE) {\n        material.side = DoubleSide\n        this.debugMessage('   DoubleSided')\n      } else if (next === MAT_ADDITIVE) {\n        this.debugMessage('   Additive Blending')\n        material.blending = AdditiveBlending\n      } else if (next === MAT_DIFFUSE) {\n        this.debugMessage('   Diffuse Color')\n        material.color = this.readColor(data)\n      } else if (next === MAT_SPECULAR) {\n        this.debugMessage('   Specular Color')\n        material.specular = this.readColor(data)\n      } else if (next === MAT_AMBIENT) {\n        this.debugMessage('   Ambient color')\n        material.color = this.readColor(data)\n      } else if (next === MAT_SHININESS) {\n        const shininess = this.readPercentage(data)\n        material.shininess = shininess * 100\n        this.debugMessage('   Shininess : ' + shininess)\n      } else if (next === MAT_TRANSPARENCY) {\n        const transparency = this.readPercentage(data)\n        material.opacity = 1 - transparency\n        this.debugMessage('  Transparency : ' + transparency)\n        material.transparent = material.opacity < 1 ? true : false\n      } else if (next === MAT_TEXMAP) {\n        this.debugMessage('   ColorMap')\n        this.resetPosition(data)\n        material.map = this.readMap(data, path)\n      } else if (next === MAT_BUMPMAP) {\n        this.debugMessage('   BumpMap')\n        this.resetPosition(data)\n        material.bumpMap = this.readMap(data, path)\n      } else if (next === MAT_OPACMAP) {\n        this.debugMessage('   OpacityMap')\n        this.resetPosition(data)\n        material.alphaMap = this.readMap(data, path)\n      } else if (next === MAT_SPECMAP) {\n        this.debugMessage('   SpecularMap')\n        this.resetPosition(data)\n        material.specularMap = this.readMap(data, path)\n      } else {\n        this.debugMessage('   Unknown material chunk: ' + next.toString(16))\n      }\n\n      next = this.nextChunk(data, chunk)\n    }\n\n    this.endChunk(chunk)\n\n    this.materials[material.name] = material\n  }\n\n  /**\n   * Read mesh data chunk.\n   *\n   * @method readMesh\n   * @param {Dataview} data Dataview in use.\n   * @return {Mesh} The parsed mesh.\n   */\n  readMesh(data) {\n    const chunk = this.readChunk(data)\n    let next = this.nextChunk(data, chunk)\n\n    const geometry = new BufferGeometry()\n\n    const material = new MeshPhongMaterial()\n    const mesh = new Mesh(geometry, material)\n    mesh.name = 'mesh'\n\n    while (next !== 0) {\n      if (next === POINT_ARRAY) {\n        const points = this.readWord(data)\n\n        this.debugMessage('   Vertex: ' + points)\n\n        //BufferGeometry\n\n        const vertices = []\n\n        for (let i = 0; i < points; i++) {\n          vertices.push(this.readFloat(data))\n          vertices.push(this.readFloat(data))\n          vertices.push(this.readFloat(data))\n        }\n\n        geometry.setAttribute('position', new Float32BufferAttribute(vertices, 3))\n      } else if (next === FACE_ARRAY) {\n        this.resetPosition(data)\n        this.readFaceArray(data, mesh)\n      } else if (next === TEX_VERTS) {\n        const texels = this.readWord(data)\n\n        this.debugMessage('   UV: ' + texels)\n\n        //BufferGeometry\n\n        const uvs = []\n\n        for (let i = 0; i < texels; i++) {\n          uvs.push(this.readFloat(data))\n          uvs.push(this.readFloat(data))\n        }\n\n        geometry.setAttribute('uv', new Float32BufferAttribute(uvs, 2))\n      } else if (next === MESH_MATRIX) {\n        this.debugMessage('   Tranformation Matrix (TODO)')\n\n        const values = []\n        for (let i = 0; i < 12; i++) {\n          values[i] = this.readFloat(data)\n        }\n\n        const matrix = new Matrix4()\n\n        //X Line\n        matrix.elements[0] = values[0]\n        matrix.elements[1] = values[6]\n        matrix.elements[2] = values[3]\n        matrix.elements[3] = values[9]\n\n        //Y Line\n        matrix.elements[4] = values[2]\n        matrix.elements[5] = values[8]\n        matrix.elements[6] = values[5]\n        matrix.elements[7] = values[11]\n\n        //Z Line\n        matrix.elements[8] = values[1]\n        matrix.elements[9] = values[7]\n        matrix.elements[10] = values[4]\n        matrix.elements[11] = values[10]\n\n        //W Line\n        matrix.elements[12] = 0\n        matrix.elements[13] = 0\n        matrix.elements[14] = 0\n        matrix.elements[15] = 1\n\n        matrix.transpose()\n\n        const inverse = new Matrix4()\n        inverse.copy(matrix).invert()\n        geometry.applyMatrix4(inverse)\n\n        matrix.decompose(mesh.position, mesh.quaternion, mesh.scale)\n      } else {\n        this.debugMessage('   Unknown mesh chunk: ' + next.toString(16))\n      }\n\n      next = this.nextChunk(data, chunk)\n    }\n\n    this.endChunk(chunk)\n\n    geometry.computeVertexNormals()\n\n    return mesh\n  }\n\n  /**\n   * Read face array data chunk.\n   *\n   * @method readFaceArray\n   * @param {Dataview} data Dataview in use.\n   * @param {Mesh} mesh Mesh to be filled with the data read.\n   */\n  readFaceArray(data, mesh) {\n    const chunk = this.readChunk(data)\n    const faces = this.readWord(data)\n\n    this.debugMessage('   Faces: ' + faces)\n\n    const index = []\n\n    for (let i = 0; i < faces; ++i) {\n      index.push(this.readWord(data), this.readWord(data), this.readWord(data))\n\n      this.readWord(data) // visibility\n    }\n\n    mesh.geometry.setIndex(index)\n\n    //The rest of the FACE_ARRAY chunk is subchunks\n\n    let materialIndex = 0\n    let start = 0\n\n    while (this.position < chunk.end) {\n      const subchunk = this.readChunk(data)\n\n      if (subchunk.id === MSH_MAT_GROUP) {\n        this.debugMessage('      Material Group')\n\n        this.resetPosition(data)\n\n        const group = this.readMaterialGroup(data)\n        const count = group.index.length * 3 // assuming successive indices\n\n        mesh.geometry.addGroup(start, count, materialIndex)\n\n        start += count\n        materialIndex++\n\n        const material = this.materials[group.name]\n\n        if (Array.isArray(mesh.material) === false) mesh.material = []\n\n        if (material !== undefined) {\n          mesh.material.push(material)\n        }\n      } else {\n        this.debugMessage('      Unknown face array chunk: ' + subchunk.toString(16))\n      }\n\n      this.endChunk(subchunk)\n    }\n\n    if (mesh.material.length === 1) mesh.material = mesh.material[0] // for backwards compatibility\n\n    this.endChunk(chunk)\n  }\n\n  /**\n   * Read texture map data chunk.\n   *\n   * @method readMap\n   * @param {Dataview} data Dataview in use.\n   * @param {String} path Path for external resources.\n   * @return {Texture} Texture read from this data chunk.\n   */\n  readMap(data, path) {\n    const chunk = this.readChunk(data)\n    let next = this.nextChunk(data, chunk)\n    let texture = {}\n\n    const loader = new TextureLoader(this.manager)\n    loader.setPath(this.resourcePath || path).setCrossOrigin(this.crossOrigin)\n\n    while (next !== 0) {\n      if (next === MAT_MAPNAME) {\n        const name = this.readString(data, 128)\n        texture = loader.load(name)\n\n        this.debugMessage('      File: ' + path + name)\n      } else if (next === MAT_MAP_UOFFSET) {\n        texture.offset.x = this.readFloat(data)\n        this.debugMessage('      OffsetX: ' + texture.offset.x)\n      } else if (next === MAT_MAP_VOFFSET) {\n        texture.offset.y = this.readFloat(data)\n        this.debugMessage('      OffsetY: ' + texture.offset.y)\n      } else if (next === MAT_MAP_USCALE) {\n        texture.repeat.x = this.readFloat(data)\n        this.debugMessage('      RepeatX: ' + texture.repeat.x)\n      } else if (next === MAT_MAP_VSCALE) {\n        texture.repeat.y = this.readFloat(data)\n        this.debugMessage('      RepeatY: ' + texture.repeat.y)\n      } else {\n        this.debugMessage('      Unknown map chunk: ' + next.toString(16))\n      }\n\n      next = this.nextChunk(data, chunk)\n    }\n\n    this.endChunk(chunk)\n\n    return texture\n  }\n\n  /**\n   * Read material group data chunk.\n   *\n   * @method readMaterialGroup\n   * @param {Dataview} data Dataview in use.\n   * @return {Object} Object with name and index of the object.\n   */\n  readMaterialGroup(data) {\n    this.readChunk(data)\n    const name = this.readString(data, 64)\n    const numFaces = this.readWord(data)\n\n    this.debugMessage('         Name: ' + name)\n    this.debugMessage('         Faces: ' + numFaces)\n\n    const index = []\n    for (let i = 0; i < numFaces; ++i) {\n      index.push(this.readWord(data))\n    }\n\n    return { name: name, index: index }\n  }\n\n  /**\n   * Read a color value.\n   *\n   * @method readColor\n   * @param {DataView} data Dataview.\n   * @return {Color} Color value read..\n   */\n  readColor(data) {\n    const chunk = this.readChunk(data)\n    const color = new Color()\n\n    if (chunk.id === COLOR_24 || chunk.id === LIN_COLOR_24) {\n      const r = this.readByte(data)\n      const g = this.readByte(data)\n      const b = this.readByte(data)\n\n      color.setRGB(r / 255, g / 255, b / 255)\n\n      this.debugMessage('      Color: ' + color.r + ', ' + color.g + ', ' + color.b)\n    } else if (chunk.id === COLOR_F || chunk.id === LIN_COLOR_F) {\n      const r = this.readFloat(data)\n      const g = this.readFloat(data)\n      const b = this.readFloat(data)\n\n      color.setRGB(r, g, b)\n\n      this.debugMessage('      Color: ' + color.r + ', ' + color.g + ', ' + color.b)\n    } else {\n      this.debugMessage('      Unknown color chunk: ' + chunk.toString(16))\n    }\n\n    this.endChunk(chunk)\n    return color\n  }\n\n  /**\n   * Read next chunk of data.\n   *\n   * @method readChunk\n   * @param {DataView} data Dataview.\n   * @return {Object} Chunk of data read.\n   */\n  readChunk(data) {\n    const chunk = {}\n\n    chunk.cur = this.position\n    chunk.id = this.readWord(data)\n    chunk.size = this.readDWord(data)\n    chunk.end = chunk.cur + chunk.size\n    chunk.cur += 6\n\n    return chunk\n  }\n\n  /**\n   * Set position to the end of the current chunk of data.\n   *\n   * @method endChunk\n   * @param {Object} chunk Data chunk.\n   */\n  endChunk(chunk) {\n    this.position = chunk.end\n  }\n\n  /**\n   * Move to the next data chunk.\n   *\n   * @method nextChunk\n   * @param {DataView} data Dataview.\n   * @param {Object} chunk Data chunk.\n   */\n  nextChunk(data, chunk) {\n    if (chunk.cur >= chunk.end) {\n      return 0\n    }\n\n    this.position = chunk.cur\n\n    try {\n      const next = this.readChunk(data)\n      chunk.cur += next.size\n      return next.id\n    } catch (e) {\n      this.debugMessage('Unable to read chunk at ' + this.position)\n      return 0\n    }\n  }\n\n  /**\n   * Reset dataview position.\n   *\n   * @method resetPosition\n   */\n  resetPosition() {\n    this.position -= 6\n  }\n\n  /**\n   * Read byte value.\n   *\n   * @method readByte\n   * @param {DataView} data Dataview to read data from.\n   * @return {Number} Data read from the dataview.\n   */\n  readByte(data) {\n    const v = data.getUint8(this.position, true)\n    this.position += 1\n    return v\n  }\n\n  /**\n   * Read 32 bit float value.\n   *\n   * @method readFloat\n   * @param {DataView} data Dataview to read data from.\n   * @return {Number} Data read from the dataview.\n   */\n  readFloat(data) {\n    try {\n      const v = data.getFloat32(this.position, true)\n      this.position += 4\n      return v\n    } catch (e) {\n      this.debugMessage(e + ' ' + this.position + ' ' + data.byteLength)\n    }\n  }\n\n  /**\n   * Read 32 bit signed integer value.\n   *\n   * @method readInt\n   * @param {DataView} data Dataview to read data from.\n   * @return {Number} Data read from the dataview.\n   */\n  readInt(data) {\n    const v = data.getInt32(this.position, true)\n    this.position += 4\n    return v\n  }\n\n  /**\n   * Read 16 bit signed integer value.\n   *\n   * @method readShort\n   * @param {DataView} data Dataview to read data from.\n   * @return {Number} Data read from the dataview.\n   */\n  readShort(data) {\n    const v = data.getInt16(this.position, true)\n    this.position += 2\n    return v\n  }\n\n  /**\n   * Read 64 bit unsigned integer value.\n   *\n   * @method readDWord\n   * @param {DataView} data Dataview to read data from.\n   * @return {Number} Data read from the dataview.\n   */\n  readDWord(data) {\n    const v = data.getUint32(this.position, true)\n    this.position += 4\n    return v\n  }\n\n  /**\n   * Read 32 bit unsigned integer value.\n   *\n   * @method readWord\n   * @param {DataView} data Dataview to read data from.\n   * @return {Number} Data read from the dataview.\n   */\n  readWord(data) {\n    const v = data.getUint16(this.position, true)\n    this.position += 2\n    return v\n  }\n\n  /**\n   * Read string value.\n   *\n   * @method readString\n   * @param {DataView} data Dataview to read data from.\n   * @param {Number} maxLength Max size of the string to be read.\n   * @return {String} Data read from the dataview.\n   */\n  readString(data, maxLength) {\n    let s = ''\n\n    for (let i = 0; i < maxLength; i++) {\n      const c = this.readByte(data)\n      if (!c) {\n        break\n      }\n\n      s += String.fromCharCode(c)\n    }\n\n    return s\n  }\n\n  /**\n   * Read percentage value.\n   *\n   * @method readPercentage\n   * @param {DataView} data Dataview to read data from.\n   * @return {Number} Data read from the dataview.\n   */\n  readPercentage(data) {\n    const chunk = this.readChunk(data)\n    let value\n\n    switch (chunk.id) {\n      case INT_PERCENTAGE:\n        value = this.readShort(data) / 100\n        break\n\n      case FLOAT_PERCENTAGE:\n        value = this.readFloat(data)\n        break\n\n      default:\n        this.debugMessage('      Unknown percentage chunk: ' + chunk.toString(16))\n    }\n\n    this.endChunk(chunk)\n\n    return value\n  }\n\n  /**\n   * Print debug message to the console.\n   *\n   * Is controlled by a flag to show or hide debug messages.\n   *\n   * @method debugMessage\n   * @param {Object} message Debug message to print to the console.\n   */\n  debugMessage(message) {\n    if (this.debug) {\n      console.log(message)\n    }\n  }\n}\n\n// const NULL_CHUNK = 0x0000;\nconst M3DMAGIC = 0x4d4d\n// const SMAGIC = 0x2D2D;\n// const LMAGIC = 0x2D3D;\nconst MLIBMAGIC = 0x3daa\n// const MATMAGIC = 0x3DFF;\nconst CMAGIC = 0xc23d\nconst M3D_VERSION = 0x0002\n// const M3D_KFVERSION = 0x0005;\nconst COLOR_F = 0x0010\nconst COLOR_24 = 0x0011\nconst LIN_COLOR_24 = 0x0012\nconst LIN_COLOR_F = 0x0013\nconst INT_PERCENTAGE = 0x0030\nconst FLOAT_PERCENTAGE = 0x0031\nconst MDATA = 0x3d3d\nconst MESH_VERSION = 0x3d3e\nconst MASTER_SCALE = 0x0100\n// const LO_SHADOW_BIAS = 0x1400;\n// const HI_SHADOW_BIAS = 0x1410;\n// const SHADOW_MAP_SIZE = 0x1420;\n// const SHADOW_SAMPLES = 0x1430;\n// const SHADOW_RANGE = 0x1440;\n// const SHADOW_FILTER = 0x1450;\n// const RAY_BIAS = 0x1460;\n// const O_CONSTS = 0x1500;\n// const AMBIENT_LIGHT = 0x2100;\n// const BIT_MAP = 0x1100;\n// const SOLID_BGND = 0x1200;\n// const V_GRADIENT = 0x1300;\n// const USE_BIT_MAP = 0x1101;\n// const USE_SOLID_BGND = 0x1201;\n// const USE_V_GRADIENT = 0x1301;\n// const FOG = 0x2200;\n// const FOG_BGND = 0x2210;\n// const LAYER_FOG = 0x2302;\n// const DISTANCE_CUE = 0x2300;\n// const DCUE_BGND = 0x2310;\n// const USE_FOG = 0x2201;\n// const USE_LAYER_FOG = 0x2303;\n// const USE_DISTANCE_CUE = 0x2301;\nconst MAT_ENTRY = 0xafff\nconst MAT_NAME = 0xa000\nconst MAT_AMBIENT = 0xa010\nconst MAT_DIFFUSE = 0xa020\nconst MAT_SPECULAR = 0xa030\nconst MAT_SHININESS = 0xa040\n// const MAT_SHIN2PCT = 0xA041;\nconst MAT_TRANSPARENCY = 0xa050\n// const MAT_XPFALL = 0xA052;\n// const MAT_USE_XPFALL = 0xA240;\n// const MAT_REFBLUR = 0xA053;\n// const MAT_SHADING = 0xA100;\n// const MAT_USE_REFBLUR = 0xA250;\n// const MAT_SELF_ILLUM = 0xA084;\nconst MAT_TWO_SIDE = 0xa081\n// const MAT_DECAL = 0xA082;\nconst MAT_ADDITIVE = 0xa083\nconst MAT_WIRE = 0xa085\n// const MAT_FACEMAP = 0xA088;\n// const MAT_TRANSFALLOFF_IN = 0xA08A;\n// const MAT_PHONGSOFT = 0xA08C;\n// const MAT_WIREABS = 0xA08E;\nconst MAT_WIRE_SIZE = 0xa087\nconst MAT_TEXMAP = 0xa200\n// const MAT_SXP_TEXT_DATA = 0xA320;\n// const MAT_TEXMASK = 0xA33E;\n// const MAT_SXP_TEXTMASK_DATA = 0xA32A;\n// const MAT_TEX2MAP = 0xA33A;\n// const MAT_SXP_TEXT2_DATA = 0xA321;\n// const MAT_TEX2MASK = 0xA340;\n// const MAT_SXP_TEXT2MASK_DATA = 0xA32C;\nconst MAT_OPACMAP = 0xa210\n// const MAT_SXP_OPAC_DATA = 0xA322;\n// const MAT_OPACMASK = 0xA342;\n// const MAT_SXP_OPACMASK_DATA = 0xA32E;\nconst MAT_BUMPMAP = 0xa230\n// const MAT_SXP_BUMP_DATA = 0xA324;\n// const MAT_BUMPMASK = 0xA344;\n// const MAT_SXP_BUMPMASK_DATA = 0xA330;\nconst MAT_SPECMAP = 0xa204\n// const MAT_SXP_SPEC_DATA = 0xA325;\n// const MAT_SPECMASK = 0xA348;\n// const MAT_SXP_SPECMASK_DATA = 0xA332;\n// const MAT_SHINMAP = 0xA33C;\n// const MAT_SXP_SHIN_DATA = 0xA326;\n// const MAT_SHINMASK = 0xA346;\n// const MAT_SXP_SHINMASK_DATA = 0xA334;\n// const MAT_SELFIMAP = 0xA33D;\n// const MAT_SXP_SELFI_DATA = 0xA328;\n// const MAT_SELFIMASK = 0xA34A;\n// const MAT_SXP_SELFIMASK_DATA = 0xA336;\n// const MAT_REFLMAP = 0xA220;\n// const MAT_REFLMASK = 0xA34C;\n// const MAT_SXP_REFLMASK_DATA = 0xA338;\n// const MAT_ACUBIC = 0xA310;\nconst MAT_MAPNAME = 0xa300\n// const MAT_MAP_TILING = 0xA351;\n// const MAT_MAP_TEXBLUR = 0xA353;\nconst MAT_MAP_USCALE = 0xa354\nconst MAT_MAP_VSCALE = 0xa356\nconst MAT_MAP_UOFFSET = 0xa358\nconst MAT_MAP_VOFFSET = 0xa35a\n// const MAT_MAP_ANG = 0xA35C;\n// const MAT_MAP_COL1 = 0xA360;\n// const MAT_MAP_COL2 = 0xA362;\n// const MAT_MAP_RCOL = 0xA364;\n// const MAT_MAP_GCOL = 0xA366;\n// const MAT_MAP_BCOL = 0xA368;\nconst NAMED_OBJECT = 0x4000\n// const N_DIRECT_LIGHT = 0x4600;\n// const DL_OFF = 0x4620;\n// const DL_OUTER_RANGE = 0x465A;\n// const DL_INNER_RANGE = 0x4659;\n// const DL_MULTIPLIER = 0x465B;\n// const DL_EXCLUDE = 0x4654;\n// const DL_ATTENUATE = 0x4625;\n// const DL_SPOTLIGHT = 0x4610;\n// const DL_SPOT_ROLL = 0x4656;\n// const DL_SHADOWED = 0x4630;\n// const DL_LOCAL_SHADOW2 = 0x4641;\n// const DL_SEE_CONE = 0x4650;\n// const DL_SPOT_RECTANGULAR = 0x4651;\n// const DL_SPOT_ASPECT = 0x4657;\n// const DL_SPOT_PROJECTOR = 0x4653;\n// const DL_SPOT_OVERSHOOT = 0x4652;\n// const DL_RAY_BIAS = 0x4658;\n// const DL_RAYSHAD = 0x4627;\n// const N_CAMERA = 0x4700;\n// const CAM_SEE_CONE = 0x4710;\n// const CAM_RANGES = 0x4720;\n// const OBJ_HIDDEN = 0x4010;\n// const OBJ_VIS_LOFTER = 0x4011;\n// const OBJ_DOESNT_CAST = 0x4012;\n// const OBJ_DONT_RECVSHADOW = 0x4017;\n// const OBJ_MATTE = 0x4013;\n// const OBJ_FAST = 0x4014;\n// const OBJ_PROCEDURAL = 0x4015;\n// const OBJ_FROZEN = 0x4016;\nconst N_TRI_OBJECT = 0x4100\nconst POINT_ARRAY = 0x4110\n// const POINT_FLAG_ARRAY = 0x4111;\nconst FACE_ARRAY = 0x4120\nconst MSH_MAT_GROUP = 0x4130\n// const SMOOTH_GROUP = 0x4150;\n// const MSH_BOXMAP = 0x4190;\nconst TEX_VERTS = 0x4140\nconst MESH_MATRIX = 0x4160\n// const MESH_COLOR = 0x4165;\n// const MESH_TEXTURE_INFO = 0x4170;\n// const KFDATA = 0xB000;\n// const KFHDR = 0xB00A;\n// const KFSEG = 0xB008;\n// const KFCURTIME = 0xB009;\n// const AMBIENT_NODE_TAG = 0xB001;\n// const OBJECT_NODE_TAG = 0xB002;\n// const CAMERA_NODE_TAG = 0xB003;\n// const TARGET_NODE_TAG = 0xB004;\n// const LIGHT_NODE_TAG = 0xB005;\n// const L_TARGET_NODE_TAG = 0xB006;\n// const SPOTLIGHT_NODE_TAG = 0xB007;\n// const NODE_ID = 0xB030;\n// const NODE_HDR = 0xB010;\n// const PIVOT = 0xB013;\n// const INSTANCE_NAME = 0xB011;\n// const MORPH_SMOOTH = 0xB015;\n// const BOUNDBOX = 0xB014;\n// const POS_TRACK_TAG = 0xB020;\n// const COL_TRACK_TAG = 0xB025;\n// const ROT_TRACK_TAG = 0xB021;\n// const SCL_TRACK_TAG = 0xB022;\n// const MORPH_TRACK_TAG = 0xB026;\n// const FOV_TRACK_TAG = 0xB023;\n// const ROLL_TRACK_TAG = 0xB024;\n// const HOT_TRACK_TAG = 0xB027;\n// const FALL_TRACK_TAG = 0xB028;\n// const HIDE_TRACK_TAG = 0xB029;\n// const POLY_2D = 0x5000;\n// const SHAPE_OK = 0x5010;\n// const SHAPE_NOT_OK = 0x5011;\n// const SHAPE_HOOK = 0x5020;\n// const PATH_3D = 0x6000;\n// const PATH_MATRIX = 0x6005;\n// const SHAPE_2D = 0x6010;\n// const M_SCALE = 0x6020;\n// const M_TWIST = 0x6030;\n// const M_TEETER = 0x6040;\n// const M_FIT = 0x6050;\n// const M_BEVEL = 0x6060;\n// const XZ_CURVE = 0x6070;\n// const YZ_CURVE = 0x6080;\n// const INTERPCT = 0x6090;\n// const DEFORM_LIMIT = 0x60A0;\n// const USE_CONTOUR = 0x6100;\n// const USE_TWEEN = 0x6110;\n// const USE_SCALE = 0x6120;\n// const USE_TWIST = 0x6130;\n// const USE_TEETER = 0x6140;\n// const USE_FIT = 0x6150;\n// const USE_BEVEL = 0x6160;\n// const DEFAULT_VIEW = 0x3000;\n// const VIEW_TOP = 0x3010;\n// const VIEW_BOTTOM = 0x3020;\n// const VIEW_LEFT = 0x3030;\n// const VIEW_RIGHT = 0x3040;\n// const VIEW_FRONT = 0x3050;\n// const VIEW_BACK = 0x3060;\n// const VIEW_USER = 0x3070;\n// const VIEW_CAMERA = 0x3080;\n// const VIEW_WINDOW = 0x3090;\n// const VIEWPORT_LAYOUT_OLD = 0x7000;\n// const VIEWPORT_DATA_OLD = 0x7010;\n// const VIEWPORT_LAYOUT = 0x7001;\n// const VIEWPORT_DATA = 0x7011;\n// const VIEWPORT_DATA_3 = 0x7012;\n// const VIEWPORT_SIZE = 0x7020;\n// const NETWORK_VIEW = 0x7030;\n\nexport { TDSLoader }\n"], "names": ["Loader", "LoaderUtils", "<PERSON><PERSON><PERSON><PERSON>", "Group", "MeshPhongMaterial", "DoubleSide", "AdditiveBlending", "BufferGeometry", "<PERSON><PERSON>", "Float32BufferAttribute", "Matrix4", "TextureLoader", "Color"], "mappings": ";;;AAyBA,MAAM,kBAAkBA,MAAAA,OAAO;AAAA,EAC7B,YAAY,SAAS;AACnB,UAAM,OAAO;AAEb,SAAK,QAAQ;AAEb,SAAK,QAAQ;AACb,SAAK,WAAW;AAEhB,SAAK,YAAY,CAAE;AACnB,SAAK,SAAS,CAAE;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWD,KAAK,KAAK,QAAQ,YAAY,SAAS;AACrC,UAAM,QAAQ;AAEd,UAAM,OAAO,KAAK,SAAS,KAAKC,MAAW,YAAC,eAAe,GAAG,IAAI,KAAK;AAEvE,UAAM,SAAS,IAAIC,iBAAW,KAAK,OAAO;AAC1C,WAAO,QAAQ,KAAK,IAAI;AACxB,WAAO,gBAAgB,aAAa;AACpC,WAAO,iBAAiB,KAAK,aAAa;AAC1C,WAAO,mBAAmB,KAAK,eAAe;AAE9C,WAAO;AAAA,MACL;AAAA,MACA,SAAU,MAAM;AACd,YAAI;AACF,iBAAO,MAAM,MAAM,MAAM,IAAI,CAAC;AAAA,QAC/B,SAAQ,GAAP;AACA,cAAI,SAAS;AACX,oBAAQ,CAAC;AAAA,UACrB,OAAiB;AACL,oBAAQ,MAAM,CAAC;AAAA,UAChB;AAED,gBAAM,QAAQ,UAAU,GAAG;AAAA,QAC5B;AAAA,MACF;AAAA,MACD;AAAA,MACA;AAAA,IACD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUD,MAAM,aAAa,MAAM;AACvB,SAAK,QAAQ,IAAIC,YAAO;AACxB,SAAK,WAAW;AAChB,SAAK,YAAY,CAAE;AACnB,SAAK,SAAS,CAAE;AAEhB,SAAK,SAAS,aAAa,IAAI;AAE/B,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC3C,WAAK,MAAM,IAAI,KAAK,OAAO,CAAC,CAAC;AAAA,IAC9B;AAED,WAAO,KAAK;AAAA,EACb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,SAAS,aAAa,MAAM;AAC1B,UAAM,OAAO,IAAI,SAAS,WAAW;AACrC,UAAM,QAAQ,KAAK,UAAU,IAAI;AAEjC,QAAI,MAAM,OAAO,aAAa,MAAM,OAAO,UAAU,MAAM,OAAO,UAAU;AAC1E,UAAI,OAAO,KAAK,UAAU,MAAM,KAAK;AAErC,aAAO,SAAS,GAAG;AACjB,YAAI,SAAS,aAAa;AACxB,gBAAM,UAAU,KAAK,UAAU,IAAI;AACnC,eAAK,aAAa,uBAAuB,OAAO;AAAA,QAC1D,WAAmB,SAAS,OAAO;AACzB,eAAK,cAAc,IAAI;AACvB,eAAK,aAAa,MAAM,IAAI;AAAA,QACtC,OAAe;AACL,eAAK,aAAa,yBAAyB,KAAK,SAAS,EAAE,CAAC;AAAA,QAC7D;AAED,eAAO,KAAK,UAAU,MAAM,KAAK;AAAA,MAClC;AAAA,IACF;AAED,SAAK,aAAa,YAAY,KAAK,OAAO,SAAS,SAAS;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,aAAa,MAAM,MAAM;AACvB,UAAM,QAAQ,KAAK,UAAU,IAAI;AACjC,QAAI,OAAO,KAAK,UAAU,MAAM,KAAK;AAErC,WAAO,SAAS,GAAG;AACjB,UAAI,SAAS,cAAc;AACzB,cAAM,UAAU,CAAC,KAAK,UAAU,IAAI;AACpC,aAAK,aAAa,mBAAmB,OAAO;AAAA,MACpD,WAAiB,SAAS,cAAc;AAChC,cAAM,QAAQ,KAAK,UAAU,IAAI;AACjC,aAAK,aAAa,mBAAmB,KAAK;AAC1C,aAAK,MAAM,MAAM,IAAI,OAAO,OAAO,KAAK;AAAA,MAChD,WAAiB,SAAS,cAAc;AAChC,aAAK,aAAa,cAAc;AAChC,aAAK,cAAc,IAAI;AACvB,aAAK,gBAAgB,IAAI;AAAA,MACjC,WAAiB,SAAS,WAAW;AAC7B,aAAK,aAAa,UAAU;AAC5B,aAAK,cAAc,IAAI;AACvB,aAAK,kBAAkB,MAAM,IAAI;AAAA,MACzC,OAAa;AACL,aAAK,aAAa,0BAA0B,KAAK,SAAS,EAAE,CAAC;AAAA,MAC9D;AAED,aAAO,KAAK,UAAU,MAAM,KAAK;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,gBAAgB,MAAM;AACpB,UAAM,QAAQ,KAAK,UAAU,IAAI;AACjC,UAAM,OAAO,KAAK,WAAW,MAAM,EAAE;AACrC,UAAM,MAAM,KAAK;AAEjB,QAAI,OAAO,KAAK,UAAU,MAAM,KAAK;AACrC,WAAO,SAAS,GAAG;AACjB,UAAI,SAAS,cAAc;AACzB,aAAK,cAAc,IAAI;AACvB,cAAM,OAAO,KAAK,SAAS,IAAI;AAC/B,aAAK,OAAO;AACZ,aAAK,OAAO,KAAK,IAAI;AAAA,MAC7B,OAAa;AACL,aAAK,aAAa,iCAAiC,KAAK,SAAS,EAAE,CAAC;AAAA,MACrE;AAED,aAAO,KAAK,UAAU,MAAM,KAAK;AAAA,IAClC;AAED,SAAK,SAAS,KAAK;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,kBAAkB,MAAM,MAAM;AAC5B,UAAM,QAAQ,KAAK,UAAU,IAAI;AACjC,QAAI,OAAO,KAAK,UAAU,MAAM,KAAK;AACrC,UAAM,WAAW,IAAIC,wBAAmB;AAExC,WAAO,SAAS,GAAG;AACjB,UAAI,SAAS,UAAU;AACrB,iBAAS,OAAO,KAAK,WAAW,MAAM,EAAE;AACxC,aAAK,aAAa,cAAc,SAAS,IAAI;AAAA,MACrD,WAAiB,SAAS,UAAU;AAC5B,aAAK,aAAa,cAAc;AAChC,iBAAS,YAAY;AAAA,MAC7B,WAAiB,SAAS,eAAe;AACjC,cAAM,QAAQ,KAAK,SAAS,IAAI;AAChC,iBAAS,qBAAqB;AAC9B,aAAK,aAAa,6BAA6B,KAAK;AAAA,MAC5D,WAAiB,SAAS,cAAc;AAChC,iBAAS,OAAOC,MAAU;AAC1B,aAAK,aAAa,gBAAgB;AAAA,MAC1C,WAAiB,SAAS,cAAc;AAChC,aAAK,aAAa,sBAAsB;AACxC,iBAAS,WAAWC,MAAgB;AAAA,MAC5C,WAAiB,SAAS,aAAa;AAC/B,aAAK,aAAa,kBAAkB;AACpC,iBAAS,QAAQ,KAAK,UAAU,IAAI;AAAA,MAC5C,WAAiB,SAAS,cAAc;AAChC,aAAK,aAAa,mBAAmB;AACrC,iBAAS,WAAW,KAAK,UAAU,IAAI;AAAA,MAC/C,WAAiB,SAAS,aAAa;AAC/B,aAAK,aAAa,kBAAkB;AACpC,iBAAS,QAAQ,KAAK,UAAU,IAAI;AAAA,MAC5C,WAAiB,SAAS,eAAe;AACjC,cAAM,YAAY,KAAK,eAAe,IAAI;AAC1C,iBAAS,YAAY,YAAY;AACjC,aAAK,aAAa,oBAAoB,SAAS;AAAA,MACvD,WAAiB,SAAS,kBAAkB;AACpC,cAAM,eAAe,KAAK,eAAe,IAAI;AAC7C,iBAAS,UAAU,IAAI;AACvB,aAAK,aAAa,sBAAsB,YAAY;AACpD,iBAAS,cAAc,SAAS,UAAU,IAAI,OAAO;AAAA,MAC7D,WAAiB,SAAS,YAAY;AAC9B,aAAK,aAAa,aAAa;AAC/B,aAAK,cAAc,IAAI;AACvB,iBAAS,MAAM,KAAK,QAAQ,MAAM,IAAI;AAAA,MAC9C,WAAiB,SAAS,aAAa;AAC/B,aAAK,aAAa,YAAY;AAC9B,aAAK,cAAc,IAAI;AACvB,iBAAS,UAAU,KAAK,QAAQ,MAAM,IAAI;AAAA,MAClD,WAAiB,SAAS,aAAa;AAC/B,aAAK,aAAa,eAAe;AACjC,aAAK,cAAc,IAAI;AACvB,iBAAS,WAAW,KAAK,QAAQ,MAAM,IAAI;AAAA,MACnD,WAAiB,SAAS,aAAa;AAC/B,aAAK,aAAa,gBAAgB;AAClC,aAAK,cAAc,IAAI;AACvB,iBAAS,cAAc,KAAK,QAAQ,MAAM,IAAI;AAAA,MACtD,OAAa;AACL,aAAK,aAAa,gCAAgC,KAAK,SAAS,EAAE,CAAC;AAAA,MACpE;AAED,aAAO,KAAK,UAAU,MAAM,KAAK;AAAA,IAClC;AAED,SAAK,SAAS,KAAK;AAEnB,SAAK,UAAU,SAAS,IAAI,IAAI;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,SAAS,MAAM;AACb,UAAM,QAAQ,KAAK,UAAU,IAAI;AACjC,QAAI,OAAO,KAAK,UAAU,MAAM,KAAK;AAErC,UAAM,WAAW,IAAIC,qBAAgB;AAErC,UAAM,WAAW,IAAIH,wBAAmB;AACxC,UAAM,OAAO,IAAII,WAAK,UAAU,QAAQ;AACxC,SAAK,OAAO;AAEZ,WAAO,SAAS,GAAG;AACjB,UAAI,SAAS,aAAa;AACxB,cAAM,SAAS,KAAK,SAAS,IAAI;AAEjC,aAAK,aAAa,gBAAgB,MAAM;AAIxC,cAAM,WAAW,CAAE;AAEnB,iBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,mBAAS,KAAK,KAAK,UAAU,IAAI,CAAC;AAClC,mBAAS,KAAK,KAAK,UAAU,IAAI,CAAC;AAClC,mBAAS,KAAK,KAAK,UAAU,IAAI,CAAC;AAAA,QACnC;AAED,iBAAS,aAAa,YAAY,IAAIC,MAAAA,uBAAuB,UAAU,CAAC,CAAC;AAAA,MACjF,WAAiB,SAAS,YAAY;AAC9B,aAAK,cAAc,IAAI;AACvB,aAAK,cAAc,MAAM,IAAI;AAAA,MACrC,WAAiB,SAAS,WAAW;AAC7B,cAAM,SAAS,KAAK,SAAS,IAAI;AAEjC,aAAK,aAAa,YAAY,MAAM;AAIpC,cAAM,MAAM,CAAE;AAEd,iBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,cAAI,KAAK,KAAK,UAAU,IAAI,CAAC;AAC7B,cAAI,KAAK,KAAK,UAAU,IAAI,CAAC;AAAA,QAC9B;AAED,iBAAS,aAAa,MAAM,IAAIA,MAAAA,uBAAuB,KAAK,CAAC,CAAC;AAAA,MACtE,WAAiB,SAAS,aAAa;AAC/B,aAAK,aAAa,gCAAgC;AAElD,cAAM,SAAS,CAAE;AACjB,iBAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,iBAAO,CAAC,IAAI,KAAK,UAAU,IAAI;AAAA,QAChC;AAED,cAAM,SAAS,IAAIC,cAAS;AAG5B,eAAO,SAAS,CAAC,IAAI,OAAO,CAAC;AAC7B,eAAO,SAAS,CAAC,IAAI,OAAO,CAAC;AAC7B,eAAO,SAAS,CAAC,IAAI,OAAO,CAAC;AAC7B,eAAO,SAAS,CAAC,IAAI,OAAO,CAAC;AAG7B,eAAO,SAAS,CAAC,IAAI,OAAO,CAAC;AAC7B,eAAO,SAAS,CAAC,IAAI,OAAO,CAAC;AAC7B,eAAO,SAAS,CAAC,IAAI,OAAO,CAAC;AAC7B,eAAO,SAAS,CAAC,IAAI,OAAO,EAAE;AAG9B,eAAO,SAAS,CAAC,IAAI,OAAO,CAAC;AAC7B,eAAO,SAAS,CAAC,IAAI,OAAO,CAAC;AAC7B,eAAO,SAAS,EAAE,IAAI,OAAO,CAAC;AAC9B,eAAO,SAAS,EAAE,IAAI,OAAO,EAAE;AAG/B,eAAO,SAAS,EAAE,IAAI;AACtB,eAAO,SAAS,EAAE,IAAI;AACtB,eAAO,SAAS,EAAE,IAAI;AACtB,eAAO,SAAS,EAAE,IAAI;AAEtB,eAAO,UAAW;AAElB,cAAM,UAAU,IAAIA,cAAS;AAC7B,gBAAQ,KAAK,MAAM,EAAE,OAAQ;AAC7B,iBAAS,aAAa,OAAO;AAE7B,eAAO,UAAU,KAAK,UAAU,KAAK,YAAY,KAAK,KAAK;AAAA,MACnE,OAAa;AACL,aAAK,aAAa,4BAA4B,KAAK,SAAS,EAAE,CAAC;AAAA,MAChE;AAED,aAAO,KAAK,UAAU,MAAM,KAAK;AAAA,IAClC;AAED,SAAK,SAAS,KAAK;AAEnB,aAAS,qBAAsB;AAE/B,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,cAAc,MAAM,MAAM;AACxB,UAAM,QAAQ,KAAK,UAAU,IAAI;AACjC,UAAM,QAAQ,KAAK,SAAS,IAAI;AAEhC,SAAK,aAAa,eAAe,KAAK;AAEtC,UAAM,QAAQ,CAAE;AAEhB,aAAS,IAAI,GAAG,IAAI,OAAO,EAAE,GAAG;AAC9B,YAAM,KAAK,KAAK,SAAS,IAAI,GAAG,KAAK,SAAS,IAAI,GAAG,KAAK,SAAS,IAAI,CAAC;AAExE,WAAK,SAAS,IAAI;AAAA,IACnB;AAED,SAAK,SAAS,SAAS,KAAK;AAI5B,QAAI,gBAAgB;AACpB,QAAI,QAAQ;AAEZ,WAAO,KAAK,WAAW,MAAM,KAAK;AAChC,YAAM,WAAW,KAAK,UAAU,IAAI;AAEpC,UAAI,SAAS,OAAO,eAAe;AACjC,aAAK,aAAa,sBAAsB;AAExC,aAAK,cAAc,IAAI;AAEvB,cAAM,QAAQ,KAAK,kBAAkB,IAAI;AACzC,cAAM,QAAQ,MAAM,MAAM,SAAS;AAEnC,aAAK,SAAS,SAAS,OAAO,OAAO,aAAa;AAElD,iBAAS;AACT;AAEA,cAAM,WAAW,KAAK,UAAU,MAAM,IAAI;AAE1C,YAAI,MAAM,QAAQ,KAAK,QAAQ,MAAM;AAAO,eAAK,WAAW,CAAE;AAE9D,YAAI,aAAa,QAAW;AAC1B,eAAK,SAAS,KAAK,QAAQ;AAAA,QAC5B;AAAA,MACT,OAAa;AACL,aAAK,aAAa,qCAAqC,SAAS,SAAS,EAAE,CAAC;AAAA,MAC7E;AAED,WAAK,SAAS,QAAQ;AAAA,IACvB;AAED,QAAI,KAAK,SAAS,WAAW;AAAG,WAAK,WAAW,KAAK,SAAS,CAAC;AAE/D,SAAK,SAAS,KAAK;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUD,QAAQ,MAAM,MAAM;AAClB,UAAM,QAAQ,KAAK,UAAU,IAAI;AACjC,QAAI,OAAO,KAAK,UAAU,MAAM,KAAK;AACrC,QAAI,UAAU,CAAE;AAEhB,UAAM,SAAS,IAAIC,oBAAc,KAAK,OAAO;AAC7C,WAAO,QAAQ,KAAK,gBAAgB,IAAI,EAAE,eAAe,KAAK,WAAW;AAEzE,WAAO,SAAS,GAAG;AACjB,UAAI,SAAS,aAAa;AACxB,cAAM,OAAO,KAAK,WAAW,MAAM,GAAG;AACtC,kBAAU,OAAO,KAAK,IAAI;AAE1B,aAAK,aAAa,iBAAiB,OAAO,IAAI;AAAA,MACtD,WAAiB,SAAS,iBAAiB;AACnC,gBAAQ,OAAO,IAAI,KAAK,UAAU,IAAI;AACtC,aAAK,aAAa,oBAAoB,QAAQ,OAAO,CAAC;AAAA,MAC9D,WAAiB,SAAS,iBAAiB;AACnC,gBAAQ,OAAO,IAAI,KAAK,UAAU,IAAI;AACtC,aAAK,aAAa,oBAAoB,QAAQ,OAAO,CAAC;AAAA,MAC9D,WAAiB,SAAS,gBAAgB;AAClC,gBAAQ,OAAO,IAAI,KAAK,UAAU,IAAI;AACtC,aAAK,aAAa,oBAAoB,QAAQ,OAAO,CAAC;AAAA,MAC9D,WAAiB,SAAS,gBAAgB;AAClC,gBAAQ,OAAO,IAAI,KAAK,UAAU,IAAI;AACtC,aAAK,aAAa,oBAAoB,QAAQ,OAAO,CAAC;AAAA,MAC9D,OAAa;AACL,aAAK,aAAa,8BAA8B,KAAK,SAAS,EAAE,CAAC;AAAA,MAClE;AAED,aAAO,KAAK,UAAU,MAAM,KAAK;AAAA,IAClC;AAED,SAAK,SAAS,KAAK;AAEnB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,kBAAkB,MAAM;AACtB,SAAK,UAAU,IAAI;AACnB,UAAM,OAAO,KAAK,WAAW,MAAM,EAAE;AACrC,UAAM,WAAW,KAAK,SAAS,IAAI;AAEnC,SAAK,aAAa,oBAAoB,IAAI;AAC1C,SAAK,aAAa,qBAAqB,QAAQ;AAE/C,UAAM,QAAQ,CAAE;AAChB,aAAS,IAAI,GAAG,IAAI,UAAU,EAAE,GAAG;AACjC,YAAM,KAAK,KAAK,SAAS,IAAI,CAAC;AAAA,IAC/B;AAED,WAAO,EAAE,MAAY,MAAc;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,UAAU,MAAM;AACd,UAAM,QAAQ,KAAK,UAAU,IAAI;AACjC,UAAM,QAAQ,IAAIC,YAAO;AAEzB,QAAI,MAAM,OAAO,YAAY,MAAM,OAAO,cAAc;AACtD,YAAM,IAAI,KAAK,SAAS,IAAI;AAC5B,YAAM,IAAI,KAAK,SAAS,IAAI;AAC5B,YAAM,IAAI,KAAK,SAAS,IAAI;AAE5B,YAAM,OAAO,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG;AAEtC,WAAK,aAAa,kBAAkB,MAAM,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;AAAA,IACnF,WAAe,MAAM,OAAO,WAAW,MAAM,OAAO,aAAa;AAC3D,YAAM,IAAI,KAAK,UAAU,IAAI;AAC7B,YAAM,IAAI,KAAK,UAAU,IAAI;AAC7B,YAAM,IAAI,KAAK,UAAU,IAAI;AAE7B,YAAM,OAAO,GAAG,GAAG,CAAC;AAEpB,WAAK,aAAa,kBAAkB,MAAM,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;AAAA,IACnF,OAAW;AACL,WAAK,aAAa,gCAAgC,MAAM,SAAS,EAAE,CAAC;AAAA,IACrE;AAED,SAAK,SAAS,KAAK;AACnB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,UAAU,MAAM;AACd,UAAM,QAAQ,CAAE;AAEhB,UAAM,MAAM,KAAK;AACjB,UAAM,KAAK,KAAK,SAAS,IAAI;AAC7B,UAAM,OAAO,KAAK,UAAU,IAAI;AAChC,UAAM,MAAM,MAAM,MAAM,MAAM;AAC9B,UAAM,OAAO;AAEb,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,SAAS,OAAO;AACd,SAAK,WAAW,MAAM;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,UAAU,MAAM,OAAO;AACrB,QAAI,MAAM,OAAO,MAAM,KAAK;AAC1B,aAAO;AAAA,IACR;AAED,SAAK,WAAW,MAAM;AAEtB,QAAI;AACF,YAAM,OAAO,KAAK,UAAU,IAAI;AAChC,YAAM,OAAO,KAAK;AAClB,aAAO,KAAK;AAAA,IACb,SAAQ,GAAP;AACA,WAAK,aAAa,6BAA6B,KAAK,QAAQ;AAC5D,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,gBAAgB;AACd,SAAK,YAAY;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,SAAS,MAAM;AACb,UAAM,IAAI,KAAK,SAAS,KAAK,UAAU,IAAI;AAC3C,SAAK,YAAY;AACjB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,UAAU,MAAM;AACd,QAAI;AACF,YAAM,IAAI,KAAK,WAAW,KAAK,UAAU,IAAI;AAC7C,WAAK,YAAY;AACjB,aAAO;AAAA,IACR,SAAQ,GAAP;AACA,WAAK,aAAa,IAAI,MAAM,KAAK,WAAW,MAAM,KAAK,UAAU;AAAA,IAClE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,QAAQ,MAAM;AACZ,UAAM,IAAI,KAAK,SAAS,KAAK,UAAU,IAAI;AAC3C,SAAK,YAAY;AACjB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,UAAU,MAAM;AACd,UAAM,IAAI,KAAK,SAAS,KAAK,UAAU,IAAI;AAC3C,SAAK,YAAY;AACjB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,UAAU,MAAM;AACd,UAAM,IAAI,KAAK,UAAU,KAAK,UAAU,IAAI;AAC5C,SAAK,YAAY;AACjB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,SAAS,MAAM;AACb,UAAM,IAAI,KAAK,UAAU,KAAK,UAAU,IAAI;AAC5C,SAAK,YAAY;AACjB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUD,WAAW,MAAM,WAAW;AAC1B,QAAI,IAAI;AAER,aAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,YAAM,IAAI,KAAK,SAAS,IAAI;AAC5B,UAAI,CAAC,GAAG;AACN;AAAA,MACD;AAED,WAAK,OAAO,aAAa,CAAC;AAAA,IAC3B;AAED,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,eAAe,MAAM;AACnB,UAAM,QAAQ,KAAK,UAAU,IAAI;AACjC,QAAI;AAEJ,YAAQ,MAAM,IAAE;AAAA,MACd,KAAK;AACH,gBAAQ,KAAK,UAAU,IAAI,IAAI;AAC/B;AAAA,MAEF,KAAK;AACH,gBAAQ,KAAK,UAAU,IAAI;AAC3B;AAAA,MAEF;AACE,aAAK,aAAa,qCAAqC,MAAM,SAAS,EAAE,CAAC;AAAA,IAC5E;AAED,SAAK,SAAS,KAAK;AAEnB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUD,aAAa,SAAS;AACpB,QAAI,KAAK,OAAO;AACd,cAAQ,IAAI,OAAO;AAAA,IACpB;AAAA,EACF;AACH;AAGA,MAAM,WAAW;AAGjB,MAAM,YAAY;AAElB,MAAM,SAAS;AACf,MAAM,cAAc;AAEpB,MAAM,UAAU;AAChB,MAAM,WAAW;AACjB,MAAM,eAAe;AACrB,MAAM,cAAc;AACpB,MAAM,iBAAiB;AACvB,MAAM,mBAAmB;AACzB,MAAM,QAAQ;AACd,MAAM,eAAe;AACrB,MAAM,eAAe;AAwBrB,MAAM,YAAY;AAClB,MAAM,WAAW;AACjB,MAAM,cAAc;AACpB,MAAM,cAAc;AACpB,MAAM,eAAe;AACrB,MAAM,gBAAgB;AAEtB,MAAM,mBAAmB;AAOzB,MAAM,eAAe;AAErB,MAAM,eAAe;AACrB,MAAM,WAAW;AAKjB,MAAM,gBAAgB;AACtB,MAAM,aAAa;AAQnB,MAAM,cAAc;AAIpB,MAAM,cAAc;AAIpB,MAAM,cAAc;AAgBpB,MAAM,cAAc;AAGpB,MAAM,iBAAiB;AACvB,MAAM,iBAAiB;AACvB,MAAM,kBAAkB;AACxB,MAAM,kBAAkB;AAOxB,MAAM,eAAe;AA8BrB,MAAM,eAAe;AACrB,MAAM,cAAc;AAEpB,MAAM,aAAa;AACnB,MAAM,gBAAgB;AAGtB,MAAM,YAAY;AAClB,MAAM,cAAc;;"}