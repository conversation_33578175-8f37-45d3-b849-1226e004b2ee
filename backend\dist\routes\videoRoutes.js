"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const videoController_1 = require("../controllers/videoController");
const router = express_1.default.Router();
router.post('/', videoController_1.createVideo);
router.get('/', videoController_1.getVideos);
router.get('/:id', videoController_1.getVideoById);
router.put('/:id', videoController_1.updateVideo);
router.delete('/:id', videoController_1.deleteVideo);
router.patch('/:id/status', videoController_1.updateVideoStatus); // Add route for updateVideoStatus
exports.default = router;
