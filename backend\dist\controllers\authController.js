"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.resetPassword = exports.forgotPassword = exports.refreshToken = exports.validateToken = exports.getMe = exports.login = exports.register = void 0;
const User_1 = __importDefault(require("../models/User"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const dotenv_1 = __importDefault(require("dotenv"));
const crypto_1 = __importDefault(require("crypto"));
const nodemailer_1 = __importDefault(require("nodemailer"));
dotenv_1.default.config();
const JWT_SECRET = process.env.JWT_SECRET;
const JWT_LIFETIME = process.env.JWT_LIFETIME || '30d'; // Default token lifetime
const RESET_PASSWORD_SECRET = process.env.RESET_PASSWORD_SECRET;
const EMAIL_USER = process.env.EMAIL_USER;
const EMAIL_PASS = process.env.EMAIL_PASS;
// Check if required environment variables are defined
if (!JWT_SECRET) {
    console.error('FATAL ERROR: JWT_SECRET is not defined.');
    process.exit(1); // Exit the process if the secret is not set
}
if (!RESET_PASSWORD_SECRET) {
    console.error('FATAL ERROR: RESET_PASSWORD_SECRET is not defined.');
    process.exit(1);
}
if (!EMAIL_USER || !EMAIL_PASS) {
    console.warn('WARNING: EMAIL_USER or EMAIL_PASS not defined. Forgot password email functionality will not work.');
}
// Register new user
const register = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { username, email, password } = req.body;
        // Basic validation
        if (!username || !email || !password) {
            return res.status(400).json({ message: 'Username, email, and password are required' });
        }
        // TODO: Add more robust validation for email format, password strength, etc.
        const newUser = new User_1.default({ username, email, password });
        const savedUser = yield newUser.save();
        // Generate JWT token
        const token = jsonwebtoken_1.default.sign({ userId: savedUser._id, role: savedUser.role }, JWT_SECRET, { expiresIn: JWT_LIFETIME });
        // Return response in format expected by frontend
        res.status(201).json({
            user: {
                id: savedUser._id,
                name: savedUser.username,
                email: savedUser.email,
                role: savedUser.role,
                status: savedUser.status || 'active'
            },
            token
        });
    }
    catch (error) {
        // Check for duplicate key error (username or email already exists)
        if (error.code === 11000) {
            return res.status(400).json({ message: 'Username or email already exists' });
        }
        res.status(500).json({ message: error.message });
    }
});
exports.register = register;
// Login user
const login = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { email, password } = req.body;
        // Basic validation
        if (!email || !password) {
            return res.status(400).json({ message: 'Email and password are required' });
        }
        // TODO: Add more robust validation for email format
        const user = yield User_1.default.findOne({ email });
        if (!user) {
            console.log('User not found');
            return res.status(401).json({ message: 'Invalid credentials' });
        }
        console.log('User found:', user);
        const isMatch = yield user.comparePassword(password);
        if (!isMatch) {
            console.log('Password does not match');
            return res.status(401).json({ message: 'Invalid credentials' });
        }
        console.log('Password matches');
        // Generate JWT token
        const token = jsonwebtoken_1.default.sign({ userId: user._id, role: user.role }, JWT_SECRET, { expiresIn: JWT_LIFETIME });
        // Return response in format expected by frontend
        res.status(200).json({
            user: {
                id: user._id,
                name: user.username,
                email: user.email,
                role: user.role,
                status: user.status || 'active'
            },
            token
        });
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.login = login;
// Get current user info (requires authentication middleware)
const getMe = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // User information is expected to be attached to the request by authentication middleware
        const user = req.user; // Assuming user is attached by middleware
        if (!user) {
            return res.status(401).json({ message: 'Not authenticated' });
        }
        res.status(200).json({ _id: user._id, username: user.username, email: user.email, role: user.role });
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.getMe = getMe;
// Validate token endpoint - returns user data if token is valid
const validateToken = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = req.user;
        if (!user) {
            return res.status(401).json({ message: 'Invalid token' });
        }
        // Return user data in the format expected by frontend
        res.status(200).json({
            id: user._id,
            name: user.username,
            email: user.email,
            role: user.role,
            status: user.status || 'active'
        });
    }
    catch (error) {
        res.status(401).json({ message: 'Token validation failed' });
    }
});
exports.validateToken = validateToken;
// Refresh token (basic implementation - can be enhanced with refresh tokens)
const refreshToken = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // In a real application, you would use a refresh token here
        // For this basic implementation, we'll just return a new token if the user is authenticated
        const user = req.user; // Assuming user is attached by middleware
        if (!user) {
            return res.status(401).json({ message: 'Not authenticated' });
        }
        const newToken = jsonwebtoken_1.default.sign({ userId: user._id, role: user.role }, JWT_SECRET, { expiresIn: JWT_LIFETIME });
        res.status(200).json({ token: newToken });
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.refreshToken = refreshToken;
// Request password reset - sends password to admin emails only
const forgotPassword = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { email } = req.body;
        console.log('Password reset requested for:', email);
        const user = yield User_1.default.findOne({ email });
        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }
        // Check if the user is an admin
        if (user.role !== 'admin') {
            console.log('Password reset denied for non-admin user:', email);
            return res.status(403).json({
                message: 'Password reset is only available for admin users. Please contact the administrator.'
            });
        }
        // Generate a temporary password for the user
        const tempPassword = crypto_1.default.randomBytes(8).toString('hex'); // Generate 16-character temp password
        // Update user's password with the temporary password
        user.password = tempPassword; // This will be hashed by the pre-save middleware
        yield user.save();
        console.log('Temporary password generated for user:', user.email);
        // Configure nodemailer transporter for Gmail (using admin email)
        const transporter = nodemailer_1.default.createTransport({
            service: 'Gmail',
            auth: {
                user: EMAIL_USER || '<EMAIL>', // Admin email as sender
                pass: EMAIL_PASS || 'your_gmail_app_password_here',
            },
        });
        const emailMessage = `
Admin Password Reset Request

Hello ${user.username},

You have requested a password reset for your admin account on Djerba Explorer.

Your new temporary password is: ${tempPassword}

Please use this password to log in and change it immediately for security reasons.

Admin Account Details:
- Email: ${user.email}
- Username: ${user.username}
- Role: ${user.role}
- Reset Time: ${new Date().toLocaleString()}

IMPORTANT: This is an admin account password reset. If you did not request this, please secure your account immediately.

Best regards,
Djerba Explorer System

This email was sent automatically from the Djerba Explorer system.
    `;
        const mailOptions = {
            to: user.email, // Send to the admin user who requested the reset
            from: EMAIL_USER || '<EMAIL>', // Send from system admin email
            subject: `Admin Password Reset - Djerba Explorer`,
            text: emailMessage,
        };
        // Check if email credentials are properly configured
        if (!EMAIL_USER || !EMAIL_PASS || EMAIL_PASS === 'REPLACE_WITH_YOUR_16_CHARACTER_APP_PASSWORD') {
            console.log('⚠️  Email not configured. Password reset successful but email not sent.');
            console.log('📋 New password for', user.email, ':', tempPassword);
            console.log('🔧 To enable email: Set up Gmail App Password in backend/.env');
            return res.status(200).json({
                message: `Password reset successful! New password: ${tempPassword} (Email not configured - check server logs)`
            });
        }
        transporter.sendMail(mailOptions, (error, info) => {
            if (error) {
                console.error('Error sending email:', error);
                console.log('📋 New password for', user.email, ':', tempPassword);
                console.log('🔧 To fix email: Set up Gmail App Password in backend/.env');
                return res.status(200).json({
                    message: `Password reset successful! New password: ${tempPassword} (Email failed - check server logs)`
                });
            }
            console.log('Admin password reset email sent:', info.response);
            res.status(200).json({
                message: 'Admin password reset email sent successfully. Please check your email for your new password.'
            });
        });
    }
    catch (error) {
        console.error('Forgot password error:', error);
        res.status(500).json({ message: error.message });
    }
});
exports.forgotPassword = forgotPassword;
// Reset password
const resetPassword = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const resetPasswordToken = crypto_1.default.createHash('sha256').update(req.params.resetToken).digest('hex');
        const user = yield User_1.default.findOne({
            resetPasswordToken,
            resetPasswordExpire: { $gt: Date.now() },
        });
        if (!user) {
            return res.status(400).json({ message: 'Invalid or expired reset token' });
        }
        // Set new password
        user.password = req.body.password;
        user.resetPasswordToken = undefined;
        user.resetPasswordExpire = undefined;
        yield user.save();
        res.status(200).json({ message: 'Password reset successful' });
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.resetPassword = resetPassword;
