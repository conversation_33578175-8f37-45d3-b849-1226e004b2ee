"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const User_1 = __importDefault(require("../models/User"));
const dotenv_1 = __importDefault(require("dotenv"));
// Load environment variables
dotenv_1.default.config();
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/djerba';
function resetAdminPassword() {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            // Connect to MongoDB
            yield mongoose_1.default.connect(MONGODB_URI);
            console.log('Connected to MongoDB');
            // Find or create admin user
            let adminUser = yield User_1.default.findOne({ email: '<EMAIL>' });
            if (!adminUser) {
                // Create new admin user
                adminUser = new User_1.default({
                    username: 'admin',
                    email: '<EMAIL>',
                    password: 'admin123', // This will be hashed by the pre-save middleware
                    role: 'admin',
                    status: 'active'
                });
                yield adminUser.save();
                console.log('✅ New admin user created successfully!');
            }
            else {
                // Reset existing admin password
                adminUser.password = 'admin123'; // This will be hashed by the pre-save middleware
                yield adminUser.save();
                console.log('✅ Admin password reset successfully!');
            }
            console.log('\n📋 Admin Login Credentials:');
            console.log('Email: <EMAIL>');
            console.log('Password: admin123');
            console.log('\n⚠️  Please change this password after logging in!');
            // Disconnect from MongoDB
            yield mongoose_1.default.disconnect();
            console.log('\n✅ Script completed successfully');
            process.exit(0);
        }
        catch (error) {
            console.error('❌ Error resetting admin password:', error);
            process.exit(1);
        }
    });
}
// Run the script
resetAdminPassword();
