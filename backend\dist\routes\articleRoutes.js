"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const uploadMiddleware_1 = __importDefault(require("../middleware/uploadMiddleware")); // Import upload middleware
const articleController_1 = require("../controllers/articleController");
const router = express_1.default.Router();
router.post('/', uploadMiddleware_1.default.array('images'), articleController_1.createArticle); // Add upload middleware - removed file count limit
router.get('/', articleController_1.getArticles);
router.get('/:id', articleController_1.getArticleById);
router.get('/slug/:slug', articleController_1.getArticleBySlug);
router.put('/:id', articleController_1.updateArticle);
router.delete('/:id', articleController_1.deleteArticle);
router.patch('/:id/status', articleController_1.updateArticleStatus); // Add route for updateArticleStatus
exports.default = router;
