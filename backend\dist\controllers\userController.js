"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteUser = exports.getUserStats = exports.updateUser = exports.getUserById = exports.getUsers = exports.createUser = void 0;
const User_1 = __importDefault(require("../models/User"));
// Create a new user
const createUser = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        console.log('createUser function called');
        const { username, email, password, role } = req.body;
        // Basic validation
        if (!username || !email || !password) {
            return res.status(400).json({ message: 'Username, email, and password are required' });
        }
        // Check if there are any users in the database
        const userCount = yield User_1.default.countDocuments();
        console.log(`userCount: ${userCount}`);
        let newUser;
        if (userCount === 0) {
            // Create a default admin user if there are no users
            newUser = new User_1.default({
                username: 'admin',
                email: '<EMAIL>',
                password: 'password', // Replace with a more secure password in production
                role: 'admin',
                createdAt: new Date(),
            });
        }
        else {
            console.log('Request Body:', req.body); // Log request body
            newUser = new User_1.default({ username, email, password, role, createdAt: new Date() });
        }
        console.log('New User Object before save:', newUser); // Log newUser object
        const savedUser = yield newUser.save();
        res.status(201).json(savedUser);
    }
    catch (error) {
        // Check for duplicate key error (username or email already exists)
        if (error.code === 11000) {
            return res.status(400).json({ message: 'Username or email already exists' });
        }
        res.status(500).json({ message: error.message });
    }
});
exports.createUser = createUser;
// Get all users
const getUsers = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const users = yield User_1.default.find();
        console.log(`Number of users in database: ${users.length}`);
        res.status(200).json(users);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.getUsers = getUsers;
// Get a single user by ID
const getUserById = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = yield User_1.default.findById(req.params.id);
        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }
        res.status(200).json(user);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.getUserById = getUserById;
// Update a user by ID
const updateUser = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const updatePayload = req.body;
        // Find the user first to apply partial updates
        const userToUpdate = yield User_1.default.findById(req.params.id);
        if (!userToUpdate) {
            return res.status(404).json({ message: 'User not found' });
        }
        // Apply updates from the payload
        Object.assign(userToUpdate, updatePayload);
        const updatedUser = yield userToUpdate.save();
        if (!updatedUser) {
            return res.status(404).json({ message: 'User not found' });
        }
        res.status(200).json(updatedUser);
    }
    catch (error) {
        // Check for duplicate key error (username or email already exists)
        if (error.code === 11000) {
            return res.status(400).json({ message: 'Username or email already exists' });
        }
        res.status(500).json({ message: error.message });
    }
});
exports.updateUser = updateUser;
// Get user statistics
const getUserStats = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const totalUsers = yield User_1.default.countDocuments();
        const activeUsers = yield User_1.default.countDocuments({ status: 'active' });
        const blockedUsers = yield User_1.default.countDocuments({ status: 'blocked' });
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const newUsers = yield User_1.default.countDocuments({ createdAt: { $gte: thirtyDaysAgo } });
        console.log(`totalUsers: ${totalUsers}`);
        console.log(`activeUsers: ${activeUsers}`);
        console.log(`blockedUsers: ${blockedUsers}`);
        console.log(`newUsers: ${newUsers}`);
        res.status(200).json({
            totalUsers,
            activeUsers,
            blockedUsers,
            newUsers,
        });
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.getUserStats = getUserStats;
// Delete a user by ID
const deleteUser = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const deletedUser = yield User_1.default.findByIdAndDelete(req.params.id);
        if (!deletedUser) {
            return res.status(404).json({ message: 'User not found' });
        }
        res.status(200).json({ message: 'User deleted' });
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.deleteUser = deleteUser;
