import express, { Request, Response } from 'express';
import mongoose from 'mongoose';
import dotenv from 'dotenv';
import cors from 'cors'; // Import CORS middleware
import articleRoutes from './routes/articleRoutes'; // Import article routes
import photoRoutes from './routes/photoRoutes'; // Import photo routes
import videoRoutes from './routes/videoRoutes'; // Import video routes
import userRoutes from './routes/userRoutes'; // Import user routes
import settingRoutes from './routes/settingRoutes'; // Import setting routes
import authRoutes from './routes/authRoutes'; // Import auth routes
import analyticRoutes from './routes/analyticRoutes'; // Import analytic routes
import experienceRoutes from './routes/experienceRoutes'; // Import experience routes
import notificationRoutes from './routes/notificationRoutes'; // Import notification routes
import placeRoutes from './routes/placeRoutes'; // Import place routes
import { autoSyncVRProjects } from './services/vrSyncService'; // Import VR sync service

dotenv.config();

const app = express();
const port = process.env.PORT || 5000;

// Middleware
app.use(express.json({ limit: '500mb' })); // Increase JSON payload limit
app.use(express.urlencoded({ extended: true, limit: '500mb' })); // Increase URL-encoded payload limit

// Enhanced CORS configuration for file uploads
app.use(cors({
  origin: [
    'http://localhost:8080', 'http://127.0.0.1:8080', // Admin frontend
    'http://localhost:8081', 'http://127.0.0.1:8081', // Djerba frontend
    'http://localhost:8082', 'http://127.0.0.1:8082', // Admin/Djerba frontend (alternate port)
    'http://localhost:8083', 'http://127.0.0.1:8083'  // Djerba frontend (alternate port)
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  maxAge: 86400 // Cache preflight for 24 hours
}));

// Serve VR projects from VRproj folder
app.use('/vr', express.static('VRproj'));

// MongoDB Connection
mongoose.connect(process.env.MONGODB_URI as string)
  .then(async () => {
    console.log('MongoDB connected');

    // Sync VR projects after database connection
    setTimeout(async () => {
      console.log('Starting VR projects sync...');
      await autoSyncVRProjects();
    }, 2000); // Wait 2 seconds after server start
  })
  .catch((err: Error) => console.error('MongoDB connection error:', err));

// Routes
app.use('/api/articles', articleRoutes); // Use article routes
app.use('/api/photos', photoRoutes); // Use photo routes
app.use('/api/videos', videoRoutes); // Use video routes

// Log request body before user routes
app.use('/api/users', (req, res, next) => {
  console.log('Request to /api/users. Method:', req.method);
  console.log('Request Body:', req.body);
  next();
}, userRoutes); // Use user routes

app.use('/api/settings', settingRoutes); // Use setting routes
app.use('/api/auth', authRoutes); // Use auth routes
app.use('/api/analytics', analyticRoutes); // Use analytic routes
// Log requests to experiences endpoint
app.use('/api/experiences', (req, res, next) => {
  console.log(`Request to /api/experiences${req.path}. Method: ${req.method}`);
  console.log('Headers:', req.headers);
  if (req.method === 'POST') {
    console.log('Body keys:', Object.keys(req.body || {}));
    console.log('Files:', req.files ? `${(req.files as any[]).length} files` : 'No files');
  }
  next();
}, experienceRoutes); // Use experience routes
app.use('/api/notifications', notificationRoutes); // Use notification routes
app.use('/api/places', placeRoutes); // Use place routes

// Basic route
app.get('/', (req: Request, res: Response) => {
  res.send('Backend is running!');
});

// Test route for VR upload connectivity
app.post('/api/test-upload', (req: Request, res: Response) => {
  console.log('Test upload endpoint hit');
  res.json({ message: 'Upload endpoint is working', timestamp: new Date().toISOString() });
});

app.listen(port, () => {
  console.log(`Server running on port ${port}`);
});