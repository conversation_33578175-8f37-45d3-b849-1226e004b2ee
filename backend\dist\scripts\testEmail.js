"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const nodemailer_1 = __importDefault(require("nodemailer"));
const dotenv_1 = __importDefault(require("dotenv"));
// Load environment variables
dotenv_1.default.config();
const EMAIL_USER = process.env.EMAIL_USER;
const EMAIL_PASS = process.env.EMAIL_PASS;
function testEmail() {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            console.log('Testing email configuration...');
            console.log('Email User:', EMAIL_USER);
            console.log('Email Pass:', EMAIL_PASS ? '***configured***' : 'NOT SET');
            if (!EMAIL_USER || !EMAIL_PASS) {
                console.error('❌ EMAIL_USER or EMAIL_PASS not configured in .env file');
                console.log('\nPlease set up your Admin Gmail App Password:');
                console.log('1. Enable 2-Factor Authentication on your admin Gmail account');
                console.log('2. Generate an App Password for Mail');
                console.log('3. Update your .env file with the admin email credentials');
                process.exit(1);
            }
            // Configure nodemailer transporter
            const transporter = nodemailer_1.default.createTransport({
                service: 'Gmail',
                auth: {
                    user: EMAIL_USER,
                    pass: EMAIL_PASS,
                },
            });
            // Test email content
            const testMessage = `
Email Configuration Test

This is a test email to verify that the email configuration is working correctly.

Configuration Details:
- Service: Gmail
- From: ${EMAIL_USER}
- To: ${EMAIL_USER}
- Time: ${new Date().toISOString()}

If you receive this email, the configuration is working properly!
    `;
            const mailOptions = {
                to: EMAIL_USER,
                from: EMAIL_USER,
                subject: 'Email Configuration Test - Djerba Explorer',
                text: testMessage,
            };
            console.log('Sending test email...');
            const info = yield transporter.sendMail(mailOptions);
            console.log('✅ Test email sent successfully!');
            console.log('Message ID:', info.messageId);
            console.log('Response:', info.response);
            console.log('\n📧 Check your inbox at:', EMAIL_USER);
        }
        catch (error) {
            console.error('❌ Error sending test email:', error);
            if (error instanceof Error) {
                if (error.message.includes('Invalid login')) {
                    console.log('\n💡 This usually means:');
                    console.log('1. Wrong email or password');
                    console.log('2. 2-Factor Authentication not enabled');
                    console.log('3. App Password not generated or incorrect');
                    console.log('4. Gmail security settings blocking the app');
                }
            }
            process.exit(1);
        }
    });
}
// Run the test
testEmail();
