# VR Experience Demo

A browser-compatible VR experience built with A-Frame that works in both desktop and VR environments.

## Features

- ✅ **Browser Compatible**: No Node.js `require()` statements
- 🥽 **VR Ready**: Works with VR headsets and controllers
- 🖱️ **Desktop Friendly**: Mouse and keyboard controls
- 📱 **Mobile Support**: Touch controls for mobile devices
- 🎵 **Audio Feedback**: Interactive sound effects
- 🎨 **Interactive Objects**: Clickable 3D elements with animations

## Files

- `index.html` - Main VR experience file
- `style.css` - Styling and responsive design
- `script.js` - Interactive JavaScript functionality
- `README.md` - This documentation

## Controls

### Desktop
- **Mouse**: Look around
- **WASD**: Move around
- **Click**: Interact with objects
- **VR Button**: Enter VR mode (if VR headset connected)

### VR Mode
- **Head Movement**: Look around
- **Controller Triggers**: Interact with objects
- **Controller Grips**: Additional interactions

## Objects

1. **Blue Box**: Click to spin
2. **Red Sphere**: Click to bounce
3. **Yellow Cylinder**: Click to scale
4. **Green Torus**: Continuously rotating

## Technical Details

- Built with A-Frame 1.4.0
- Uses A-Frame Extras for enhanced functionality
- Web Audio API for sound effects
- Responsive CSS design
- Performance monitoring in debug mode

## Browser Compatibility

- Chrome/Chromium (recommended for VR)
- Firefox
- Safari
- Edge
- Mobile browsers

## VR Headset Support

- Oculus/Meta Quest
- HTC Vive
- Windows Mixed Reality
- Any WebXR-compatible headset

## Development

This project demonstrates how to create VR experiences that work in browsers without requiring Node.js or complex build processes.

## Troubleshooting

If you see "require is not defined" errors, make sure you're accessing this through a web server (like http://localhost:5000) and not opening the HTML file directly in the browser.

## License

This is a demo project for educational purposes.
