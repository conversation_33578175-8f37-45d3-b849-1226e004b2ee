{"version": 3, "file": "DRACOExporter.js", "sources": ["../../src/exporters/DRACOExporter.js"], "sourcesContent": ["import { BufferGeo<PERSON>, Mesh, Points } from 'three'\n\n/**\n * Export draco compressed files from threejs geometry objects.\n *\n * Draco files are compressed and usually are smaller than conventional 3D file formats.\n *\n * The exporter receives a options object containing\n *  - decodeSpeed, indicates how to tune the encoder regarding decode speed (0 gives better speed but worst quality)\n *  - encodeSpeed, indicates how to tune the encoder parameters (0 gives better speed but worst quality)\n *  - encoderMethod\n *  - quantization, indicates the presision of each type of data stored in the draco file in the order (POSITION, NORMAL, COLOR, TEX_COORD, GENERIC)\n *  - exportUvs\n *  - exportNormals\n */\n\nconst DRACOExporter = /* @__PURE__ */ (() => {\n  class DRACOExporter {\n    // Encoder methods\n\n    static MESH_EDGEBREAKER_ENCODING = 1\n    static MESH_SEQUENTIAL_ENCODING = 0\n\n    // Geometry type\n\n    static POINT_CLOUD = 0\n    static TRIANGULAR_MESH = 1\n\n    // Attribute type\n    static INVALID = -1\n    static POSITION = 0\n    static NORMAL = 1\n    static COLOR = 2\n    static TEX_COORD = 3\n    static GENERIC = 4\n\n    parse(\n      object,\n      options = {\n        decodeSpeed: 5,\n        encodeSpeed: 5,\n        encoderMethod: DRACOExporter.MESH_EDGEBREAKER_ENCODING,\n        quantization: [16, 8, 8, 8, 8],\n        exportUvs: true,\n        exportNormals: true,\n        exportColor: false,\n      },\n    ) {\n      if (object instanceof BufferGeometry && object.isBufferGeometry) {\n        throw new Error('DRACOExporter: The first parameter of parse() is now an instance of Mesh or Points.')\n      }\n\n      if (DracoEncoderModule === undefined) {\n        throw new Error('THREE.DRACOExporter: required the draco_encoder to work.')\n      }\n\n      const geometry = object.geometry\n\n      const dracoEncoder = DracoEncoderModule()\n      const encoder = new dracoEncoder.Encoder()\n      let builder\n      let dracoObject\n\n      if (!geometry.isBufferGeometry) {\n        throw new Error(\n          'THREE.DRACOExporter.parse(geometry, options): geometry is not a THREE.BufferGeometry instance.',\n        )\n      }\n\n      if (object instanceof Mesh && object.isMesh) {\n        builder = new dracoEncoder.MeshBuilder()\n        dracoObject = new dracoEncoder.Mesh()\n\n        const vertices = geometry.getAttribute('position')\n        // @ts-ignore\n        builder.AddFloatAttributeToMesh(\n          dracoObject,\n          dracoEncoder.POSITION,\n          vertices.count,\n          vertices.itemSize,\n          vertices.array,\n        )\n\n        const faces = geometry.getIndex()\n\n        if (faces !== null) {\n          builder.AddFacesToMesh(dracoObject, faces.count / 3, faces.array)\n        } else {\n          const faces = new (vertices.count > 65535 ? Uint32Array : Uint16Array)(vertices.count)\n\n          for (let i = 0; i < faces.length; i++) {\n            faces[i] = i\n          }\n\n          builder.AddFacesToMesh(dracoObject, vertices.count, faces)\n        }\n\n        if (options.exportNormals) {\n          const normals = geometry.getAttribute('normal')\n\n          if (normals !== undefined) {\n            // @ts-ignore\n            builder.AddFloatAttributeToMesh(\n              dracoObject,\n              dracoEncoder.NORMAL,\n              normals.count,\n              normals.itemSize,\n              normals.array,\n            )\n          }\n        }\n\n        if (options.exportUvs) {\n          const uvs = geometry.getAttribute('uv')\n\n          if (uvs !== undefined) {\n            // @ts-ignore\n            builder.AddFloatAttributeToMesh(dracoObject, dracoEncoder.TEX_COORD, uvs.count, uvs.itemSize, uvs.array)\n          }\n        }\n\n        if (options.exportColor) {\n          const colors = geometry.getAttribute('color')\n\n          if (colors !== undefined) {\n            // @ts-ignore\n            builder.AddFloatAttributeToMesh(\n              dracoObject,\n              dracoEncoder.COLOR,\n              colors.count,\n              colors.itemSize,\n              colors.array,\n            )\n          }\n        }\n      } else if (object instanceof Points && object.isPoints) {\n        // @ts-ignore\n        builder = new dracoEncoder.PointCloudBuilder()\n        // @ts-ignore\n        dracoObject = new dracoEncoder.PointCloud()\n\n        const vertices = geometry.getAttribute('position')\n        builder.AddFloatAttribute(dracoObject, dracoEncoder.POSITION, vertices.count, vertices.itemSize, vertices.array)\n\n        if (options.exportColor) {\n          const colors = geometry.getAttribute('color')\n\n          if (colors !== undefined) {\n            builder.AddFloatAttribute(dracoObject, dracoEncoder.COLOR, colors.count, colors.itemSize, colors.array)\n          }\n        }\n      } else {\n        throw new Error('DRACOExporter: Unsupported object type.')\n      }\n\n      //Compress using draco encoder\n\n      const encodedData = new dracoEncoder.DracoInt8Array()\n\n      //Sets the desired encoding and decoding speed for the given options from 0 (slowest speed, but the best compression) to 10 (fastest, but the worst compression).\n\n      const encodeSpeed = options.encodeSpeed !== undefined ? options.encodeSpeed : 5\n      const decodeSpeed = options.decodeSpeed !== undefined ? options.decodeSpeed : 5\n\n      encoder.SetSpeedOptions(encodeSpeed, decodeSpeed)\n\n      // Sets the desired encoding method for a given geometry.\n\n      if (options.encoderMethod !== undefined) {\n        encoder.SetEncodingMethod(options.encoderMethod)\n      }\n\n      // Sets the quantization (number of bits used to represent) compression options for a named attribute.\n      // The attribute values will be quantized in a box defined by the maximum extent of the attribute values.\n      if (options.quantization !== undefined) {\n        for (let i = 0; i < 5; i++) {\n          if (options.quantization[i] !== undefined) {\n            encoder.SetAttributeQuantization(i, options.quantization[i])\n          }\n        }\n      }\n\n      let length\n\n      if (object instanceof Mesh && object.isMesh) {\n        length = encoder.EncodeMeshToDracoBuffer(dracoObject, encodedData)\n      } else {\n        // @ts-ignore\n        length = encoder.EncodePointCloudToDracoBuffer(dracoObject, true, encodedData)\n      }\n\n      dracoEncoder.destroy(dracoObject)\n\n      if (length === 0) {\n        throw new Error('THREE.DRACOExporter: Draco encoding failed.')\n      }\n\n      //Copy encoded data to buffer.\n      const outputData = new Int8Array(new ArrayBuffer(length))\n\n      for (let i = 0; i < length; i++) {\n        outputData[i] = encodedData.GetValue(i)\n      }\n\n      dracoEncoder.destroy(encodedData)\n      dracoEncoder.destroy(encoder)\n      dracoEncoder.destroy(builder)\n\n      return outputData\n    }\n  }\n\n  return DRACOExporter\n})()\n\nexport { DRACOExporter }\n"], "names": ["faces", "DRACOExporter"], "mappings": ";;;;;;;AAgBK,MAAC,gBAAiC,uBAAM;AAC3C,QAAM,iBAAN,MAAoB;AAAA,IAmBlB,MACE,QACA,UAAU;AAAA,MACR,aAAa;AAAA,MACb,aAAa;AAAA,MACb,eAAe,eAAc;AAAA,MAC7B,cAAc,CAAC,IAAI,GAAG,GAAG,GAAG,CAAC;AAAA,MAC7B,WAAW;AAAA,MACX,eAAe;AAAA,MACf,aAAa;AAAA,IACd,GACD;AACA,UAAI,kBAAkB,kBAAkB,OAAO,kBAAkB;AAC/D,cAAM,IAAI,MAAM,qFAAqF;AAAA,MACtG;AAED,UAAI,uBAAuB,QAAW;AACpC,cAAM,IAAI,MAAM,0DAA0D;AAAA,MAC3E;AAED,YAAM,WAAW,OAAO;AAExB,YAAM,eAAe,mBAAoB;AACzC,YAAM,UAAU,IAAI,aAAa,QAAS;AAC1C,UAAI;AACJ,UAAI;AAEJ,UAAI,CAAC,SAAS,kBAAkB;AAC9B,cAAM,IAAI;AAAA,UACR;AAAA,QACD;AAAA,MACF;AAED,UAAI,kBAAkB,QAAQ,OAAO,QAAQ;AAC3C,kBAAU,IAAI,aAAa,YAAa;AACxC,sBAAc,IAAI,aAAa,KAAM;AAErC,cAAM,WAAW,SAAS,aAAa,UAAU;AAEjD,gBAAQ;AAAA,UACN;AAAA,UACA,aAAa;AAAA,UACb,SAAS;AAAA,UACT,SAAS;AAAA,UACT,SAAS;AAAA,QACV;AAED,cAAM,QAAQ,SAAS,SAAU;AAEjC,YAAI,UAAU,MAAM;AAClB,kBAAQ,eAAe,aAAa,MAAM,QAAQ,GAAG,MAAM,KAAK;AAAA,QAC1E,OAAe;AACL,gBAAMA,SAAQ,KAAK,SAAS,QAAQ,QAAQ,cAAc,aAAa,SAAS,KAAK;AAErF,mBAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,KAAK;AACrC,YAAAA,OAAM,CAAC,IAAI;AAAA,UACZ;AAED,kBAAQ,eAAe,aAAa,SAAS,OAAOA,MAAK;AAAA,QAC1D;AAED,YAAI,QAAQ,eAAe;AACzB,gBAAM,UAAU,SAAS,aAAa,QAAQ;AAE9C,cAAI,YAAY,QAAW;AAEzB,oBAAQ;AAAA,cACN;AAAA,cACA,aAAa;AAAA,cACb,QAAQ;AAAA,cACR,QAAQ;AAAA,cACR,QAAQ;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAED,YAAI,QAAQ,WAAW;AACrB,gBAAM,MAAM,SAAS,aAAa,IAAI;AAEtC,cAAI,QAAQ,QAAW;AAErB,oBAAQ,wBAAwB,aAAa,aAAa,WAAW,IAAI,OAAO,IAAI,UAAU,IAAI,KAAK;AAAA,UACxG;AAAA,QACF;AAED,YAAI,QAAQ,aAAa;AACvB,gBAAM,SAAS,SAAS,aAAa,OAAO;AAE5C,cAAI,WAAW,QAAW;AAExB,oBAAQ;AAAA,cACN;AAAA,cACA,aAAa;AAAA,cACb,OAAO;AAAA,cACP,OAAO;AAAA,cACP,OAAO;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF,WAAU,kBAAkB,UAAU,OAAO,UAAU;AAEtD,kBAAU,IAAI,aAAa,kBAAmB;AAE9C,sBAAc,IAAI,aAAa,WAAY;AAE3C,cAAM,WAAW,SAAS,aAAa,UAAU;AACjD,gBAAQ,kBAAkB,aAAa,aAAa,UAAU,SAAS,OAAO,SAAS,UAAU,SAAS,KAAK;AAE/G,YAAI,QAAQ,aAAa;AACvB,gBAAM,SAAS,SAAS,aAAa,OAAO;AAE5C,cAAI,WAAW,QAAW;AACxB,oBAAQ,kBAAkB,aAAa,aAAa,OAAO,OAAO,OAAO,OAAO,UAAU,OAAO,KAAK;AAAA,UACvG;AAAA,QACF;AAAA,MACT,OAAa;AACL,cAAM,IAAI,MAAM,yCAAyC;AAAA,MAC1D;AAID,YAAM,cAAc,IAAI,aAAa,eAAgB;AAIrD,YAAM,cAAc,QAAQ,gBAAgB,SAAY,QAAQ,cAAc;AAC9E,YAAM,cAAc,QAAQ,gBAAgB,SAAY,QAAQ,cAAc;AAE9E,cAAQ,gBAAgB,aAAa,WAAW;AAIhD,UAAI,QAAQ,kBAAkB,QAAW;AACvC,gBAAQ,kBAAkB,QAAQ,aAAa;AAAA,MAChD;AAID,UAAI,QAAQ,iBAAiB,QAAW;AACtC,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,cAAI,QAAQ,aAAa,CAAC,MAAM,QAAW;AACzC,oBAAQ,yBAAyB,GAAG,QAAQ,aAAa,CAAC,CAAC;AAAA,UAC5D;AAAA,QACF;AAAA,MACF;AAED,UAAI;AAEJ,UAAI,kBAAkB,QAAQ,OAAO,QAAQ;AAC3C,iBAAS,QAAQ,wBAAwB,aAAa,WAAW;AAAA,MACzE,OAAa;AAEL,iBAAS,QAAQ,8BAA8B,aAAa,MAAM,WAAW;AAAA,MAC9E;AAED,mBAAa,QAAQ,WAAW;AAEhC,UAAI,WAAW,GAAG;AAChB,cAAM,IAAI,MAAM,6CAA6C;AAAA,MAC9D;AAGD,YAAM,aAAa,IAAI,UAAU,IAAI,YAAY,MAAM,CAAC;AAExD,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,mBAAW,CAAC,IAAI,YAAY,SAAS,CAAC;AAAA,MACvC;AAED,mBAAa,QAAQ,WAAW;AAChC,mBAAa,QAAQ,OAAO;AAC5B,mBAAa,QAAQ,OAAO;AAE5B,aAAO;AAAA,IACR;AAAA,EACF;AAjMD,MAAMC,iBAAN;AAGE;AAAA,gBAHIA,gBAGG,6BAA4B;AACnC,gBAJIA,gBAIG,4BAA2B;AAIlC;AAAA,gBARIA,gBAQG,eAAc;AACrB,gBATIA,gBASG,mBAAkB;AAGzB;AAAA,gBAZIA,gBAYG,WAAU;AACjB,gBAbIA,gBAaG,YAAW;AAClB,gBAdIA,gBAcG,UAAS;AAChB,gBAfIA,gBAeG,SAAQ;AACf,gBAhBIA,gBAgBG,aAAY;AACnB,gBAjBIA,gBAiBG,WAAU;AAkLnB,SAAOA;AACT,GAAC;"}