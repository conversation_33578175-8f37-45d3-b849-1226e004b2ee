import { Line2NodeMaterial } from "three/webgpu";
import { LineGeometry } from "../LineGeometry.js";
import { LineSegments2 } from "./LineSegments2.js";

declare class Line2 extends LineSegments2 {
    geometry: LineGeometry;
    material: Line2NodeMaterial;

    readonly isLine2: true;

    constructor(geometry?: LineGeometry, material?: Line2NodeMaterial);
}

export { Line2 };
s from a chain of points.
 */
export class Line2 extends LineSegments2 {
    geometry: LineGeometry;
    material: LineMaterial;

    /**
     * Read-only flag to check if a given object is of type Line2.
     */
    readonly isLine2: true;

    /**
     * @param geometry (optional) Pair(s) of vertices representing each line segment
     * @param material (optional) Material for the line. Default is a {@link LineMaterial} with random color.
     */
    constructor(geometry?: LineGeometry, material?: LineMaterial);
}
