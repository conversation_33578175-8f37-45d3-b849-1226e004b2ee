{"version": 3, "file": "RGBELoader.js", "sources": ["../../src/loaders/RGBELoader.js"], "sourcesContent": ["import { DataTextureLoader, DataUtils, FloatType, HalfFloatType, LinearFilter } from 'three'\n\n// https://github.com/mrdoob/three.js/issues/5552\n// http://en.wikipedia.org/wiki/RGBE_image_format\n\nclass RGBELoader extends DataTextureLoader {\n  constructor(manager) {\n    super(manager)\n\n    this.type = HalfFloatType\n  }\n\n  // adapted from http://www.graphics.cornell.edu/~bjw/rgbe.html\n\n  parse(buffer) {\n    const /* default error routine.  change this to change error handling */\n      rgbe_read_error = 1,\n      rgbe_write_error = 2,\n      rgbe_format_error = 3,\n      rgbe_memory_error = 4,\n      rgbe_error = function (rgbe_error_code, msg) {\n        switch (rgbe_error_code) {\n          case rgbe_read_error:\n            throw new Error('THREE.RGBELoader: Read Error: ' + (msg || ''))\n          case rgbe_write_error:\n            throw new Error('THREE.RGBELoader: Write Error: ' + (msg || ''))\n          case rgbe_format_error:\n            throw new Error('THREE.RGBELoader: Bad File Format: ' + (msg || ''))\n          default:\n          case rgbe_memory_error:\n            throw new Error('THREE.RGBELoader: Memory Error: ' + (msg || ''))\n        }\n      },\n      /* offsets to red, green, and blue components in a data (float) pixel */\n      //RGBE_DATA_RED = 0,\n      //RGBE_DATA_GREEN = 1,\n      //RGBE_DATA_BLUE = 2,\n\n      /* number of floats per pixel, use 4 since stored in rgba image format */\n      //RGBE_DATA_SIZE = 4,\n\n      /* flags indicating which fields in an rgbe_header_info are valid */\n      RGBE_VALID_PROGRAMTYPE = 1,\n      RGBE_VALID_FORMAT = 2,\n      RGBE_VALID_DIMENSIONS = 4,\n      NEWLINE = '\\n',\n      fgets = function (buffer, lineLimit, consume) {\n        const chunkSize = 128\n\n        lineLimit = !lineLimit ? 1024 : lineLimit\n        let p = buffer.pos,\n          i = -1,\n          len = 0,\n          s = '',\n          chunk = String.fromCharCode.apply(null, new Uint16Array(buffer.subarray(p, p + chunkSize)))\n\n        while (0 > (i = chunk.indexOf(NEWLINE)) && len < lineLimit && p < buffer.byteLength) {\n          s += chunk\n          len += chunk.length\n          p += chunkSize\n          chunk += String.fromCharCode.apply(null, new Uint16Array(buffer.subarray(p, p + chunkSize)))\n        }\n\n        if (-1 < i) {\n          /*for (i=l-1; i>=0; i--) {\n\t\t\t\t\t\tbyteCode = m.charCodeAt(i);\n\t\t\t\t\t\tif (byteCode > 0x7f && byteCode <= 0x7ff) byteLen++;\n\t\t\t\t\t\telse if (byteCode > 0x7ff && byteCode <= 0xffff) byteLen += 2;\n\t\t\t\t\t\tif (byteCode >= 0xDC00 && byteCode <= 0xDFFF) i--; //trail surrogate\n\t\t\t\t\t}*/\n          if (false !== consume) buffer.pos += len + i + 1\n          return s + chunk.slice(0, i)\n        }\n\n        return false\n      },\n      /* minimal header reading.  modify if you want to parse more information */\n      RGBE_ReadHeader = function (buffer) {\n        // regexes to parse header info fields\n        const magic_token_re = /^#\\?(\\S+)/,\n          gamma_re = /^\\s*GAMMA\\s*=\\s*(\\d+(\\.\\d+)?)\\s*$/,\n          exposure_re = /^\\s*EXPOSURE\\s*=\\s*(\\d+(\\.\\d+)?)\\s*$/,\n          format_re = /^\\s*FORMAT=(\\S+)\\s*$/,\n          dimensions_re = /^\\s*\\-Y\\s+(\\d+)\\s+\\+X\\s+(\\d+)\\s*$/,\n          // RGBE format header struct\n          header = {\n            valid: 0 /* indicate which fields are valid */,\n\n            string: '' /* the actual header string */,\n\n            comments: '' /* comments found in header */,\n\n            programtype: 'RGBE' /* listed at beginning of file to identify it after \"#?\". defaults to \"RGBE\" */,\n\n            format: '' /* RGBE format, default 32-bit_rle_rgbe */,\n\n            gamma: 1.0 /* image has already been gamma corrected with given gamma. defaults to 1.0 (no correction) */,\n\n            exposure: 1.0 /* a value of 1.0 in an image corresponds to <exposure> watts/steradian/m^2. defaults to 1.0 */,\n\n            width: 0,\n            height: 0 /* image dimensions, width/height */,\n          }\n\n        let line, match\n\n        if (buffer.pos >= buffer.byteLength || !(line = fgets(buffer))) {\n          rgbe_error(rgbe_read_error, 'no header found')\n        }\n\n        /* if you want to require the magic token then uncomment the next line */\n        if (!(match = line.match(magic_token_re))) {\n          rgbe_error(rgbe_format_error, 'bad initial token')\n        }\n\n        header.valid |= RGBE_VALID_PROGRAMTYPE\n        header.programtype = match[1]\n        header.string += line + '\\n'\n\n        while (true) {\n          line = fgets(buffer)\n          if (false === line) break\n          header.string += line + '\\n'\n\n          if ('#' === line.charAt(0)) {\n            header.comments += line + '\\n'\n            continue // comment line\n          }\n\n          if ((match = line.match(gamma_re))) {\n            header.gamma = parseFloat(match[1])\n          }\n\n          if ((match = line.match(exposure_re))) {\n            header.exposure = parseFloat(match[1])\n          }\n\n          if ((match = line.match(format_re))) {\n            header.valid |= RGBE_VALID_FORMAT\n            header.format = match[1] //'32-bit_rle_rgbe';\n          }\n\n          if ((match = line.match(dimensions_re))) {\n            header.valid |= RGBE_VALID_DIMENSIONS\n            header.height = parseInt(match[1], 10)\n            header.width = parseInt(match[2], 10)\n          }\n\n          if (header.valid & RGBE_VALID_FORMAT && header.valid & RGBE_VALID_DIMENSIONS) break\n        }\n\n        if (!(header.valid & RGBE_VALID_FORMAT)) {\n          rgbe_error(rgbe_format_error, 'missing format specifier')\n        }\n\n        if (!(header.valid & RGBE_VALID_DIMENSIONS)) {\n          rgbe_error(rgbe_format_error, 'missing image size specifier')\n        }\n\n        return header\n      },\n      RGBE_ReadPixels_RLE = function (buffer, w, h) {\n        const scanline_width = w\n\n        if (\n          // run length encoding is not allowed so read flat\n          scanline_width < 8 ||\n          scanline_width > 0x7fff ||\n          // this file is not run length encoded\n          2 !== buffer[0] ||\n          2 !== buffer[1] ||\n          buffer[2] & 0x80\n        ) {\n          // return the flat buffer\n          return new Uint8Array(buffer)\n        }\n\n        if (scanline_width !== ((buffer[2] << 8) | buffer[3])) {\n          rgbe_error(rgbe_format_error, 'wrong scanline width')\n        }\n\n        const data_rgba = new Uint8Array(4 * w * h)\n\n        if (!data_rgba.length) {\n          rgbe_error(rgbe_memory_error, 'unable to allocate buffer space')\n        }\n\n        let offset = 0,\n          pos = 0\n\n        const ptr_end = 4 * scanline_width\n        const rgbeStart = new Uint8Array(4)\n        const scanline_buffer = new Uint8Array(ptr_end)\n        let num_scanlines = h\n\n        // read in each successive scanline\n        while (num_scanlines > 0 && pos < buffer.byteLength) {\n          if (pos + 4 > buffer.byteLength) {\n            rgbe_error(rgbe_read_error)\n          }\n\n          rgbeStart[0] = buffer[pos++]\n          rgbeStart[1] = buffer[pos++]\n          rgbeStart[2] = buffer[pos++]\n          rgbeStart[3] = buffer[pos++]\n\n          if (2 != rgbeStart[0] || 2 != rgbeStart[1] || ((rgbeStart[2] << 8) | rgbeStart[3]) != scanline_width) {\n            rgbe_error(rgbe_format_error, 'bad rgbe scanline format')\n          }\n\n          // read each of the four channels for the scanline into the buffer\n          // first red, then green, then blue, then exponent\n          let ptr = 0,\n            count\n\n          while (ptr < ptr_end && pos < buffer.byteLength) {\n            count = buffer[pos++]\n            const isEncodedRun = count > 128\n            if (isEncodedRun) count -= 128\n\n            if (0 === count || ptr + count > ptr_end) {\n              rgbe_error(rgbe_format_error, 'bad scanline data')\n            }\n\n            if (isEncodedRun) {\n              // a (encoded) run of the same value\n              const byteValue = buffer[pos++]\n              for (let i = 0; i < count; i++) {\n                scanline_buffer[ptr++] = byteValue\n              }\n              //ptr += count;\n            } else {\n              // a literal-run\n              scanline_buffer.set(buffer.subarray(pos, pos + count), ptr)\n              ptr += count\n              pos += count\n            }\n          }\n\n          // now convert data from buffer into rgba\n          // first red, then green, then blue, then exponent (alpha)\n          const l = scanline_width //scanline_buffer.byteLength;\n          for (let i = 0; i < l; i++) {\n            let off = 0\n            data_rgba[offset] = scanline_buffer[i + off]\n            off += scanline_width //1;\n            data_rgba[offset + 1] = scanline_buffer[i + off]\n            off += scanline_width //1;\n            data_rgba[offset + 2] = scanline_buffer[i + off]\n            off += scanline_width //1;\n            data_rgba[offset + 3] = scanline_buffer[i + off]\n            offset += 4\n          }\n\n          num_scanlines--\n        }\n\n        return data_rgba\n      }\n\n    const RGBEByteToRGBFloat = function (sourceArray, sourceOffset, destArray, destOffset) {\n      const e = sourceArray[sourceOffset + 3]\n      const scale = Math.pow(2.0, e - 128.0) / 255.0\n\n      destArray[destOffset + 0] = sourceArray[sourceOffset + 0] * scale\n      destArray[destOffset + 1] = sourceArray[sourceOffset + 1] * scale\n      destArray[destOffset + 2] = sourceArray[sourceOffset + 2] * scale\n      destArray[destOffset + 3] = 1\n    }\n\n    const RGBEByteToRGBHalf = function (sourceArray, sourceOffset, destArray, destOffset) {\n      const e = sourceArray[sourceOffset + 3]\n      const scale = Math.pow(2.0, e - 128.0) / 255.0\n\n      // clamping to 65504, the maximum representable value in float16\n      destArray[destOffset + 0] = DataUtils.toHalfFloat(Math.min(sourceArray[sourceOffset + 0] * scale, 65504))\n      destArray[destOffset + 1] = DataUtils.toHalfFloat(Math.min(sourceArray[sourceOffset + 1] * scale, 65504))\n      destArray[destOffset + 2] = DataUtils.toHalfFloat(Math.min(sourceArray[sourceOffset + 2] * scale, 65504))\n      destArray[destOffset + 3] = DataUtils.toHalfFloat(1)\n    }\n\n    const byteArray = new Uint8Array(buffer)\n    byteArray.pos = 0\n    const rgbe_header_info = RGBE_ReadHeader(byteArray)\n\n    const w = rgbe_header_info.width,\n      h = rgbe_header_info.height,\n      image_rgba_data = RGBE_ReadPixels_RLE(byteArray.subarray(byteArray.pos), w, h)\n\n    let data, type\n    let numElements\n\n    switch (this.type) {\n      case FloatType:\n        numElements = image_rgba_data.length / 4\n        const floatArray = new Float32Array(numElements * 4)\n\n        for (let j = 0; j < numElements; j++) {\n          RGBEByteToRGBFloat(image_rgba_data, j * 4, floatArray, j * 4)\n        }\n\n        data = floatArray\n        type = FloatType\n        break\n\n      case HalfFloatType:\n        numElements = image_rgba_data.length / 4\n        const halfArray = new Uint16Array(numElements * 4)\n\n        for (let j = 0; j < numElements; j++) {\n          RGBEByteToRGBHalf(image_rgba_data, j * 4, halfArray, j * 4)\n        }\n\n        data = halfArray\n        type = HalfFloatType\n        break\n\n      default:\n        throw new Error('THREE.RGBELoader: Unsupported type: ' + this.type)\n        break\n    }\n\n    return {\n      width: w,\n      height: h,\n      data: data,\n      header: rgbe_header_info.string,\n      gamma: rgbe_header_info.gamma,\n      exposure: rgbe_header_info.exposure,\n      type: type,\n    }\n  }\n\n  setDataType(value) {\n    this.type = value\n    return this\n  }\n\n  load(url, onLoad, onProgress, onError) {\n    function onLoadCallback(texture, texData) {\n      switch (texture.type) {\n        case FloatType:\n        case HalfFloatType:\n          if ('colorSpace' in texture) texture.colorSpace = 'srgb-linear'\n          else texture.encoding = 3000 // LinearEncoding\n          texture.minFilter = LinearFilter\n          texture.magFilter = LinearFilter\n          texture.generateMipmaps = false\n          texture.flipY = true\n\n          break\n      }\n\n      if (onLoad) onLoad(texture, texData)\n    }\n\n    return super.load(url, onLoadCallback, onProgress, onError)\n  }\n}\n\nexport { RGBELoader }\n"], "names": ["buffer", "w", "h"], "mappings": ";AAKA,MAAM,mBAAmB,kBAAkB;AAAA,EACzC,YAAY,SAAS;AACnB,UAAM,OAAO;AAEb,SAAK,OAAO;AAAA,EACb;AAAA;AAAA,EAID,MAAM,QAAQ;AACZ,UACE,kBAAkB,GAClB,mBAAmB,GACnB,oBAAoB,GACpB,oBAAoB,GACpB,aAAa,SAAU,iBAAiB,KAAK;AAC3C,cAAQ,iBAAe;AAAA,QACrB,KAAK;AACH,gBAAM,IAAI,MAAM,oCAAoC,OAAO,GAAG;AAAA,QAChE,KAAK;AACH,gBAAM,IAAI,MAAM,qCAAqC,OAAO,GAAG;AAAA,QACjE,KAAK;AACH,gBAAM,IAAI,MAAM,yCAAyC,OAAO,GAAG;AAAA,QACrE;AAAA,QACA,KAAK;AACH,gBAAM,IAAI,MAAM,sCAAsC,OAAO,GAAG;AAAA,MACnE;AAAA,IACF,GAUD,yBAAyB,GACzB,oBAAoB,GACpB,wBAAwB,GACxB,UAAU,MACV,QAAQ,SAAUA,SAAQ,WAAW,SAAS;AAC5C,YAAM,YAAY;AAElB,kBAAY,CAAC,YAAY,OAAO;AAChC,UAAI,IAAIA,QAAO,KACb,IAAI,IACJ,MAAM,GACN,IAAI,IACJ,QAAQ,OAAO,aAAa,MAAM,MAAM,IAAI,YAAYA,QAAO,SAAS,GAAG,IAAI,SAAS,CAAC,CAAC;AAE5F,aAAO,KAAK,IAAI,MAAM,QAAQ,OAAO,MAAM,MAAM,aAAa,IAAIA,QAAO,YAAY;AACnF,aAAK;AACL,eAAO,MAAM;AACb,aAAK;AACL,iBAAS,OAAO,aAAa,MAAM,MAAM,IAAI,YAAYA,QAAO,SAAS,GAAG,IAAI,SAAS,CAAC,CAAC;AAAA,MAC5F;AAED,UAAI,KAAK,GAAG;AAOV,YAAI,UAAU;AAAS,UAAAA,QAAO,OAAO,MAAM,IAAI;AAC/C,eAAO,IAAI,MAAM,MAAM,GAAG,CAAC;AAAA,MAC5B;AAED,aAAO;AAAA,IACR,GAED,kBAAkB,SAAUA,SAAQ;AAElC,YAAM,iBAAiB,aACrB,WAAW,qCACX,cAAc,wCACd,YAAY,wBACZ,gBAAgB,qCAEhB,SAAS;AAAA,QACP,OAAO;AAAA,QAEP,QAAQ;AAAA,QAER,UAAU;AAAA,QAEV,aAAa;AAAA,QAEb,QAAQ;AAAA,QAER,OAAO;AAAA,QAEP,UAAU;AAAA,QAEV,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAEH,UAAI,MAAM;AAEV,UAAIA,QAAO,OAAOA,QAAO,cAAc,EAAE,OAAO,MAAMA,OAAM,IAAI;AAC9D,mBAAW,iBAAiB,iBAAiB;AAAA,MAC9C;AAGD,UAAI,EAAE,QAAQ,KAAK,MAAM,cAAc,IAAI;AACzC,mBAAW,mBAAmB,mBAAmB;AAAA,MAClD;AAED,aAAO,SAAS;AAChB,aAAO,cAAc,MAAM,CAAC;AAC5B,aAAO,UAAU,OAAO;AAExB,aAAO,MAAM;AACX,eAAO,MAAMA,OAAM;AACnB,YAAI,UAAU;AAAM;AACpB,eAAO,UAAU,OAAO;AAExB,YAAI,QAAQ,KAAK,OAAO,CAAC,GAAG;AAC1B,iBAAO,YAAY,OAAO;AAC1B;AAAA,QACD;AAED,YAAK,QAAQ,KAAK,MAAM,QAAQ,GAAI;AAClC,iBAAO,QAAQ,WAAW,MAAM,CAAC,CAAC;AAAA,QACnC;AAED,YAAK,QAAQ,KAAK,MAAM,WAAW,GAAI;AACrC,iBAAO,WAAW,WAAW,MAAM,CAAC,CAAC;AAAA,QACtC;AAED,YAAK,QAAQ,KAAK,MAAM,SAAS,GAAI;AACnC,iBAAO,SAAS;AAChB,iBAAO,SAAS,MAAM,CAAC;AAAA,QACxB;AAED,YAAK,QAAQ,KAAK,MAAM,aAAa,GAAI;AACvC,iBAAO,SAAS;AAChB,iBAAO,SAAS,SAAS,MAAM,CAAC,GAAG,EAAE;AACrC,iBAAO,QAAQ,SAAS,MAAM,CAAC,GAAG,EAAE;AAAA,QACrC;AAED,YAAI,OAAO,QAAQ,qBAAqB,OAAO,QAAQ;AAAuB;AAAA,MAC/E;AAED,UAAI,EAAE,OAAO,QAAQ,oBAAoB;AACvC,mBAAW,mBAAmB,0BAA0B;AAAA,MACzD;AAED,UAAI,EAAE,OAAO,QAAQ,wBAAwB;AAC3C,mBAAW,mBAAmB,8BAA8B;AAAA,MAC7D;AAED,aAAO;AAAA,IACR,GACD,sBAAsB,SAAUA,SAAQC,IAAGC,IAAG;AAC5C,YAAM,iBAAiBD;AAEvB;AAAA;AAAA,QAEE,iBAAiB,KACjB,iBAAiB;AAAA,QAEjB,MAAMD,QAAO,CAAC,KACd,MAAMA,QAAO,CAAC,KACdA,QAAO,CAAC,IAAI;AAAA,QACZ;AAEA,eAAO,IAAI,WAAWA,OAAM;AAAA,MAC7B;AAED,UAAI,oBAAqBA,QAAO,CAAC,KAAK,IAAKA,QAAO,CAAC,IAAI;AACrD,mBAAW,mBAAmB,sBAAsB;AAAA,MACrD;AAED,YAAM,YAAY,IAAI,WAAW,IAAIC,KAAIC,EAAC;AAE1C,UAAI,CAAC,UAAU,QAAQ;AACrB,mBAAW,mBAAmB,iCAAiC;AAAA,MAChE;AAED,UAAI,SAAS,GACX,MAAM;AAER,YAAM,UAAU,IAAI;AACpB,YAAM,YAAY,IAAI,WAAW,CAAC;AAClC,YAAM,kBAAkB,IAAI,WAAW,OAAO;AAC9C,UAAI,gBAAgBA;AAGpB,aAAO,gBAAgB,KAAK,MAAMF,QAAO,YAAY;AACnD,YAAI,MAAM,IAAIA,QAAO,YAAY;AAC/B,qBAAW,eAAe;AAAA,QAC3B;AAED,kBAAU,CAAC,IAAIA,QAAO,KAAK;AAC3B,kBAAU,CAAC,IAAIA,QAAO,KAAK;AAC3B,kBAAU,CAAC,IAAIA,QAAO,KAAK;AAC3B,kBAAU,CAAC,IAAIA,QAAO,KAAK;AAE3B,YAAI,KAAK,UAAU,CAAC,KAAK,KAAK,UAAU,CAAC,MAAO,UAAU,CAAC,KAAK,IAAK,UAAU,CAAC,MAAM,gBAAgB;AACpG,qBAAW,mBAAmB,0BAA0B;AAAA,QACzD;AAID,YAAI,MAAM,GACR;AAEF,eAAO,MAAM,WAAW,MAAMA,QAAO,YAAY;AAC/C,kBAAQA,QAAO,KAAK;AACpB,gBAAM,eAAe,QAAQ;AAC7B,cAAI;AAAc,qBAAS;AAE3B,cAAI,MAAM,SAAS,MAAM,QAAQ,SAAS;AACxC,uBAAW,mBAAmB,mBAAmB;AAAA,UAClD;AAED,cAAI,cAAc;AAEhB,kBAAM,YAAYA,QAAO,KAAK;AAC9B,qBAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,8BAAgB,KAAK,IAAI;AAAA,YAC1B;AAAA,UAEf,OAAmB;AAEL,4BAAgB,IAAIA,QAAO,SAAS,KAAK,MAAM,KAAK,GAAG,GAAG;AAC1D,mBAAO;AACP,mBAAO;AAAA,UACR;AAAA,QACF;AAID,cAAM,IAAI;AACV,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,cAAI,MAAM;AACV,oBAAU,MAAM,IAAI,gBAAgB,IAAI,GAAG;AAC3C,iBAAO;AACP,oBAAU,SAAS,CAAC,IAAI,gBAAgB,IAAI,GAAG;AAC/C,iBAAO;AACP,oBAAU,SAAS,CAAC,IAAI,gBAAgB,IAAI,GAAG;AAC/C,iBAAO;AACP,oBAAU,SAAS,CAAC,IAAI,gBAAgB,IAAI,GAAG;AAC/C,oBAAU;AAAA,QACX;AAED;AAAA,MACD;AAED,aAAO;AAAA,IACR;AAEH,UAAM,qBAAqB,SAAU,aAAa,cAAc,WAAW,YAAY;AACrF,YAAM,IAAI,YAAY,eAAe,CAAC;AACtC,YAAM,QAAQ,KAAK,IAAI,GAAK,IAAI,GAAK,IAAI;AAEzC,gBAAU,aAAa,CAAC,IAAI,YAAY,eAAe,CAAC,IAAI;AAC5D,gBAAU,aAAa,CAAC,IAAI,YAAY,eAAe,CAAC,IAAI;AAC5D,gBAAU,aAAa,CAAC,IAAI,YAAY,eAAe,CAAC,IAAI;AAC5D,gBAAU,aAAa,CAAC,IAAI;AAAA,IAC7B;AAED,UAAM,oBAAoB,SAAU,aAAa,cAAc,WAAW,YAAY;AACpF,YAAM,IAAI,YAAY,eAAe,CAAC;AACtC,YAAM,QAAQ,KAAK,IAAI,GAAK,IAAI,GAAK,IAAI;AAGzC,gBAAU,aAAa,CAAC,IAAI,UAAU,YAAY,KAAK,IAAI,YAAY,eAAe,CAAC,IAAI,OAAO,KAAK,CAAC;AACxG,gBAAU,aAAa,CAAC,IAAI,UAAU,YAAY,KAAK,IAAI,YAAY,eAAe,CAAC,IAAI,OAAO,KAAK,CAAC;AACxG,gBAAU,aAAa,CAAC,IAAI,UAAU,YAAY,KAAK,IAAI,YAAY,eAAe,CAAC,IAAI,OAAO,KAAK,CAAC;AACxG,gBAAU,aAAa,CAAC,IAAI,UAAU,YAAY,CAAC;AAAA,IACpD;AAED,UAAM,YAAY,IAAI,WAAW,MAAM;AACvC,cAAU,MAAM;AAChB,UAAM,mBAAmB,gBAAgB,SAAS;AAElD,UAAM,IAAI,iBAAiB,OACzB,IAAI,iBAAiB,QACrB,kBAAkB,oBAAoB,UAAU,SAAS,UAAU,GAAG,GAAG,GAAG,CAAC;AAE/E,QAAI,MAAM;AACV,QAAI;AAEJ,YAAQ,KAAK,MAAI;AAAA,MACf,KAAK;AACH,sBAAc,gBAAgB,SAAS;AACvC,cAAM,aAAa,IAAI,aAAa,cAAc,CAAC;AAEnD,iBAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,6BAAmB,iBAAiB,IAAI,GAAG,YAAY,IAAI,CAAC;AAAA,QAC7D;AAED,eAAO;AACP,eAAO;AACP;AAAA,MAEF,KAAK;AACH,sBAAc,gBAAgB,SAAS;AACvC,cAAM,YAAY,IAAI,YAAY,cAAc,CAAC;AAEjD,iBAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,4BAAkB,iBAAiB,IAAI,GAAG,WAAW,IAAI,CAAC;AAAA,QAC3D;AAED,eAAO;AACP,eAAO;AACP;AAAA,MAEF;AACE,cAAM,IAAI,MAAM,yCAAyC,KAAK,IAAI;AAAA,IAErE;AAED,WAAO;AAAA,MACL,OAAO;AAAA,MACP,QAAQ;AAAA,MACR;AAAA,MACA,QAAQ,iBAAiB;AAAA,MACzB,OAAO,iBAAiB;AAAA,MACxB,UAAU,iBAAiB;AAAA,MAC3B;AAAA,IACD;AAAA,EACF;AAAA,EAED,YAAY,OAAO;AACjB,SAAK,OAAO;AACZ,WAAO;AAAA,EACR;AAAA,EAED,KAAK,KAAK,QAAQ,YAAY,SAAS;AACrC,aAAS,eAAe,SAAS,SAAS;AACxC,cAAQ,QAAQ,MAAI;AAAA,QAClB,KAAK;AAAA,QACL,KAAK;AACH,cAAI,gBAAgB;AAAS,oBAAQ,aAAa;AAAA;AAC7C,oBAAQ,WAAW;AACxB,kBAAQ,YAAY;AACpB,kBAAQ,YAAY;AACpB,kBAAQ,kBAAkB;AAC1B,kBAAQ,QAAQ;AAEhB;AAAA,MACH;AAED,UAAI;AAAQ,eAAO,SAAS,OAAO;AAAA,IACpC;AAED,WAAO,MAAM,KAAK,KAAK,gBAAgB,YAAY,OAAO;AAAA,EAC3D;AACH;"}