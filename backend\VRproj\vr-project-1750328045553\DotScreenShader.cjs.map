{"version": 3, "file": "DotScreenShader.cjs", "sources": ["../../src/shaders/DotScreenShader.ts"], "sourcesContent": ["import { Vector2 } from 'three'\n\n/**\n * Dot screen shader\n * based on glfx.js sepia shader\n * https://github.com/evanw/glfx.js\n */\n\nexport const DotScreenShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    tSize: { value: /* @__PURE__ */ new Vector2(256, 256) },\n    center: { value: /* @__PURE__ */ new Vector2(0.5, 0.5) },\n    angle: { value: 1.57 },\n    scale: { value: 1.0 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform vec2 center;\n    uniform float angle;\n    uniform float scale;\n    uniform vec2 tSize;\n\n    uniform sampler2D tDiffuse;\n\n    varying vec2 vUv;\n\n    float pattern() {\n\n    \tfloat s = sin( angle ), c = cos( angle );\n\n    \tvec2 tex = vUv * tSize - center;\n    \tvec2 point = vec2( c * tex.x - s * tex.y, s * tex.x + c * tex.y ) * scale;\n\n    \treturn ( sin( point.x ) * sin( point.y ) ) * 4.0;\n\n    }\n\n    void main() {\n\n    \tvec4 color = texture2D( tDiffuse, vUv );\n\n    \tfloat average = ( color.r + color.g + color.b ) / 3.0;\n\n    \tgl_FragColor = vec4( vec3( average * 10.0 - 5.0 + pattern() ), color.a );\n\n    }\n  `,\n}\n"], "names": ["Vector2"], "mappings": ";;;AAQO,MAAM,kBAAkB;AAAA,EAC7B,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA,IACxB,OAAO,EAAE,2BAA2BA,MAAQ,QAAA,KAAK,GAAG,EAAE;AAAA,IACtD,QAAQ,EAAE,2BAA2BA,MAAQ,QAAA,KAAK,GAAG,EAAE;AAAA,IACvD,OAAO,EAAE,OAAO,KAAK;AAAA,IACrB,OAAO,EAAE,OAAO,EAAI;AAAA,EACtB;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA+B7B;;"}