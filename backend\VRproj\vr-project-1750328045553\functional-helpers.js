"use strict";
// Copyright (c) 2016 Piotr Witek <<EMAIL>> (http://piotrwitek.github.io)
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * @function getReturnOfExpression
 * @deprecated from TS v2.8 use built-in ReturnType<T> or $Call API
 * @description infer the return type from a given "expression" (at runtime it's equivalent of "noop")
 * @template RT - ReturnType
 * @param expression: (...params: any[]) => RT
 * @returns undefined as RT
 */
function getReturnOfExpression(expression) {
    return undefined;
}
exports.getReturnOfExpression = getReturnOfExpression;
//# sourceMappingURL=functional-helpers.js.map