{"version": 3, "file": "DotScreenPass.cjs", "sources": ["../../src/postprocessing/DotScreenPass.ts"], "sourcesContent": ["import { Pass, FullScreenQuad } from './Pass'\nimport { IUniform, ShaderMaterial, UniformsUtils, Vector2, WebGLRenderer, WebGLRenderTarget } from 'three'\nimport { DotScreenShader } from '../shaders/DotScreenShader'\n\nclass DotScreenPass extends Pass {\n  public material: ShaderMaterial\n  public fsQuad: FullScreenQuad\n\n  public uniforms: Record<keyof typeof DotScreenShader['uniforms'], IUniform<any>>\n\n  constructor(center?: Vector2, angle?: number, scale?: number) {\n    super()\n    if (DotScreenShader === undefined) console.error('THREE.DotScreenPass relies on THREE.DotScreenShader')\n    const shader = DotScreenShader\n    this.uniforms = UniformsUtils.clone(shader.uniforms)\n    if (center !== undefined) this.uniforms['center'].value.copy(center)\n    if (angle !== undefined) this.uniforms['angle'].value = angle\n    if (scale !== undefined) this.uniforms['scale'].value = scale\n    this.material = new ShaderMaterial({\n      uniforms: this.uniforms,\n      vertexShader: shader.vertexShader,\n      fragmentShader: shader.fragmentShader,\n    })\n    this.fsQuad = new FullScreenQuad(this.material)\n  }\n\n  public render(\n    renderer: WebGLRenderer,\n    writeBuffer: WebGLRenderTarget,\n    readBuffer: WebGLRenderTarget,\n    /*, deltaTime, maskActive */\n  ): void {\n    this.uniforms['tDiffuse'].value = readBuffer.texture\n    this.uniforms['tSize'].value.set(readBuffer.width, readBuffer.height)\n\n    if (this.renderToScreen) {\n      renderer.setRenderTarget(null)\n      this.fsQuad.render(renderer)\n    } else {\n      renderer.setRenderTarget(writeBuffer)\n      if (this.clear) renderer.clear()\n      this.fsQuad.render(renderer)\n    }\n  }\n}\n\nexport { DotScreenPass }\n"], "names": ["Pass", "DotScreenShader", "UniformsUtils", "ShaderMaterial", "FullScreenQuad"], "mappings": ";;;;;;;;;;;AAIA,MAAM,sBAAsBA,KAAAA,KAAK;AAAA,EAM/B,YAAY,QAAkB,OAAgB,OAAgB;AACtD;AAND;AACA;AAEA;AAIL,QAAIC,gBAAAA,oBAAoB;AAAW,cAAQ,MAAM,qDAAqD;AACtG,UAAM,SAASA,gBAAAA;AACf,SAAK,WAAWC,MAAA,cAAc,MAAM,OAAO,QAAQ;AACnD,QAAI,WAAW;AAAW,WAAK,SAAS,QAAQ,EAAE,MAAM,KAAK,MAAM;AACnE,QAAI,UAAU;AAAgB,WAAA,SAAS,OAAO,EAAE,QAAQ;AACxD,QAAI,UAAU;AAAgB,WAAA,SAAS,OAAO,EAAE,QAAQ;AACnD,SAAA,WAAW,IAAIC,qBAAe;AAAA,MACjC,UAAU,KAAK;AAAA,MACf,cAAc,OAAO;AAAA,MACrB,gBAAgB,OAAO;AAAA,IAAA,CACxB;AACD,SAAK,SAAS,IAAIC,KAAe,eAAA,KAAK,QAAQ;AAAA,EAChD;AAAA,EAEO,OACL,UACA,aACA,YAEM;AACN,SAAK,SAAS,UAAU,EAAE,QAAQ,WAAW;AACxC,SAAA,SAAS,OAAO,EAAE,MAAM,IAAI,WAAW,OAAO,WAAW,MAAM;AAEpE,QAAI,KAAK,gBAAgB;AACvB,eAAS,gBAAgB,IAAI;AACxB,WAAA,OAAO,OAAO,QAAQ;AAAA,IAAA,OACtB;AACL,eAAS,gBAAgB,WAAW;AACpC,UAAI,KAAK;AAAO,iBAAS,MAAM;AAC1B,WAAA,OAAO,OAAO,QAAQ;AAAA,IAC7B;AAAA,EACF;AACF;;"}