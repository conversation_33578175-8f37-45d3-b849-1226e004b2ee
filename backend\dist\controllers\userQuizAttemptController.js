"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getQuizAttemptById = exports.getLeaderboard = exports.getUserQuizAttempts = exports.submitQuizAttempt = void 0;
const UserQuizAttempt_1 = __importDefault(require("../models/UserQuizAttempt"));
const Quiz_1 = __importDefault(require("../models/Quiz"));
const User_1 = __importDefault(require("../models/User"));
const mongoose_1 = __importDefault(require("mongoose"));
// Submit a quiz attempt
const submitQuizAttempt = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { quizId, answers } = req.body;
        const userId = req.user._id; // Assuming user ID is attached by auth middleware
        if (!quizId || !answers) {
            return res.status(400).json({ message: 'Quiz ID and answers are required' });
        }
        const quiz = yield Quiz_1.default.findById(quizId);
        if (!quiz) {
            return res.status(404).json({ message: 'Quiz not found' });
        }
        let score = 0;
        // Calculate score based on submitted answers and correct answers in the quiz
        // Assuming 'answers' is an array of objects like { questionId: string, selectedAnswerText: string }
        for (const submittedAnswer of answers) {
            // Find the question by its _id
            const question = quiz.questions.find(q => q._id && q._id.toString() === submittedAnswer.questionId);
            if (question) {
                // Find the correct answer for the question
                const correctAnswer = question.answers.find((ans) => ans.isCorrect);
                if (correctAnswer && correctAnswer.text === submittedAnswer.selectedAnswerText) {
                    score += question.points;
                }
            }
        }
        const newUserQuizAttempt = new UserQuizAttempt_1.default({
            userId: new mongoose_1.default.Types.ObjectId(userId),
            quizId: new mongoose_1.default.Types.ObjectId(quizId),
            score,
            completedAt: new Date(),
        });
        const savedAttempt = yield newUserQuizAttempt.save();
        // Update user's total points
        const user = yield User_1.default.findById(userId);
        if (user) {
            user.points = (user.points || 0) + score;
            yield user.save();
        }
        res.status(201).json(savedAttempt);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.submitQuizAttempt = submitQuizAttempt;
// Get user's quiz attempt history
const getUserQuizAttempts = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user._id; // Assuming user ID is attached by auth middleware
        const attempts = yield UserQuizAttempt_1.default.find({ userId: new mongoose_1.default.Types.ObjectId(userId) })
            .populate('quizId', 'title description') // Populate quiz details
            .sort({ completedAt: -1 }); // Sort by latest attempts first
        res.status(200).json(attempts);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.getUserQuizAttempts = getUserQuizAttempts;
// Get leaderboard (top users by points)
const getLeaderboard = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { limit = 10 } = req.query;
        const leaderboard = yield User_1.default.find()
            .select('username points') // Select only username and points
            .sort({ points: -1 }) // Sort by points descending
            .limit(Number(limit));
        res.status(200).json(leaderboard);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.getLeaderboard = getLeaderboard;
// Get a specific quiz attempt by ID
const getQuizAttemptById = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const attemptId = req.params.id;
        const userId = req.user._id; // Assuming user ID is attached by auth middleware
        const attempt = yield UserQuizAttempt_1.default.findOne({
            _id: attemptId,
            userId: new mongoose_1.default.Types.ObjectId(userId),
        }).populate('quizId'); // Populate quiz details
        if (!attempt) {
            return res.status(404).json({ message: 'Quiz attempt not found or does not belong to user' });
        }
        res.status(200).json(attempt);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.getQuizAttemptById = getQuizAttemptById;
