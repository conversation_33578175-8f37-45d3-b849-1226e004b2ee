System.register(["immer"],function(c){"use strict";var r;return{setters:[function(n){r=n.produce}],execute:function(){const S=c("immer",s=>(o,u,t)=>(t.setState=(e,i,...m)=>{const f=typeof e=="function"?r(e):e;return o(f,i,...m)},s(t.setState,u,t)))}}});
}(this,(function(e,t){"use strict";var n=function(e){return function(n,r,i){return i.setState=function(e,r){for(var i="function"==typeof e?t.produce(e):e,o=arguments.length,f=new Array(o>2?o-2:0),u=2;u<o;u++)f[u-2]=arguments[u];return n.apply(void 0,[i,r].concat(f))},e(i.setState,r,i)}};e.immer=n}));
