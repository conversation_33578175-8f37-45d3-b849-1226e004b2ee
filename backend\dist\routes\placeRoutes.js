"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const placeController_1 = require("../controllers/placeController");
const router = express_1.default.Router();
// GET all places
router.get('/', placeController_1.getPlaces);
// GET single place by ID
router.get('/:id', placeController_1.getPlaceById);
// POST create new place
router.post('/', placeController_1.createPlace);
// PUT update existing place
router.put('/:id', placeController_1.updatePlace);
// DELETE place
router.delete('/:id', placeController_1.deletePlace);
// GET places by category
router.get('/category/:category', placeController_1.getPlacesByCategory);
exports.default = router;
