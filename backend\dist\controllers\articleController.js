"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateArticleStatus = exports.deleteArticle = exports.updateArticle = exports.getArticleBySlug = exports.getArticleById = exports.getArticles = exports.createArticle = void 0;
const Article_1 = __importDefault(require("../models/Article"));
// Create a new article
const createArticle = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { title, description, content, excerpt, category, tags, status, author } = req.body;
        const images = ((_a = req.files) === null || _a === void 0 ? void 0 : _a.map(file => `/uploads/${file.filename}`)) || [];
        const newArticle = new Article_1.default({
            title,
            description,
            content,
            excerpt,
            category,
            tags: tags ? tags.split(',').map((tag) => tag.trim()) : [],
            images,
            status,
            author,
        });
        const savedArticle = yield newArticle.save();
        res.status(201).json(savedArticle);
    }
    catch (error) {
        console.error('Error creating article:', error);
        res.status(500).json({ message: error.message });
    }
});
exports.createArticle = createArticle;
// Get all articles with filtering, pagination, and sorting
const getArticles = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page = 1, limit = 10, status, category, tag, search, sortBy, sortOrder } = req.query;
        const query = {};
        if (status) {
            query.status = status;
        }
        if (category) {
            query.category = category;
        }
        if (tag) {
            query.tag = tag;
        }
        if (search) {
            query.$or = [
                { title: { $regex: search, $options: 'i' } },
                { content: { $regex: search, $options: 'i' } },
            ];
        }
        const sort = {};
        if (sortBy) {
            sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
        }
        else {
            sort.createdAt = -1; // Default sort by creation date descending
        }
        const options = {
            skip: (Number(page) - 1) * Number(limit),
            limit: Number(limit),
            sort,
        };
        const articles = yield Article_1.default.find(query, null, options);
        const total = yield Article_1.default.countDocuments(query);
        res.status(200).json({
            data: articles,
            pagination: {
                total,
                page: Number(page),
                limit: Number(limit),
                pages: Math.ceil(total / Number(limit)),
            },
        });
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.getArticles = getArticles;
// Get a single article by ID
const getArticleById = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const article = yield Article_1.default.findById(req.params.id);
        if (!article) {
            return res.status(404).json({ message: 'Article not found' });
        }
        res.status(200).json(article);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.getArticleById = getArticleById;
// Get a single article by slug
const getArticleBySlug = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const article = yield Article_1.default.findOne({ slug: req.params.slug });
        if (!article) {
            return res.status(404).json({ message: 'Article not found' });
        }
        res.status(200).json(article);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.getArticleBySlug = getArticleBySlug;
// Update an article by ID
const updateArticle = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { title, description, content, excerpt, category, tags, status, author, images } = req.body;
        const updatedArticle = yield Article_1.default.findByIdAndUpdate(req.params.id, {
            title,
            description,
            content,
            excerpt,
            category,
            tags: tags ? tags.split(',').map((tag) => tag.trim()) : [],
            images: images || [], // Use images directly from body, default to empty array
            status,
            author,
        }, { new: true });
        if (!updatedArticle) {
            return res.status(404).json({ message: 'Article not found' });
        }
        res.status(200).json(updatedArticle);
    }
    catch (error) {
        console.error('Error updating article:', error);
        res.status(500).json({ message: error.message });
    }
});
exports.updateArticle = updateArticle;
// Delete an article by ID
const deleteArticle = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const deletedArticle = yield Article_1.default.findByIdAndDelete(req.params.id);
        if (!deletedArticle) {
            return res.status(404).json({ message: 'Article not found' });
        }
        res.status(200).json({ message: 'Article deleted' });
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.deleteArticle = deleteArticle;
// Update article status by ID
const updateArticleStatus = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { status } = req.body;
        const updatedArticle = yield Article_1.default.findByIdAndUpdate(req.params.id, { status }, { new: true });
        if (!updatedArticle) {
            return res.status(404).json({ message: 'Article not found' });
        }
        res.status(200).json(updatedArticle);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.updateArticleStatus = updateArticleStatus;
