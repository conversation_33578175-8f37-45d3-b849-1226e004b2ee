
import { useState } from "react";
import { Experience, DisplayStyle } from "@/types/content";
import { useContentData } from "@/hooks/useContentData";
import { LoadingState } from "@/components/ui/content/LoadingState";
import { ErrorState } from "@/components/ui/content/ErrorState";
import { ContentHeader } from "@/components/ui/content/ContentHeader";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  FileText,
  MapPin,
  Calendar,
  DollarSign,
  Timer,
  Star,
  Headset as HeadsetIcon,
  Upload,
  FolderOpen
} from "lucide-react";
import { experienceService } from "@/services/contentService";
import { experienceService as apiExperienceService } from "@/api/services/experienceService";

interface ExperiencesManagerProps {
  displayStyle: DisplayStyle;
  onDisplayStyleChange: (style: DisplayStyle) => void;
}

export function ExperiencesManager({ displayStyle, onDisplayStyleChange }: ExperiencesManagerProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [dialogOpen, setDialogOpen] = useState(false);
  const [currentExperience, setCurrentExperience] = useState<Partial<Experience>>({
    title: "",
    description: "",
    location: "",
    category: "",
    price: 0,
    duration: 0,
    isFeatured: false,
    isVR: false,
    isAR: false,
    status: "draft",
  });
  const [isEditing, setIsEditing] = useState(false);
  const [vrFiles, setVrFiles] = useState<FileList | null>(null);
  const [vrDescription, setVrDescription] = useState("");
  const [vrControls, setVrControls] = useState("");
  const [isUploading, setIsUploading] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);

  const { 
    items: experiences, 
    isLoading, 
    isError, 
    createItem, 
    updateItem, 
    deleteItem, 
    toggleStatus 
  } = useContentData<Experience>("experience");

  const filteredExperiences = experiences.filter((experience) => {
    const matchesSearch = experience.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         experience.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === "all" || experience.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleAddNew = () => {
    setCurrentExperience({
      title: "",
      description: "",
      location: "",
      category: "",
      price: 0,
      duration: 0,
      isFeatured: false,
      isVR: false,
      isAR: false,
      status: "draft",
    });
    setIsEditing(false);
    setDialogOpen(true);
  };

  const handleEdit = (experience: Experience) => {
    setCurrentExperience(experience);
    setIsEditing(true);
    setDialogOpen(true);
  };

  const handleDelete = async (id: string) => {
    if (window.confirm("Are you sure you want to delete this experience?")) {
      await deleteItem(id);
    }
  };

  const handleStatusChange = async (id: string, status: Experience["status"]) => {
    await toggleStatus(id, status);
  };

  const handleToggleFeatured = async (id: string, isFeatured: boolean) => {
    await apiExperienceService.toggleFeatured(id, isFeatured);
    // Refresh the data
    setTimeout(() => {
      window.location.reload();
    }, 500);
  };

  const handleLaunchVR = async (experience: Experience) => {
    if (!experience.isVR) return;

    try {
      const vrInfo = await apiExperienceService.getVRLaunchInfo(experience.id);
      const launchUrl = `http://localhost:5000${vrInfo.vrProject.launchUrl}`;

      // Open VR experience in new tab
      window.open(launchUrl, '_blank');
    } catch (error) {
      console.error('Error launching VR experience:', error);
      alert('Failed to launch VR experience. Please try again.');
    }
  };

  const handleSubmit = async () => {
    if (!currentExperience.title) return;

    // If it's a VR experience with files, use VR upload endpoint
    if (currentExperience.isVR && vrFiles && vrFiles.length > 0 && !isEditing) {
      await handleVRSubmit();
      return;
    }

    if (isEditing && currentExperience.id) {
      await updateItem(currentExperience.id, currentExperience);
    } else {
      await createItem(currentExperience as Omit<Experience, "id" | "created" | "updated">);
    }

    setDialogOpen(false);
  };

  const handleVRSync = async () => {
    try {
      setIsSyncing(true);
      const response = await fetch('http://localhost:5000/api/experiences/vr-sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        window.location.reload();
        alert('Synchronisation VR terminée avec succès!');
      } else {
        throw new Error('Erreur lors de la synchronisation');
      }
    } catch (error) {
      console.error('VR sync error:', error);
      alert('Erreur lors de la synchronisation VR');
    } finally {
      setIsSyncing(false);
    }
  };

  const handleVRSubmit = async () => {
    console.log('=== VR SUBMIT DEBUG ===');
    console.log('VR Files:', vrFiles);
    console.log('VR Files length:', vrFiles?.length);
    console.log('Current Experience:', currentExperience);

    if (!vrFiles || vrFiles.length === 0) {
      console.log('No VR files selected, aborting upload');
      alert('Please select VR project files before uploading.');
      return;
    }

    const formData = new FormData();

    // Add experience data
    Object.entries(currentExperience).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        formData.append(key, String(value));
      }
    });

    // Add VR-specific data
    formData.append('vrDescription', vrDescription);
    formData.append('vrControls', vrControls);
    formData.append('projectId', `vr-project-${Date.now()}`);

    // Add all VR files
    Array.from(vrFiles).forEach((file: File) => {
      formData.append('vrFiles', file);
    });

    try {
      setIsUploading(true);
      console.log('Starting VR upload with', vrFiles?.length, 'files');
      console.log('Upload may take several minutes for large VR projects...');
      console.log('FormData contents:');
      for (let [key, value] of formData.entries()) {
        console.log(key, value);
      }

      const result = await apiExperienceService.createVRExperience(formData);
      console.log('VR Experience created:', result);

      // Reset form
      setCurrentExperience({
        title: "",
        description: "",
        location: "",
        category: "",
        price: 0,
        duration: 0,
        isFeatured: false,
        isVR: false,
        isAR: false,
        status: "draft",
      });
      setVrFiles(null);
      setVrDescription('');
      setVrControls('');

      // Refresh the experiences list
      window.location.reload();

      alert(`VR Experience créée avec succès! Upload terminé.`);

    } catch (error: any) {
      console.error('Error uploading VR experience:', error);

      let errorMessage = 'Failed to upload VR experience. Please try again.';

      if (error.code === 'ERR_NETWORK' || error.message === 'Network Error') {
        errorMessage = 'Upload en cours... Le serveur traite votre projet VR. Veuillez vérifier la liste des expériences dans quelques minutes.';

        // Try to refresh the experiences list after a delay
        setTimeout(async () => {
          try {
            window.location.reload();
          } catch (refreshError) {
            console.error('Error refreshing experiences:', refreshError);
          }
        }, 10000); // Wait 10 seconds then refresh

      } else if (error.response?.status === 413 || error.message?.includes('too large')) {
        errorMessage = 'Upload failed: Files are too large. Please reduce file sizes.';
      } else if (error.response?.status === 400) {
        errorMessage = 'Upload failed: Invalid file format or missing required files.';
      }

      alert(errorMessage);
    } finally {
      setIsUploading(false);
    }

    setDialogOpen(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    let parsedValue: string | number = value;
    
    if (name === "price" || name === "duration") {
      parsedValue = parseFloat(value) || 0;
    }
    
    setCurrentExperience(prev => ({ ...prev, [name]: parsedValue }));
  };

  const handleSwitchChange = (name: string, checked: boolean) => {
    setCurrentExperience(prev => ({ ...prev, [name]: checked }));
  };

  const getStatusBadge = (status: Experience["status"]) => {
    switch (status) {
      case "published":
        return <Badge variant="success">Published</Badge>;
      case "draft":
        return <Badge variant="secondary">Draft</Badge>;
      case "review":
        return <Badge variant="warning">In Review</Badge>;
      case "archived":
        return <Badge variant="outline">Archived</Badge>;
    }
  };

  const formatPrice = (price?: number) => {
    if (price === undefined) return "Free";
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price);
  };

  if (isLoading) return <LoadingState />;
  if (isError) return <ErrorState onRetry={() => window.location.reload()} />;

  return (
    <>
      <div className="space-y-4">
        <ContentHeader
          contentType="experience"
          title="Experiences Management"
          displayStyle={displayStyle}
          searchQuery={searchQuery}
          statusFilter={statusFilter}
          onDisplayStyleChange={onDisplayStyleChange}
          onSearchChange={setSearchQuery}
          onStatusFilterChange={setStatusFilter}
          onAddNew={handleAddNew}
          onPreview={() => window.open('/preview/experiences', '_blank')}
        />

        {/* VR Sync Button */}
        <div className="flex justify-end gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={async () => {
              try {
                const response = await fetch('http://localhost:5000/api/test-upload', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({ test: 'data' })
                });
                const result = await response.json();
                alert(`Test successful: ${result.message}`);
              } catch (error) {
                alert(`Test failed: ${error}`);
              }
            }}
            className="flex items-center gap-2"
          >
            Test Upload
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleVRSync}
            disabled={isSyncing}
            className="flex items-center gap-2"
          >
            {isSyncing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                Synchronisation VR...
              </>
            ) : (
              <>
                <HeadsetIcon className="h-4 w-4" />
                Synchroniser VR
              </>
            )}
          </Button>
        </div>

        {displayStyle === "grid" ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredExperiences.map((experience) => (
              <Card key={experience.id} className={`overflow-hidden ${experience.isFeatured ? 'border-admin-orange' : ''}`}>
                <CardContent className="p-0">
                  <div className="relative aspect-[4/3] bg-gradient-to-br from-admin-blue/20 to-admin-purple/20 flex items-center justify-center">
                    <HeadsetIcon className="h-12 w-12 text-muted-foreground opacity-50" />
                    <div className="absolute top-2 right-2">
                      {getStatusBadge(experience.status)}
                    </div>
                    {experience.isFeatured && (
                      <div className="absolute top-2 left-2">
                        <Badge variant="outline" className="bg-admin-orange/10 text-admin-orange">Featured</Badge>
                      </div>
                    )}
                    <div className="absolute bottom-2 left-2 flex flex-wrap gap-1">
                      {experience.isVR && (
                        <Badge variant="outline" className="bg-admin-purple/10 text-admin-purple">VR</Badge>
                      )}
                      {experience.isAR && (
                        <Badge variant="outline" className="bg-admin-cyan/10 text-admin-cyan">AR</Badge>
                      )}
                    </div>
                  </div>
                  <div className="p-3 space-y-2">
                    <h3 className="font-semibold">{experience.title}</h3>
                    <p className="text-sm text-muted-foreground line-clamp-2">
                      {experience.description}
                    </p>
                    <div className="flex flex-wrap gap-3 text-xs text-muted-foreground">
                      {experience.location && (
                        <div className="flex items-center gap-1">
                          <MapPin className="h-3.5 w-3.5" />
                          <span>{experience.location}</span>
                        </div>
                      )}
                      {experience.price !== undefined && (
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-3.5 w-3.5" />
                          <span>{formatPrice(experience.price)}</span>
                        </div>
                      )}
                      {experience.duration && (
                        <div className="flex items-center gap-1">
                          <Timer className="h-3.5 w-3.5" />
                          <span>{experience.duration} min</span>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="border-t p-2 flex justify-end">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">Actions</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      {experience.isVR && (
                        <DropdownMenuItem
                          className="flex items-center gap-2"
                          onClick={() => handleLaunchVR(experience)}
                        >
                          <HeadsetIcon className="h-4 w-4" />
                          Launch VR
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem className="flex items-center gap-2">
                        <Eye className="h-4 w-4" />
                        Preview
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        className="flex items-center gap-2"
                        onClick={() => handleEdit(experience)}
                      >
                        <Edit className="h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="flex items-center gap-2"
                        onClick={() => handleToggleFeatured(experience.id, !experience.isFeatured)}
                      >
                        <Star className="h-4 w-4" />
                        {experience.isFeatured ? "Remove Featured" : "Make Featured"}
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      {experience.status !== "published" ? (
                        <DropdownMenuItem
                          className="flex items-center gap-2"
                          onClick={() => handleStatusChange(experience.id, "published")}
                        >
                          <FileText className="h-4 w-4" />
                          Publish
                        </DropdownMenuItem>
                      ) : (
                        <DropdownMenuItem
                          className="flex items-center gap-2"
                          onClick={() => handleStatusChange(experience.id, "draft")}
                        >
                          <FileText className="h-4 w-4" />
                          Unpublish
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        className="flex items-center gap-2 text-destructive focus:text-destructive"
                        onClick={() => handleDelete(experience.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </CardFooter>
              </Card>
            ))}
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="w-[100px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredExperiences.length > 0 ? (
                  filteredExperiences.map((experience) => (
                    <TableRow key={experience.id} className={experience.isFeatured ? "bg-admin-orange/5" : ""}>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          {experience.title}
                          {experience.isFeatured && (
                            <Star className="h-4 w-4 text-admin-orange fill-admin-orange" />
                          )}
                        </div>
                      </TableCell>
                      <TableCell>{experience.location || "—"}</TableCell>
                      <TableCell>{experience.category || "—"}</TableCell>
                      <TableCell>{formatPrice(experience.price)}</TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {experience.isVR && (
                            <Badge variant="outline" className="bg-admin-purple/10 text-admin-purple">VR</Badge>
                          )}
                          {experience.isAR && (
                            <Badge variant="outline" className="bg-admin-cyan/10 text-admin-cyan">AR</Badge>
                          )}
                          {!experience.isVR && !experience.isAR && "—"}
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(experience.status)}</TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-8 w-8">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Actions</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            {experience.isVR && (
                              <DropdownMenuItem
                                className="flex items-center gap-2"
                                onClick={() => handleLaunchVR(experience)}
                              >
                                <HeadsetIcon className="h-4 w-4" />
                                Launch VR
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuItem className="flex items-center gap-2">
                              <Eye className="h-4 w-4" />
                              Preview
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              className="flex items-center gap-2"
                              onClick={() => handleEdit(experience)}
                            >
                              <Edit className="h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              className="flex items-center gap-2"
                              onClick={() => handleToggleFeatured(experience.id, !experience.isFeatured)}
                            >
                              <Star className="h-4 w-4" />
                              {experience.isFeatured ? "Remove Featured" : "Make Featured"}
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            {experience.status !== "published" ? (
                              <DropdownMenuItem
                                className="flex items-center gap-2"
                                onClick={() => handleStatusChange(experience.id, "published")}
                              >
                                <FileText className="h-4 w-4" />
                                Publish
                              </DropdownMenuItem>
                            ) : (
                              <DropdownMenuItem
                                className="flex items-center gap-2"
                                onClick={() => handleStatusChange(experience.id, "draft")}
                              >
                                <FileText className="h-4 w-4" />
                                Unpublish
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              className="flex items-center gap-2 text-destructive focus:text-destructive"
                              onClick={() => handleDelete(experience.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="h-24 text-center">
                      No experiences found.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        )}

        <div className="flex items-center justify-between mt-4">
          <div className="text-sm text-muted-foreground">
            Showing <strong>{filteredExperiences.length}</strong> of{" "}
            <strong>{experiences.length}</strong> experiences
          </div>
        </div>
      </div>

      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{isEditing ? "Edit Experience" : "Add New Experience"}</DialogTitle>
            <DialogDescription>
              {isEditing 
                ? "Make changes to your experience details here. Click save when you're done."
                : "Fill in the details for your new experience."
              }
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                name="title"
                value={currentExperience.title}
                onChange={handleInputChange}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                value={currentExperience.description}
                onChange={handleInputChange}
                rows={2}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="location">Location</Label>
                <Input
                  id="location"
                  name="location"
                  value={currentExperience.location}
                  onChange={handleInputChange}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="category">Category</Label>
                <Input
                  id="category"
                  name="category"
                  value={currentExperience.category}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="price">Price (USD)</Label>
                <Input
                  id="price"
                  name="price"
                  type="number"
                  value={currentExperience.price}
                  onChange={handleInputChange}
                  step="0.01"
                  min="0"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="duration">Duration (minutes)</Label>
                <Input
                  id="duration"
                  name="duration"
                  type="number"
                  value={currentExperience.duration}
                  onChange={handleInputChange}
                  min="0"
                />
              </div>
            </div>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="featured-toggle">Featured Experience</Label>
                  <p className="text-xs text-muted-foreground">
                    Promote this experience on the homepage
                  </p>
                </div>
                <Switch
                  id="featured-toggle"
                  checked={currentExperience.isFeatured}
                  onCheckedChange={(checked) => handleSwitchChange("isFeatured", checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="vr-toggle">VR Experience</Label>
                  <p className="text-xs text-muted-foreground">
                    This is a virtual reality experience
                  </p>
                </div>
                <Switch
                  id="vr-toggle"
                  checked={currentExperience.isVR}
                  onCheckedChange={(checked) => handleSwitchChange("isVR", checked)}
                />
              </div>

              {/* VR Project Upload Section */}
              {currentExperience.isVR && (
                <div className="space-y-4 p-4 border rounded-lg bg-muted/50">
                  <div className="flex items-center gap-2">
                    <Upload className="h-4 w-4" />
                    <Label className="text-sm font-medium">VR Project Files</Label>
                  </div>

                  <div className="grid gap-3">
                    <div className="grid gap-2">
                      <Label htmlFor="vr-folder" className="text-xs">
                        Upload Three.js VR Project Folder
                      </Label>
                      <Input
                        id="vr-folder"
                        type="file"
                        multiple
                        webkitdirectory=""
                        directory=""
                        accept=".html,.js,.css,.json,.gltf,.glb,.obj,.mtl,.fbx,.dae,.png,.jpg,.jpeg,.gif,.svg,.mp3,.wav,.ogg,.mp4,.webm,.txt,.md"
                        onChange={(e) => {
                          const files = e.target.files;
                          setVrFiles(files);
                        }}
                        className="file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-primary-foreground hover:file:bg-primary/80"
                      />
                      <p className="text-xs text-muted-foreground">
                        Select your complete Three.js VR project folder. All files and subfolders will be uploaded.
                        <br />
                        <span className="text-blue-600">File size limit: 50MB per file</span>
                      </p>
                      <div className="flex items-center gap-2 text-xs text-blue-600">
                        <FolderOpen className="h-3 w-3" />
                        <span>Click "Choisir des fichiers" to select your project folder</span>
                      </div>
                    </div>

                    <div className="grid gap-2">
                      <Label htmlFor="vr-description" className="text-xs">
                        VR Project Description
                      </Label>
                      <Textarea
                        id="vr-description"
                        placeholder="Describe your VR experience..."
                        value={vrDescription}
                        onChange={(e) => setVrDescription(e.target.value)}
                        rows={2}
                        className="text-sm"
                      />
                    </div>

                    <div className="grid gap-2">
                      <Label htmlFor="vr-controls" className="text-xs">
                        VR Controls Instructions
                      </Label>
                      <Input
                        id="vr-controls"
                        placeholder="e.g., Use VR headset or mouse/keyboard to navigate"
                        value={vrControls}
                        onChange={(e) => setVrControls(e.target.value)}
                        className="text-sm"
                      />

                    </div>

                    {vrFiles && vrFiles.length > 0 ? (
                      <div className="grid gap-2">
                        <Label className="text-xs">
                          Selected Files ({vrFiles.length})
                          {isUploading && (
                            <span className="ml-2 text-blue-600">
                              <div className="inline-flex items-center gap-1">
                                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600"></div>
                                Uploading...
                              </div>
                            </span>
                          )}
                        </Label>
                        <div className="max-h-40 overflow-y-auto space-y-1">
                          {Array.from(vrFiles).map((file, index) => {
                            const isMainFile = file.name.toLowerCase().includes('index.html') ||
                                             file.name.toLowerCase().includes('main.html');
                            const pathParts = file.webkitRelativePath ? file.webkitRelativePath.split('/') : [file.name];
                            const fileName = pathParts[pathParts.length - 1];
                            const folderPath = pathParts.length > 1 ? pathParts.slice(0, -1).join('/') + '/' : '';

                            return (
                              <div key={index} className={`flex items-center gap-2 text-xs p-2 rounded border ${
                                isMainFile ? 'bg-blue-50 border-blue-200' : 'bg-background'
                              }`}>
                                <FolderOpen className="h-3 w-3 flex-shrink-0" />
                                <div className="flex-1 min-w-0">
                                  <div className="truncate">
                                    {folderPath && (
                                      <span className="text-muted-foreground">{folderPath}</span>
                                    )}
                                    <span className={isMainFile ? 'font-medium text-blue-700' : ''}>{fileName}</span>
                                    {isMainFile && (
                                      <span className="ml-1 text-xs bg-blue-100 text-blue-700 px-1 rounded">MAIN</span>
                                    )}
                                  </div>
                                </div>
                                <span className="text-muted-foreground flex-shrink-0">
                                  ({(file.size / 1024 / 1024).toFixed(2)} MB)
                                </span>
                              </div>
                            );
                          })}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Total size: {Array.from(vrFiles).reduce((total, file) => total + file.size, 0) / 1024 / 1024 < 1
                            ? `${(Array.from(vrFiles).reduce((total, file) => total + file.size, 0) / 1024).toFixed(0)} KB`
                            : `${(Array.from(vrFiles).reduce((total, file) => total + file.size, 0) / 1024 / 1024).toFixed(2)} MB`
                          }
                        </div>
                      </div>
                    ) : (
                      <div className="grid gap-2">
                        <div className="flex items-center gap-2 text-xs text-muted-foreground p-2 rounded border border-dashed">
                          <FolderOpen className="h-3 w-3" />
                          <span>Aucun fichier n'a été sélectionné</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="ar-toggle">AR Experience</Label>
                  <p className="text-xs text-muted-foreground">
                    This is an augmented reality experience
                  </p>
                </div>
                <Switch
                  id="ar-toggle"
                  checked={currentExperience.isAR}
                  onCheckedChange={(checked) => handleSwitchChange("isAR", checked)}
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogOpen(false)} disabled={isUploading}>
              Cancel
            </Button>
            <Button type="submit" onClick={handleSubmit} disabled={isUploading}>
              {isUploading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Uploading VR Project...
                </>
              ) : (
                isEditing ? "Save Changes" : "Add Experience"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
