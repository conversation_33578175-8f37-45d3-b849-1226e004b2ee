import express, { Request, Response } from 'express';
import {
  createExperience,
  getExperiences,
  getExperienceById,
  updateExperience,
  deleteExperience,
  updateExperienceStatus,
  toggleExperienceFeatured,
  createVRExperience,
} from '../controllers/experienceController';
import { protect } from '../middleware/authMiddleware'; // Protect experience routes
import { uploadVRProject } from '../middleware/vrUploadMiddleware';
import ExperienceModel from '../models/Experience'; // Import Experience model
import { vrSyncService } from '../services/vrSyncService'; // Import VR sync service

const router = express.Router();

router.post('/', protect, createExperience);

// VR sync route - manually sync VR projects from filesystem to database
router.post('/vr-sync', protect, async (req, res) => {
  try {
    console.log('Manual VR sync requested');
    await vrSyncService.syncVRProjects();
    res.json({ message: 'VR projects sync completed successfully' });
  } catch (error: any) {
    console.error('VR sync error:', error);
    res.status(500).json({ message: 'VR sync failed', error: error.message });
  }
});

// VR upload route with multer
router.post('/vr', protect, uploadVRProject, createVRExperience);

// Test upload endpoint (no auth for testing)
router.post('/test-upload', uploadVRProject, (req: Request, res: Response) => {
  console.log('=== TEST UPLOAD ===');
  console.log('Files received:', req.files ? (req.files as any[]).length : 0);
  console.log('Body:', req.body);
  if (req.files && (req.files as any[]).length > 0) {
    (req.files as any[]).slice(0, 3).forEach((file: any, index: number) => {
      console.log(`Test File ${index}:`, {
        originalname: file.originalname,
        filename: file.filename,
        path: file.path,
        size: file.size
      });
    });
  }
  res.json({
    message: 'Test upload successful',
    filesReceived: req.files ? (req.files as any[]).length : 0
  });
});

// Route to get VR project info and launch URL
router.get('/:id/vr-launch', protect, async (req, res) => {
  try {
    const experience = await ExperienceModel.findById(req.params.id);

    if (!experience || !experience.isVR || !experience.vrProject) {
      return res.status(404).json({ message: 'VR experience not found' });
    }

    const vrProject = experience.vrProject;
    const launchUrl = `/vr/${vrProject.folderPath}/${vrProject.mainFile || 'index.html'}`;

    res.json({
      id: experience._id,
      title: experience.title,
      description: experience.description,
      vrProject: {
        folderPath: vrProject.folderPath,
        mainFile: vrProject.mainFile,
        description: vrProject.description,
        controls: vrProject.controls,
        launchUrl: launchUrl
      }
    });
  } catch (error: any) {
    console.error('Error getting VR launch info:', error);
    res.status(500).json({ message: error.message });
  }
});
router.get('/', protect, getExperiences);
router.get('/:id', protect, getExperienceById);
router.put('/:id', protect, updateExperience);
router.delete('/:id', protect, deleteExperience);
router.patch('/:id/status', protect, updateExperienceStatus);
router.patch('/:id/featured', protect, toggleExperienceFeatured);

export default router;