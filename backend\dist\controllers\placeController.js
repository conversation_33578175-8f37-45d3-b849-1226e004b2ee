"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getPlacesByCategory = exports.deletePlace = exports.updatePlace = exports.getPlaceById = exports.getPlaces = exports.createPlace = void 0;
const Place_1 = __importDefault(require("../models/Place"));
// Create a new place
const createPlace = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const newPlace = new Place_1.default(req.body);
        const savedPlace = yield newPlace.save();
        res.status(201).json(savedPlace);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.createPlace = createPlace;
// Get all places with filtering, pagination, and sorting
const getPlaces = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page = 1, limit = 10, category, search, sortBy, sortOrder } = req.query;
        const query = {};
        if (category) {
            query.category = category;
        }
        if (search) {
            query.$or = [
                { name: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } },
            ];
        }
        const sort = {};
        if (sortBy) {
            sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
        }
        else {
            sort.createdAt = -1; // Default sort by creation date descending
        }
        const options = {
            skip: (Number(page) - 1) * Number(limit),
            limit: Number(limit),
            sort,
        };
        const places = yield Place_1.default.find(query, null, options);
        const total = yield Place_1.default.countDocuments(query);
        res.status(200).json({
            data: places,
            pagination: {
                total,
                page: Number(page),
                limit: Number(limit),
                pages: Math.ceil(total / Number(limit)),
            },
        });
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.getPlaces = getPlaces;
// Get a single place by ID
const getPlaceById = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const place = yield Place_1.default.findById(req.params.id);
        if (!place) {
            return res.status(404).json({ message: 'Place not found' });
        }
        res.status(200).json(place);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.getPlaceById = getPlaceById;
// Update a place by ID
const updatePlace = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const updatedPlace = yield Place_1.default.findByIdAndUpdate(req.params.id, req.body, { new: true });
        if (!updatedPlace) {
            return res.status(404).json({ message: 'Place not found' });
        }
        res.status(200).json(updatedPlace);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.updatePlace = updatePlace;
// Delete a place by ID
const deletePlace = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const deletedPlace = yield Place_1.default.findByIdAndDelete(req.params.id);
        if (!deletedPlace) {
            return res.status(404).json({ message: 'Place not found' });
        }
        res.status(200).json({ message: 'Place deleted' });
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.deletePlace = deletePlace;
// Get places by category
const getPlacesByCategory = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { category } = req.params;
        if (!category) {
            return res.status(400).json({ message: 'Category is required' });
        }
        const places = yield Place_1.default.find({ category });
        res.status(200).json(places);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.getPlacesByCategory = getPlacesByCategory;
