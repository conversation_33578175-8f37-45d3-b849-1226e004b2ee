{"version": 3, "file": "EffectComposer.cjs", "sources": ["../../src/postprocessing/EffectComposer.ts"], "sourcesContent": ["import { Clock, LinearFilter, RGBAFormat, NoBlending, Vector2, WebG<PERSON>enderer, WebGLRenderTarget } from 'three'\nimport { CopyShader } from '../shaders/CopyShader'\nimport { ShaderPass } from './ShaderPass'\nimport { MaskPass, ClearMaskPass } from './MaskPass'\nimport { Pass } from './Pass'\n\nclass EffectComposer<TRenderTarget extends WebGLRenderTarget = WebGLRenderTarget> {\n  public renderer: WebGLRenderer\n  private _pixelRatio: number\n  private _width: number\n  private _height: number\n  public renderTarget1: WebGLRenderTarget\n  public renderTarget2: WebGLRenderTarget\n  public writeBuffer: WebGLRenderTarget\n  public readBuffer: WebGLRenderTarget\n  public renderToScreen: boolean\n  public passes: Pass[] = []\n  public copyPass: Pass\n  public clock: Clock\n\n  constructor(renderer: WebGLRenderer, renderTarget?: TRenderTarget) {\n    this.renderer = renderer\n\n    if (renderTarget === undefined) {\n      const parameters = {\n        minFilter: LinearFilter,\n        magFilter: LinearFilter,\n        format: RGBAFormat,\n      }\n\n      const size = renderer.getSize(new Vector2())\n      this._pixelRatio = renderer.getPixelRatio()\n      this._width = size.width\n      this._height = size.height\n\n      renderTarget = new WebGLRenderTarget(\n        this._width * this._pixelRatio,\n        this._height * this._pixelRatio,\n        parameters,\n      ) as TRenderTarget\n      renderTarget.texture.name = 'EffectComposer.rt1'\n    } else {\n      this._pixelRatio = 1\n      this._width = renderTarget.width\n      this._height = renderTarget.height\n    }\n\n    this.renderTarget1 = renderTarget\n    this.renderTarget2 = renderTarget.clone()\n    this.renderTarget2.texture.name = 'EffectComposer.rt2'\n\n    this.writeBuffer = this.renderTarget1\n    this.readBuffer = this.renderTarget2\n\n    this.renderToScreen = true\n\n    // dependencies\n\n    if (CopyShader === undefined) {\n      console.error('THREE.EffectComposer relies on CopyShader')\n    }\n\n    if (ShaderPass === undefined) {\n      console.error('THREE.EffectComposer relies on ShaderPass')\n    }\n\n    this.copyPass = new ShaderPass(CopyShader)\n    // @ts-ignore\n    this.copyPass.material.blending = NoBlending\n\n    this.clock = new Clock()\n  }\n\n  public swapBuffers(): void {\n    const tmp = this.readBuffer\n    this.readBuffer = this.writeBuffer\n    this.writeBuffer = tmp\n  }\n\n  public addPass(pass: Pass): void {\n    this.passes.push(pass)\n    pass.setSize(this._width * this._pixelRatio, this._height * this._pixelRatio)\n  }\n\n  public insertPass(pass: Pass, index: number): void {\n    this.passes.splice(index, 0, pass)\n    pass.setSize(this._width * this._pixelRatio, this._height * this._pixelRatio)\n  }\n\n  public removePass(pass: Pass): void {\n    const index = this.passes.indexOf(pass)\n\n    if (index !== -1) {\n      this.passes.splice(index, 1)\n    }\n  }\n\n  public isLastEnabledPass(passIndex: number): boolean {\n    for (let i = passIndex + 1; i < this.passes.length; i++) {\n      if (this.passes[i].enabled) {\n        return false\n      }\n    }\n\n    return true\n  }\n\n  public render(deltaTime?: number): void {\n    // deltaTime value is in seconds\n\n    if (deltaTime === undefined) {\n      deltaTime = this.clock.getDelta()\n    }\n\n    const currentRenderTarget = this.renderer.getRenderTarget()\n\n    let maskActive = false\n\n    const il = this.passes.length\n\n    for (let i = 0; i < il; i++) {\n      const pass = this.passes[i]\n\n      if (pass.enabled === false) continue\n\n      pass.renderToScreen = this.renderToScreen && this.isLastEnabledPass(i)\n      pass.render(this.renderer, this.writeBuffer, this.readBuffer, deltaTime, maskActive)\n\n      if (pass.needsSwap) {\n        if (maskActive) {\n          const context = this.renderer.getContext()\n          const stencil = this.renderer.state.buffers.stencil\n\n          //context.stencilFunc( context.NOTEQUAL, 1, 0xffffffff );\n          stencil.setFunc(context.NOTEQUAL, 1, 0xffffffff)\n\n          this.copyPass.render(this.renderer, this.writeBuffer, this.readBuffer, deltaTime)\n\n          //context.stencilFunc( context.EQUAL, 1, 0xffffffff );\n          stencil.setFunc(context.EQUAL, 1, 0xffffffff)\n        }\n\n        this.swapBuffers()\n      }\n\n      if (MaskPass !== undefined) {\n        if (pass instanceof MaskPass) {\n          maskActive = true\n        } else if (pass instanceof ClearMaskPass) {\n          maskActive = false\n        }\n      }\n    }\n\n    this.renderer.setRenderTarget(currentRenderTarget)\n  }\n\n  public reset(renderTarget: WebGLRenderTarget): void {\n    if (renderTarget === undefined) {\n      const size = this.renderer.getSize(new Vector2())\n      this._pixelRatio = this.renderer.getPixelRatio()\n      this._width = size.width\n      this._height = size.height\n\n      renderTarget = this.renderTarget1.clone()\n      renderTarget.setSize(this._width * this._pixelRatio, this._height * this._pixelRatio)\n    }\n\n    this.renderTarget1.dispose()\n    this.renderTarget2.dispose()\n    this.renderTarget1 = renderTarget\n    this.renderTarget2 = renderTarget.clone()\n\n    this.writeBuffer = this.renderTarget1\n    this.readBuffer = this.renderTarget2\n  }\n\n  public setSize(width: number, height: number): void {\n    this._width = width\n    this._height = height\n\n    const effectiveWidth = this._width * this._pixelRatio\n    const effectiveHeight = this._height * this._pixelRatio\n\n    this.renderTarget1.setSize(effectiveWidth, effectiveHeight)\n    this.renderTarget2.setSize(effectiveWidth, effectiveHeight)\n\n    for (let i = 0; i < this.passes.length; i++) {\n      this.passes[i].setSize(effectiveWidth, effectiveHeight)\n    }\n  }\n\n  public setPixelRatio(pixelRatio: number): void {\n    this._pixelRatio = pixelRatio\n\n    this.setSize(this._width, this._height)\n  }\n\n  public dispose() {\n    this.renderTarget1.dispose()\n    this.renderTarget2.dispose()\n\n    this.copyPass.dispose()\n  }\n}\n\nexport { EffectComposer }\n"], "names": ["LinearFilter", "RGBAFormat", "Vector2", "WebGLRenderTarget", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "NoBlending", "Clock", "<PERSON><PERSON><PERSON>", "ClearMaskPass"], "mappings": ";;;;;;;;;;;;AAMA,MAAM,eAA4E;AAAA,EAchF,YAAY,UAAyB,cAA8B;AAb5D;AACC;AACA;AACA;AACD;AACA;AACA;AACA;AACA;AACA,kCAAiB,CAAA;AACjB;AACA;AAGL,SAAK,WAAW;AAEhB,QAAI,iBAAiB,QAAW;AAC9B,YAAM,aAAa;AAAA,QACjB,WAAWA,MAAA;AAAA,QACX,WAAWA,MAAA;AAAA,QACX,QAAQC,MAAA;AAAA,MAAA;AAGV,YAAM,OAAO,SAAS,QAAQ,IAAIC,MAAAA,QAAS,CAAA;AACtC,WAAA,cAAc,SAAS;AAC5B,WAAK,SAAS,KAAK;AACnB,WAAK,UAAU,KAAK;AAEpB,qBAAe,IAAIC,MAAA;AAAA,QACjB,KAAK,SAAS,KAAK;AAAA,QACnB,KAAK,UAAU,KAAK;AAAA,QACpB;AAAA,MAAA;AAEF,mBAAa,QAAQ,OAAO;AAAA,IAAA,OACvB;AACL,WAAK,cAAc;AACnB,WAAK,SAAS,aAAa;AAC3B,WAAK,UAAU,aAAa;AAAA,IAC9B;AAEA,SAAK,gBAAgB;AAChB,SAAA,gBAAgB,aAAa;AAC7B,SAAA,cAAc,QAAQ,OAAO;AAElC,SAAK,cAAc,KAAK;AACxB,SAAK,aAAa,KAAK;AAEvB,SAAK,iBAAiB;AAItB,QAAIC,WAAAA,eAAe,QAAW;AAC5B,cAAQ,MAAM,2CAA2C;AAAA,IAC3D;AAEA,QAAIC,WAAAA,eAAe,QAAW;AAC5B,cAAQ,MAAM,2CAA2C;AAAA,IAC3D;AAEK,SAAA,WAAW,IAAIA,WAAA,WAAWD,WAAU,UAAA;AAEpC,SAAA,SAAS,SAAS,WAAWE,MAAAA;AAE7B,SAAA,QAAQ,IAAIC,MAAAA;EACnB;AAAA,EAEO,cAAoB;AACzB,UAAM,MAAM,KAAK;AACjB,SAAK,aAAa,KAAK;AACvB,SAAK,cAAc;AAAA,EACrB;AAAA,EAEO,QAAQ,MAAkB;AAC1B,SAAA,OAAO,KAAK,IAAI;AAChB,SAAA,QAAQ,KAAK,SAAS,KAAK,aAAa,KAAK,UAAU,KAAK,WAAW;AAAA,EAC9E;AAAA,EAEO,WAAW,MAAY,OAAqB;AACjD,SAAK,OAAO,OAAO,OAAO,GAAG,IAAI;AAC5B,SAAA,QAAQ,KAAK,SAAS,KAAK,aAAa,KAAK,UAAU,KAAK,WAAW;AAAA,EAC9E;AAAA,EAEO,WAAW,MAAkB;AAClC,UAAM,QAAQ,KAAK,OAAO,QAAQ,IAAI;AAEtC,QAAI,UAAU,IAAI;AACX,WAAA,OAAO,OAAO,OAAO,CAAC;AAAA,IAC7B;AAAA,EACF;AAAA,EAEO,kBAAkB,WAA4B;AACnD,aAAS,IAAI,YAAY,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK;AACvD,UAAI,KAAK,OAAO,CAAC,EAAE,SAAS;AACnB,eAAA;AAAA,MACT;AAAA,IACF;AAEO,WAAA;AAAA,EACT;AAAA,EAEO,OAAO,WAA0B;AAGtC,QAAI,cAAc,QAAW;AACf,kBAAA,KAAK,MAAM;IACzB;AAEM,UAAA,sBAAsB,KAAK,SAAS,gBAAgB;AAE1D,QAAI,aAAa;AAEX,UAAA,KAAK,KAAK,OAAO;AAEvB,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AACrB,YAAA,OAAO,KAAK,OAAO,CAAC;AAE1B,UAAI,KAAK,YAAY;AAAO;AAE5B,WAAK,iBAAiB,KAAK,kBAAkB,KAAK,kBAAkB,CAAC;AAChE,WAAA,OAAO,KAAK,UAAU,KAAK,aAAa,KAAK,YAAY,WAAW,UAAU;AAEnF,UAAI,KAAK,WAAW;AAClB,YAAI,YAAY;AACR,gBAAA,UAAU,KAAK,SAAS,WAAW;AACzC,gBAAM,UAAU,KAAK,SAAS,MAAM,QAAQ;AAG5C,kBAAQ,QAAQ,QAAQ,UAAU,GAAG,UAAU;AAE1C,eAAA,SAAS,OAAO,KAAK,UAAU,KAAK,aAAa,KAAK,YAAY,SAAS;AAGhF,kBAAQ,QAAQ,QAAQ,OAAO,GAAG,UAAU;AAAA,QAC9C;AAEA,aAAK,YAAY;AAAA,MACnB;AAEA,UAAIC,SAAAA,aAAa,QAAW;AAC1B,YAAI,gBAAgBA,SAAAA,UAAU;AACf,uBAAA;AAAA,QAAA,WACJ,gBAAgBC,wBAAe;AAC3B,uBAAA;AAAA,QACf;AAAA,MACF;AAAA,IACF;AAEK,SAAA,SAAS,gBAAgB,mBAAmB;AAAA,EACnD;AAAA,EAEO,MAAM,cAAuC;AAClD,QAAI,iBAAiB,QAAW;AAC9B,YAAM,OAAO,KAAK,SAAS,QAAQ,IAAIP,eAAS;AAC3C,WAAA,cAAc,KAAK,SAAS,cAAc;AAC/C,WAAK,SAAS,KAAK;AACnB,WAAK,UAAU,KAAK;AAEL,qBAAA,KAAK,cAAc;AACrB,mBAAA,QAAQ,KAAK,SAAS,KAAK,aAAa,KAAK,UAAU,KAAK,WAAW;AAAA,IACtF;AAEA,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,SAAK,gBAAgB;AAChB,SAAA,gBAAgB,aAAa;AAElC,SAAK,cAAc,KAAK;AACxB,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA,EAEO,QAAQ,OAAe,QAAsB;AAClD,SAAK,SAAS;AACd,SAAK,UAAU;AAET,UAAA,iBAAiB,KAAK,SAAS,KAAK;AACpC,UAAA,kBAAkB,KAAK,UAAU,KAAK;AAEvC,SAAA,cAAc,QAAQ,gBAAgB,eAAe;AACrD,SAAA,cAAc,QAAQ,gBAAgB,eAAe;AAE1D,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC3C,WAAK,OAAO,CAAC,EAAE,QAAQ,gBAAgB,eAAe;AAAA,IACxD;AAAA,EACF;AAAA,EAEO,cAAc,YAA0B;AAC7C,SAAK,cAAc;AAEnB,SAAK,QAAQ,KAAK,QAAQ,KAAK,OAAO;AAAA,EACxC;AAAA,EAEO,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,cAAc;AAEnB,SAAK,SAAS;EAChB;AACF;;"}