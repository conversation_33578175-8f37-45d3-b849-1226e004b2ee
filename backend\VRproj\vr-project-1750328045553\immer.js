import { produce } from 'immer';

const immerImpl = (initializer) => (set, get, store) => {
  store.setState = (updater, replace, ...a) => {
    const nextState = typeof updater === "function" ? produce(updater) : updater;
    return set(nextState, replace, ...a);
  };
  return initializer(store.setState, get, store);
};
const immer = immerImpl;

export { immer };
_key < _len; _key++) {
        a[_key - 2] = arguments[_key];
      }
      return set.apply(void 0, [nextState, replace].concat(a));
    };
    return initializer(store.setState, get, store);
  };
};
var immer = immerImpl;

exports.immer = immer;
