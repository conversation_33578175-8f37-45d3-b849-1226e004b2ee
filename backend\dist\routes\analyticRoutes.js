"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const analyticController_1 = require("../controllers/analyticController");
const authMiddleware_1 = require("../middleware/authMiddleware"); // Protect analytics routes
const router = express_1.default.Router();
router.post('/', authMiddleware_1.protect, analyticController_1.recordAnalyticEvent); // Route to record an event
router.get('/aggregated', authMiddleware_1.protect, analyticController_1.getAggregatedAnalytics); // Route to get aggregated data
router.get('/events', authMiddleware_1.protect, analyticController_1.getAnalyticEvents); // Route to get analytics events
router.get('/overview', authMiddleware_1.protect, analyticController_1.getOverviewMetrics); // Route to get overview metrics
router.get('/audience', authMiddleware_1.protect, analyticController_1.getAudienceMetrics); // Route to get audience metrics
router.get('/content', authMiddleware_1.protect, analyticController_1.getContentMetrics); // Route to get content metrics
router.get('/content-statistics', authMiddleware_1.protect, analyticController_1.getContentStatistics); // Route to get content statistics by status
router.get('/metrics/:metricId', authMiddleware_1.protect, analyticController_1.getMetricsById); // Route to get specific metrics by ID
// You can add more routes here for different analytics queries
exports.default = router;
