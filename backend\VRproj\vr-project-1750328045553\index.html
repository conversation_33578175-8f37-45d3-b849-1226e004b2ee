<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VR Experience - A-Frame Demo</title>
    <script src="https://aframe.io/releases/1.4.0/aframe.min.js"></script>
    <script src="https://cdn.jsdelivr.net/gh/donmccurdy/aframe-extras@v6.1.1/dist/aframe-extras.min.js"></script>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
        }
        .info {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 8px;
            z-index: 1000;
            max-width: 300px;
        }
        .controls {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: white;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="info">
        <h3>🥽 VR Experience Demo</h3>
        <p>This is a browser-compatible VR experience built with A-Frame!</p>
        <p><strong>Controls:</strong></p>
        <ul>
            <li>Mouse: Look around</li>
            <li>WASD: Move</li>
            <li>Click objects to interact</li>
            <li>VR Button: Enter VR mode</li>
        </ul>
    </div>

    <a-scene
        background="color: #87CEEB"
        vr-mode-ui="enabled: true"
        embedded
        style="height: 100vh; width: 100vw;">

        <!-- Assets -->
        <a-assets>
            <a-mixin id="interactive"
                     geometry="primitive: box"
                     material="color: #4CC3D9"
                     animation__mouseenter="property: scale; to: 1.2 1.2 1.2; startEvents: mouseenter; dur: 200"
                     animation__mouseleave="property: scale; to: 1 1 1; startEvents: mouseleave; dur: 200">
            </a-mixin>
        </a-assets>

        <!-- Lighting -->
        <a-light type="ambient" color="#404040" intensity="0.4"></a-light>
        <a-light type="directional" position="2 4 5" color="#ffffff" intensity="0.8"></a-light>

        <!-- Interactive Objects -->
        <a-box
            mixin="interactive"
            position="-2 1 -3"
            rotation="0 45 0"
            color="#4CC3D9"
            class="clickable"
            animation__click="property: rotation; to: 0 405 0; startEvents: click; dur: 1000">
        </a-box>

        <a-sphere
            position="0 2 -5"
            radius="1"
            color="#EF2D5E"
            class="clickable"
            animation__click="property: position; to: 0 4 -5; startEvents: click; dur: 1000; dir: alternate; loop: true">
        </a-sphere>

        <a-cylinder
            position="2 1 -3"
            radius="0.5"
            height="2"
            color="#FFC65D"
            class="clickable"
            animation__click="property: scale; to: 1.5 1.5 1.5; startEvents: click; dur: 1000; dir: alternate; loop: true">
        </a-cylinder>

        <a-torus
            position="0 1 -8"
            color="#7BC8A4"
            radius="2"
            radius-tubular="0.1"
            animation="property: rotation; to: 0 360 0; loop: true; dur: 10000">
        </a-torus>

        <!-- Ground -->
        <a-plane
            position="0 0 -4"
            rotation="-90 0 0"
            width="20"
            height="20"
            color="#7BC8A4"
            shadow="receive: true">
        </a-plane>

        <!-- Sky -->
        <a-sky color="#87CEEB"></a-sky>

        <!-- Camera with VR controls -->
        <a-entity id="rig" position="0 1.6 3">
            <a-camera
                look-controls="pointerLockEnabled: true"
                wasd-controls="acceleration: 20"
                cursor="rayOrigin: mouse">
            </a-camera>

            <!-- VR Controllers -->
            <a-entity
                id="leftHand"
                laser-controls="hand: left"
                raycaster="objects: .clickable">
            </a-entity>

            <a-entity
                id="rightHand"
                laser-controls="hand: right"
                raycaster="objects: .clickable">
            </a-entity>
        </a-entity>

        <!-- Text -->
        <a-text
            value="Welcome to VR!"
            position="0 3 -6"
            align="center"
            color="#000000"
            geometry="primitive: plane; width: 6; height: 1"
            material="color: white; opacity: 0.8">
        </a-text>
    </a-scene>

    <script>
        console.log('VR Experience loaded successfully!');

        // Add click handlers for desktop interaction
        document.addEventListener('DOMContentLoaded', function() {
            const clickableElements = document.querySelectorAll('.clickable');

            clickableElements.forEach(element => {
                element.addEventListener('click', function() {
                    console.log('Clicked:', this.tagName);
                    // Trigger the click animation
                    this.emit('click');
                });

                element.addEventListener('mouseenter', function() {
                    this.emit('mouseenter');
                });

                element.addEventListener('mouseleave', function() {
                    this.emit('mouseleave');
                });
            });

            // VR mode detection
            document.querySelector('a-scene').addEventListener('enter-vr', function() {
                console.log('Entered VR mode');
                document.querySelector('.info').style.display = 'none';
            });

            document.querySelector('a-scene').addEventListener('exit-vr', function() {
                console.log('Exited VR mode');
                document.querySelector('.info').style.display = 'block';
            });
        });
    </script>

    <!-- Include external JavaScript -->
    <script src="script.js"></script>
</body>
</html>
