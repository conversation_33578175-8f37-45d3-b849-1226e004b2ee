{"version": 3, "file": "ColorCorrectionShader.js", "sources": ["../../src/shaders/ColorCorrectionShader.ts"], "sourcesContent": ["import { Vector3 } from 'three'\n\n/**\n * Color correction\n */\n\nexport const ColorCorrectionShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    powRGB: { value: /* @__PURE__ */ new Vector3(2, 2, 2) },\n    mulRGB: { value: /* @__PURE__ */ new Vector3(1, 1, 1) },\n    addRGB: { value: /* @__PURE__ */ new Vector3(0, 0, 0) },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform sampler2D tDiffuse;\n    uniform vec3 powRGB;\n    uniform vec3 mulRGB;\n    uniform vec3 addRGB;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tgl_FragColor = texture2D( tDiffuse, vUv );\n    \tgl_FragColor.rgb = mulRGB * pow( ( gl_FragColor.rgb + addRGB ), powRGB );\n\n    }\n  `,\n}\n"], "names": [], "mappings": ";AAMO,MAAM,wBAAwB;AAAA,EACnC,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA,IACxB,QAAQ,EAAE,OAAuB,oBAAI,QAAQ,GAAG,GAAG,CAAC,EAAE;AAAA,IACtD,QAAQ,EAAE,OAAuB,oBAAI,QAAQ,GAAG,GAAG,CAAC,EAAE;AAAA,IACtD,QAAQ,EAAE,OAAuB,oBAAI,QAAQ,GAAG,GAAG,CAAC,EAAE;AAAA,EACxD;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAe7B;"}