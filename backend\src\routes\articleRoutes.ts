import express from 'express';
import upload from '../middleware/uploadMiddleware'; // Import upload middleware
import {
  createArticle,
  getArticles,
  getArticleById,
  updateArticle,
  deleteArticle,
  getArticleBySlug,
  updateArticleStatus, // Import updateArticleStatus
} from '../controllers/articleController';

const router = express.Router();

router.post('/', upload.array('images'), createArticle); // Add upload middleware - removed file count limit
router.get('/', getArticles);
router.get('/:id', getArticleById);
router.get('/slug/:slug', getArticleBySlug);
router.put('/:id', updateArticle);
router.delete('/:id', deleteArticle);
router.patch('/:id/status', updateArticleStatus); // Add route for updateArticleStatus

export default router;