
/**
 * API Configuration
 * 
 * Central location for API-related configuration and constants.
 */

// Base API URL - from environment variable or default
export const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// API endpoints
export const ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    PROFILE: '/auth/me',
    REFRESH_TOKEN: '/auth/refresh-token',
  },
  CONTENT: {
    ARTICLES: '/articles',
    PHOTOS: '/photos',
    VIDEOS: '/videos',
    EXPERIENCES: '/experiences',
  },
  ANALYTICS: {
    OVERVIEW: '/analytics/overview',
    AUDIENCE: '/analytics/audience',
    CONTENT: '/analytics/content',
    EVENTS: '/analytics/events',
    METRICS: '/analytics/metrics',
    SESSIONS: '/analytics/sessions',
    PAGEVIEWS: '/analytics/pageviews',
  },
  NOTIFICATIONS: {
    LIST: '/notifications',
    MARK_READ: '/notifications/mark-read',
    MARK_ALL_READ: '/notifications/mark-all-read',
    COUNT: '/notifications/count',
  },
  PLACES: '/places',
  SETTINGS: '/settings',
  USERS: '/users',
};

// Request timeout in milliseconds
export const REQUEST_TIMEOUT = 30000;

// Maximum file upload size in bytes (100MB) - increased from 10MB
export const MAX_UPLOAD_SIZE = 100 * 1024 * 1024;

// Content type headers
export const HEADERS = {
  JSON: { 'Content-Type': 'application/json' },
  MULTIPART: { 'Content-Type': 'multipart/form-data' },
};
