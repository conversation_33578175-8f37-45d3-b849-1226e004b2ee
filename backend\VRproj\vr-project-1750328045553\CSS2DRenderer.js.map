{"version": 3, "file": "CSS2DRenderer.js", "sources": ["../../src/renderers/CSS2DRenderer.js"], "sourcesContent": ["import { Matrix4, Object3D, Vector2, Vector3 } from 'three'\n\nclass CSS2DObject extends Object3D {\n  constructor(element = document.createElement('div')) {\n    super()\n\n    this.isCSS2DObject = true\n\n    this.element = element\n\n    this.element.style.position = 'absolute'\n    this.element.style.userSelect = 'none'\n\n    this.element.setAttribute('draggable', false)\n\n    this.center = new Vector2(0.5, 0.5) // ( 0, 0 ) is the lower left; ( 1, 1 ) is the top right\n\n    this.addEventListener('removed', function () {\n      this.traverse(function (object) {\n        if (object.element instanceof Element && object.element.parentNode !== null) {\n          object.element.parentNode.removeChild(object.element)\n        }\n      })\n    })\n  }\n\n  copy(source, recursive) {\n    super.copy(source, recursive)\n\n    this.element = source.element.cloneNode(true)\n\n    this.center = source.center\n\n    return this\n  }\n}\n\nconst _vector = /* @__PURE__ */ new Vector3()\nconst _viewMatrix = /* @__PURE__ */ new Matrix4()\nconst _viewProjectionMatrix = /* @__PURE__ */ new Matrix4()\nconst _a = /* @__PURE__ */ new Vector3()\nconst _b = /* @__PURE__ */ new Vector3()\n\nclass CSS2DRenderer {\n  constructor(parameters = {}) {\n    const _this = this\n\n    let _width, _height\n    let _widthHalf, _heightHalf\n\n    const cache = {\n      objects: new WeakMap(),\n    }\n\n    const domElement = parameters.element !== undefined ? parameters.element : document.createElement('div')\n\n    domElement.style.overflow = 'hidden'\n\n    this.domElement = domElement\n\n    this.getSize = function () {\n      return {\n        width: _width,\n        height: _height,\n      }\n    }\n\n    this.render = function (scene, camera) {\n      if (scene.matrixWorldAutoUpdate === true) scene.updateMatrixWorld()\n      if (camera.parent === null && camera.matrixWorldAutoUpdate === true) camera.updateMatrixWorld()\n\n      _viewMatrix.copy(camera.matrixWorldInverse)\n      _viewProjectionMatrix.multiplyMatrices(camera.projectionMatrix, _viewMatrix)\n\n      renderObject(scene, scene, camera)\n      zOrder(scene)\n    }\n\n    this.setSize = function (width, height) {\n      _width = width\n      _height = height\n\n      _widthHalf = _width / 2\n      _heightHalf = _height / 2\n\n      domElement.style.width = width + 'px'\n      domElement.style.height = height + 'px'\n    }\n\n    function renderObject(object, scene, camera) {\n      if (object.isCSS2DObject) {\n        _vector.setFromMatrixPosition(object.matrixWorld)\n        _vector.applyMatrix4(_viewProjectionMatrix)\n\n        const visible =\n          object.visible === true && _vector.z >= -1 && _vector.z <= 1 && object.layers.test(camera.layers) === true\n        object.element.style.display = visible === true ? '' : 'none'\n\n        if (visible === true) {\n          object.onBeforeRender(_this, scene, camera)\n\n          const element = object.element\n\n          element.style.transform =\n            'translate(' +\n            -100 * object.center.x +\n            '%,' +\n            -100 * object.center.y +\n            '%)' +\n            'translate(' +\n            (_vector.x * _widthHalf + _widthHalf) +\n            'px,' +\n            (-_vector.y * _heightHalf + _heightHalf) +\n            'px)'\n\n          if (element.parentNode !== domElement) {\n            domElement.appendChild(element)\n          }\n\n          object.onAfterRender(_this, scene, camera)\n        }\n\n        const objectData = {\n          distanceToCameraSquared: getDistanceToSquared(camera, object),\n        }\n\n        cache.objects.set(object, objectData)\n      }\n\n      for (let i = 0, l = object.children.length; i < l; i++) {\n        renderObject(object.children[i], scene, camera)\n      }\n    }\n\n    function getDistanceToSquared(object1, object2) {\n      _a.setFromMatrixPosition(object1.matrixWorld)\n      _b.setFromMatrixPosition(object2.matrixWorld)\n\n      return _a.distanceToSquared(_b)\n    }\n\n    function filterAndFlatten(scene) {\n      const result = []\n\n      scene.traverse(function (object) {\n        if (object.isCSS2DObject) result.push(object)\n      })\n\n      return result\n    }\n\n    function zOrder(scene) {\n      const sorted = filterAndFlatten(scene).sort(function (a, b) {\n        if (a.renderOrder !== b.renderOrder) {\n          return b.renderOrder - a.renderOrder\n        }\n\n        const distanceA = cache.objects.get(a).distanceToCameraSquared\n        const distanceB = cache.objects.get(b).distanceToCameraSquared\n\n        return distanceA - distanceB\n      })\n\n      const zMax = sorted.length\n\n      for (let i = 0, l = sorted.length; i < l; i++) {\n        sorted[i].element.style.zIndex = zMax - i\n      }\n    }\n  }\n}\n\nexport { CSS2DObject, CSS2DRenderer }\n"], "names": [], "mappings": ";AAEA,MAAM,oBAAoB,SAAS;AAAA,EACjC,YAAY,UAAU,SAAS,cAAc,KAAK,GAAG;AACnD,UAAO;AAEP,SAAK,gBAAgB;AAErB,SAAK,UAAU;AAEf,SAAK,QAAQ,MAAM,WAAW;AAC9B,SAAK,QAAQ,MAAM,aAAa;AAEhC,SAAK,QAAQ,aAAa,aAAa,KAAK;AAE5C,SAAK,SAAS,IAAI,QAAQ,KAAK,GAAG;AAElC,SAAK,iBAAiB,WAAW,WAAY;AAC3C,WAAK,SAAS,SAAU,QAAQ;AAC9B,YAAI,OAAO,mBAAmB,WAAW,OAAO,QAAQ,eAAe,MAAM;AAC3E,iBAAO,QAAQ,WAAW,YAAY,OAAO,OAAO;AAAA,QACrD;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA,EAED,KAAK,QAAQ,WAAW;AACtB,UAAM,KAAK,QAAQ,SAAS;AAE5B,SAAK,UAAU,OAAO,QAAQ,UAAU,IAAI;AAE5C,SAAK,SAAS,OAAO;AAErB,WAAO;AAAA,EACR;AACH;AAEA,MAAM,UAA0B,oBAAI,QAAS;AAC7C,MAAM,cAA8B,oBAAI,QAAS;AACjD,MAAM,wBAAwC,oBAAI,QAAS;AAC3D,MAAM,KAAqB,oBAAI,QAAS;AACxC,MAAM,KAAqB,oBAAI,QAAS;AAExC,MAAM,cAAc;AAAA,EAClB,YAAY,aAAa,IAAI;AAC3B,UAAM,QAAQ;AAEd,QAAI,QAAQ;AACZ,QAAI,YAAY;AAEhB,UAAM,QAAQ;AAAA,MACZ,SAAS,oBAAI,QAAS;AAAA,IACvB;AAED,UAAM,aAAa,WAAW,YAAY,SAAY,WAAW,UAAU,SAAS,cAAc,KAAK;AAEvG,eAAW,MAAM,WAAW;AAE5B,SAAK,aAAa;AAElB,SAAK,UAAU,WAAY;AACzB,aAAO;AAAA,QACL,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAAA,IACF;AAED,SAAK,SAAS,SAAU,OAAO,QAAQ;AACrC,UAAI,MAAM,0BAA0B;AAAM,cAAM,kBAAmB;AACnE,UAAI,OAAO,WAAW,QAAQ,OAAO,0BAA0B;AAAM,eAAO,kBAAmB;AAE/F,kBAAY,KAAK,OAAO,kBAAkB;AAC1C,4BAAsB,iBAAiB,OAAO,kBAAkB,WAAW;AAE3E,mBAAa,OAAO,OAAO,MAAM;AACjC,aAAO,KAAK;AAAA,IACb;AAED,SAAK,UAAU,SAAU,OAAO,QAAQ;AACtC,eAAS;AACT,gBAAU;AAEV,mBAAa,SAAS;AACtB,oBAAc,UAAU;AAExB,iBAAW,MAAM,QAAQ,QAAQ;AACjC,iBAAW,MAAM,SAAS,SAAS;AAAA,IACpC;AAED,aAAS,aAAa,QAAQ,OAAO,QAAQ;AAC3C,UAAI,OAAO,eAAe;AACxB,gBAAQ,sBAAsB,OAAO,WAAW;AAChD,gBAAQ,aAAa,qBAAqB;AAE1C,cAAM,UACJ,OAAO,YAAY,QAAQ,QAAQ,KAAK,MAAM,QAAQ,KAAK,KAAK,OAAO,OAAO,KAAK,OAAO,MAAM,MAAM;AACxG,eAAO,QAAQ,MAAM,UAAU,YAAY,OAAO,KAAK;AAEvD,YAAI,YAAY,MAAM;AACpB,iBAAO,eAAe,OAAO,OAAO,MAAM;AAE1C,gBAAM,UAAU,OAAO;AAEvB,kBAAQ,MAAM,YACZ,eACA,OAAO,OAAO,OAAO,IACrB,OACA,OAAO,OAAO,OAAO,IACrB,kBAEC,QAAQ,IAAI,aAAa,cAC1B,SACC,CAAC,QAAQ,IAAI,cAAc,eAC5B;AAEF,cAAI,QAAQ,eAAe,YAAY;AACrC,uBAAW,YAAY,OAAO;AAAA,UAC/B;AAED,iBAAO,cAAc,OAAO,OAAO,MAAM;AAAA,QAC1C;AAED,cAAM,aAAa;AAAA,UACjB,yBAAyB,qBAAqB,QAAQ,MAAM;AAAA,QAC7D;AAED,cAAM,QAAQ,IAAI,QAAQ,UAAU;AAAA,MACrC;AAED,eAAS,IAAI,GAAG,IAAI,OAAO,SAAS,QAAQ,IAAI,GAAG,KAAK;AACtD,qBAAa,OAAO,SAAS,CAAC,GAAG,OAAO,MAAM;AAAA,MAC/C;AAAA,IACF;AAED,aAAS,qBAAqB,SAAS,SAAS;AAC9C,SAAG,sBAAsB,QAAQ,WAAW;AAC5C,SAAG,sBAAsB,QAAQ,WAAW;AAE5C,aAAO,GAAG,kBAAkB,EAAE;AAAA,IAC/B;AAED,aAAS,iBAAiB,OAAO;AAC/B,YAAM,SAAS,CAAE;AAEjB,YAAM,SAAS,SAAU,QAAQ;AAC/B,YAAI,OAAO;AAAe,iBAAO,KAAK,MAAM;AAAA,MACpD,CAAO;AAED,aAAO;AAAA,IACR;AAED,aAAS,OAAO,OAAO;AACrB,YAAM,SAAS,iBAAiB,KAAK,EAAE,KAAK,SAAU,GAAG,GAAG;AAC1D,YAAI,EAAE,gBAAgB,EAAE,aAAa;AACnC,iBAAO,EAAE,cAAc,EAAE;AAAA,QAC1B;AAED,cAAM,YAAY,MAAM,QAAQ,IAAI,CAAC,EAAE;AACvC,cAAM,YAAY,MAAM,QAAQ,IAAI,CAAC,EAAE;AAEvC,eAAO,YAAY;AAAA,MAC3B,CAAO;AAED,YAAM,OAAO,OAAO;AAEpB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,eAAO,CAAC,EAAE,QAAQ,MAAM,SAAS,OAAO;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AACH;"}