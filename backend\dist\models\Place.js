"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const PlaceSchema = new mongoose_1.Schema({
    name: { type: String, required: true },
    description: { type: String },
    location: {
        type: {
            type: String,
            enum: ['Point'],
            required: true
        },
        coordinates: {
            type: [Number],
            required: true
        }
    },
    category: { type: String },
    imageUrl: { type: String }, // Added imageUrl field to schema
    isEvent: { type: Boolean, default: false }, // Added isEvent field to schema
    eventDate: { type: String }, // Added eventDate field to schema
    eventTime: { type: String }, // Added eventTime field to schema
    isFeatured: { type: Boolean, default: false }, // Added isFeatured field to schema
    tags: { type: [String], default: [] }, // Added tags field to schema
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
});
// Add a 2dsphere index for geospatial queries
PlaceSchema.index({ location: '2dsphere' });
// Update the updatedAt field on save
PlaceSchema.pre('save', function (next) {
    this.updatedAt = new Date();
    next();
});
const Place = mongoose_1.default.model('Place', PlaceSchema);
exports.default = Place;
