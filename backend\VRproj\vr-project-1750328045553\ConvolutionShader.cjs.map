{"version": 3, "file": "ConvolutionShader.cjs", "sources": ["../../src/shaders/ConvolutionShader.ts"], "sourcesContent": ["import { Vector2 } from 'three'\n\n/**\n * Convolution shader\n * ported from o3d sample to WebGL / GLSL\n * http://o3d.googlecode.com/svn/trunk/samples/convolution.html\n */\n\nimport type { IUniform, Texture } from 'three'\nimport type { IShader } from './types'\n\nexport type ConvolutionShaderDefines = {\n  KERNEL_SIZE_FLOAT: string\n  KERNEL_SIZE_INT: string\n}\n\nexport type ConvolutionShaderUniforms = {\n  cKernel: IUniform<number[]>\n  tDiffuse: IUniform<Texture | null>\n  uImageIncrement: IUniform<Vector2>\n}\n\nexport interface IConvolutionShader extends IShader<ConvolutionShaderUniforms, ConvolutionShaderDefines> {\n  buildKernel: (sigma: number) => number[]\n}\n\nexport const ConvolutionShader: IConvolutionShader = {\n  defines: {\n    KERNEL_SIZE_FLOAT: '25.0',\n    KERNEL_SIZE_INT: '25',\n  },\n\n  uniforms: {\n    tDiffuse: { value: null },\n    uImageIncrement: { value: /* @__PURE__ */ new Vector2(0.001953125, 0.0) },\n    cKernel: { value: [] },\n  },\n\n  vertexShader: /* glsl */ `\n    uniform vec2 uImageIncrement;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv - ( ( KERNEL_SIZE_FLOAT - 1.0 ) / 2.0 ) * uImageIncrement;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform float cKernel[ KERNEL_SIZE_INT ];\n\n    uniform sampler2D tDiffuse;\n    uniform vec2 uImageIncrement;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec2 imageCoord = vUv;\n    \tvec4 sum = vec4( 0.0, 0.0, 0.0, 0.0 );\n\n    \tfor( int i = 0; i < KERNEL_SIZE_INT; i ++ ) {\n\n    \t\tsum += texture2D( tDiffuse, imageCoord ) * cKernel[ i ];\n    \t\timageCoord += uImageIncrement;\n\n    \t}\n\n    \tgl_FragColor = sum;\n\n    }\n  `,\n\n  buildKernel: function (sigma) {\n    // We lop off the sqrt(2 * pi) * sigma term, since we're going to normalize anyway.\n\n    function gauss(x: number, sigma: number): number {\n      return Math.exp(-(x * x) / (2.0 * sigma * sigma))\n    }\n\n    const kMaxKernelSize = 25\n\n    const kernelSize = Math.min(2 * Math.ceil(sigma * 3.0) + 1, kMaxKernelSize)\n\n    const halfWidth = (kernelSize - 1) * 0.5\n\n    const values: number[] = new Array(kernelSize)\n\n    let sum = 0.0\n\n    for (let i = 0; i < kernelSize; ++i) {\n      values[i] = gauss(i - halfWidth, sigma)\n      sum += values[i]\n    }\n\n    // normalize the kernel\n\n    for (let i = 0; i < kernelSize; ++i) values[i] /= sum\n\n    return values\n  },\n}\n"], "names": ["Vector2", "sigma"], "mappings": ";;;AA0BO,MAAM,oBAAwC;AAAA,EACnD,SAAS;AAAA,IACP,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,EACnB;AAAA,EAEA,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA,IACxB,iBAAiB,EAAE,2BAA2BA,MAAQ,QAAA,YAAa,CAAG,EAAE;AAAA,IACxE,SAAS,EAAE,OAAO,GAAG;AAAA,EACvB;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAazB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAyB3B,aAAa,SAAU,OAAO;AAGnB,aAAA,MAAM,GAAWC,QAAuB;AAC/C,aAAO,KAAK,IAAI,EAAE,IAAI,MAAM,IAAMA,SAAQA,OAAM;AAAA,IAClD;AAEA,UAAM,iBAAiB;AAEjB,UAAA,aAAa,KAAK,IAAI,IAAI,KAAK,KAAK,QAAQ,CAAG,IAAI,GAAG,cAAc;AAEpE,UAAA,aAAa,aAAa,KAAK;AAE/B,UAAA,SAAmB,IAAI,MAAM,UAAU;AAE7C,QAAI,MAAM;AAEV,aAAS,IAAI,GAAG,IAAI,YAAY,EAAE,GAAG;AACnC,aAAO,CAAC,IAAI,MAAM,IAAI,WAAW,KAAK;AACtC,aAAO,OAAO,CAAC;AAAA,IACjB;AAIA,aAAS,IAAI,GAAG,IAAI,YAAY,EAAE;AAAG,aAAO,CAAC,KAAK;AAE3C,WAAA;AAAA,EACT;AACF;;"}