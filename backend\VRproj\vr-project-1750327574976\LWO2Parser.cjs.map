{"version": 3, "file": "LWO2Parser.cjs", "sources": ["../../../src/loaders/lwo/LWO2Parser.js"], "sourcesContent": ["class LWO2Parser {\n  constructor(IFFParser) {\n    this.IFF = IFFParser\n  }\n\n  parseBlock() {\n    this.IFF.debugger.offset = this.IFF.reader.offset\n    this.IFF.debugger.closeForms()\n\n    const blockID = this.IFF.reader.getIDTag()\n    let length = this.IFF.reader.getUint32() // size of data in bytes\n    if (length > this.IFF.reader.dv.byteLength - this.IFF.reader.offset) {\n      this.IFF.reader.offset -= 4\n      length = this.IFF.reader.getUint16()\n    }\n\n    this.IFF.debugger.dataOffset = this.IFF.reader.offset\n    this.IFF.debugger.length = length\n\n    // Data types may be found in either LWO2 OR LWO3 spec\n    switch (blockID) {\n      case 'FORM': // form blocks may consist of sub -chunks or sub-forms\n        this.IFF.parseForm(length)\n        break\n\n      // SKIPPED CHUNKS\n      // if break; is called directly, the position in the lwoTree is not created\n      // any sub chunks and forms are added to the parent form instead\n      // MISC skipped\n      case 'ICON': // Thumbnail Icon Image\n      case 'VMPA': // Vertex Map Parameter\n      case 'BBOX': // bounding box\n      // case 'VMMD':\n      // case 'VTYP':\n\n      // normal maps can be specified, normally on models imported from other applications. Currently ignored\n      case 'NORM':\n\n      // ENVL FORM skipped\n      case 'PRE ':\n      case 'POST':\n      case 'KEY ':\n      case 'SPAN':\n\n      // CLIP FORM skipped\n      case 'TIME':\n      case 'CLRS':\n      case 'CLRA':\n      case 'FILT':\n      case 'DITH':\n      case 'CONT':\n      case 'BRIT':\n      case 'SATR':\n      case 'HUE ':\n      case 'GAMM':\n      case 'NEGA':\n      case 'IFLT':\n      case 'PFLT':\n\n      // Image Map Layer skipped\n      case 'PROJ':\n      case 'AXIS':\n      case 'AAST':\n      case 'PIXB':\n      case 'AUVO':\n      case 'STCK':\n\n      // Procedural Textures skipped\n      case 'PROC':\n      case 'VALU':\n      case 'FUNC':\n\n      // Gradient Textures skipped\n      case 'PNAM':\n      case 'INAM':\n      case 'GRST':\n      case 'GREN':\n      case 'GRPT':\n      case 'FKEY':\n      case 'IKEY':\n\n      // Texture Mapping Form skipped\n      case 'CSYS':\n\n      // Surface CHUNKs skipped\n      case 'OPAQ': // top level 'opacity' checkbox\n      case 'CMAP': // clip map\n\n      // Surface node CHUNKS skipped\n      // These mainly specify the node editor setup in LW\n      case 'NLOC':\n      case 'NZOM':\n      case 'NVER':\n      case 'NSRV':\n      case 'NVSK': // unknown\n      case 'NCRD':\n      case 'WRPW': // image wrap w ( for cylindrical and spherical projections)\n      case 'WRPH': // image wrap h\n      case 'NMOD':\n      case 'NSEL':\n      case 'NPRW':\n      case 'NPLA':\n      case 'NODS':\n      case 'VERS':\n      case 'ENUM':\n      case 'TAG ':\n      case 'OPAC':\n\n      // Car Material CHUNKS\n      case 'CGMD':\n      case 'CGTY':\n      case 'CGST':\n      case 'CGEN':\n      case 'CGTS':\n      case 'CGTE':\n      case 'OSMP':\n      case 'OMDE':\n      case 'OUTR':\n      case 'FLAG':\n\n      case 'TRNL':\n      case 'GLOW':\n      case 'GVAL': // glow intensity\n      case 'SHRP':\n      case 'RFOP':\n      case 'RSAN':\n      case 'TROP':\n      case 'RBLR':\n      case 'TBLR':\n      case 'CLRH':\n      case 'CLRF':\n      case 'ADTR':\n      case 'LINE':\n      case 'ALPH':\n      case 'VCOL':\n      case 'ENAB':\n        this.IFF.debugger.skipped = true\n        this.IFF.reader.skip(length)\n        break\n\n      case 'SURF':\n        this.IFF.parseSurfaceLwo2(length)\n        break\n\n      case 'CLIP':\n        this.IFF.parseClipLwo2(length)\n        break\n\n      // Texture node chunks (not in spec)\n      case 'IPIX': // usePixelBlending\n      case 'IMIP': // useMipMaps\n      case 'IMOD': // imageBlendingMode\n      case 'AMOD': // unknown\n      case 'IINV': // imageInvertAlpha\n      case 'INCR': // imageInvertColor\n      case 'IAXS': // imageAxis ( for non-UV maps)\n      case 'IFOT': // imageFallofType\n      case 'ITIM': // timing for animated textures\n      case 'IWRL':\n      case 'IUTI':\n      case 'IINX':\n      case 'IINY':\n      case 'IINZ':\n      case 'IREF': // possibly a VX for reused texture nodes\n        if (length === 4) this.IFF.currentNode[blockID] = this.IFF.reader.getInt32()\n        else this.IFF.reader.skip(length)\n        break\n\n      case 'OTAG':\n        this.IFF.parseObjectTag()\n        break\n\n      case 'LAYR':\n        this.IFF.parseLayer(length)\n        break\n\n      case 'PNTS':\n        this.IFF.parsePoints(length)\n        break\n\n      case 'VMAP':\n        this.IFF.parseVertexMapping(length)\n        break\n\n      case 'AUVU':\n      case 'AUVN':\n        this.IFF.reader.skip(length - 1)\n        this.IFF.reader.getVariableLengthIndex() // VX\n        break\n\n      case 'POLS':\n        this.IFF.parsePolygonList(length)\n        break\n\n      case 'TAGS':\n        this.IFF.parseTagStrings(length)\n        break\n\n      case 'PTAG':\n        this.IFF.parsePolygonTagMapping(length)\n        break\n\n      case 'VMAD':\n        this.IFF.parseVertexMapping(length, true)\n        break\n\n      // Misc CHUNKS\n      case 'DESC': // Description Line\n        this.IFF.currentForm.description = this.IFF.reader.getString()\n        break\n\n      case 'TEXT':\n      case 'CMNT':\n      case 'NCOM':\n        this.IFF.currentForm.comment = this.IFF.reader.getString()\n        break\n\n      // Envelope Form\n      case 'NAME':\n        this.IFF.currentForm.channelName = this.IFF.reader.getString()\n        break\n\n      // Image Map Layer\n      case 'WRAP':\n        this.IFF.currentForm.wrap = { w: this.IFF.reader.getUint16(), h: this.IFF.reader.getUint16() }\n        break\n\n      case 'IMAG':\n        const index = this.IFF.reader.getVariableLengthIndex()\n        this.IFF.currentForm.imageIndex = index\n        break\n\n      // Texture Mapping Form\n      case 'OREF':\n        this.IFF.currentForm.referenceObject = this.IFF.reader.getString()\n        break\n\n      case 'ROID':\n        this.IFF.currentForm.referenceObjectID = this.IFF.reader.getUint32()\n        break\n\n      // Surface Blocks\n      case 'SSHN':\n        this.IFF.currentSurface.surfaceShaderName = this.IFF.reader.getString()\n        break\n\n      case 'AOVN':\n        this.IFF.currentSurface.surfaceCustomAOVName = this.IFF.reader.getString()\n        break\n\n      // Nodal Blocks\n      case 'NSTA':\n        this.IFF.currentForm.disabled = this.IFF.reader.getUint16()\n        break\n\n      case 'NRNM':\n        this.IFF.currentForm.realName = this.IFF.reader.getString()\n        break\n\n      case 'NNME':\n        this.IFF.currentForm.refName = this.IFF.reader.getString()\n        this.IFF.currentSurface.nodes[this.IFF.currentForm.refName] = this.IFF.currentForm\n        break\n\n      // Nodal Blocks : connections\n      case 'INME':\n        if (!this.IFF.currentForm.nodeName) this.IFF.currentForm.nodeName = []\n        this.IFF.currentForm.nodeName.push(this.IFF.reader.getString())\n        break\n\n      case 'IINN':\n        if (!this.IFF.currentForm.inputNodeName) this.IFF.currentForm.inputNodeName = []\n        this.IFF.currentForm.inputNodeName.push(this.IFF.reader.getString())\n        break\n\n      case 'IINM':\n        if (!this.IFF.currentForm.inputName) this.IFF.currentForm.inputName = []\n        this.IFF.currentForm.inputName.push(this.IFF.reader.getString())\n        break\n\n      case 'IONM':\n        if (!this.IFF.currentForm.inputOutputName) this.IFF.currentForm.inputOutputName = []\n        this.IFF.currentForm.inputOutputName.push(this.IFF.reader.getString())\n        break\n\n      case 'FNAM':\n        this.IFF.currentForm.fileName = this.IFF.reader.getString()\n        break\n\n      case 'CHAN': // NOTE: ENVL Forms may also have CHAN chunk, however ENVL is currently ignored\n        if (length === 4) this.IFF.currentForm.textureChannel = this.IFF.reader.getIDTag()\n        else this.IFF.reader.skip(length)\n        break\n\n      // LWO2 Spec chunks: these are needed since the SURF FORMs are often in LWO2 format\n      case 'SMAN':\n        const maxSmoothingAngle = this.IFF.reader.getFloat32()\n        this.IFF.currentSurface.attributes.smooth = maxSmoothingAngle < 0 ? false : true\n        break\n\n      // LWO2: Basic Surface Parameters\n      case 'COLR':\n        this.IFF.currentSurface.attributes.Color = { value: this.IFF.reader.getFloat32Array(3) }\n        this.IFF.reader.skip(2) // VX: envelope\n        break\n\n      case 'LUMI':\n        this.IFF.currentSurface.attributes.Luminosity = { value: this.IFF.reader.getFloat32() }\n        this.IFF.reader.skip(2)\n        break\n\n      case 'SPEC':\n        this.IFF.currentSurface.attributes.Specular = { value: this.IFF.reader.getFloat32() }\n        this.IFF.reader.skip(2)\n        break\n\n      case 'DIFF':\n        this.IFF.currentSurface.attributes.Diffuse = { value: this.IFF.reader.getFloat32() }\n        this.IFF.reader.skip(2)\n        break\n\n      case 'REFL':\n        this.IFF.currentSurface.attributes.Reflection = { value: this.IFF.reader.getFloat32() }\n        this.IFF.reader.skip(2)\n        break\n\n      case 'GLOS':\n        this.IFF.currentSurface.attributes.Glossiness = { value: this.IFF.reader.getFloat32() }\n        this.IFF.reader.skip(2)\n        break\n\n      case 'TRAN':\n        this.IFF.currentSurface.attributes.opacity = this.IFF.reader.getFloat32()\n        this.IFF.reader.skip(2)\n        break\n\n      case 'BUMP':\n        this.IFF.currentSurface.attributes.bumpStrength = this.IFF.reader.getFloat32()\n        this.IFF.reader.skip(2)\n        break\n\n      case 'SIDE':\n        this.IFF.currentSurface.attributes.side = this.IFF.reader.getUint16()\n        break\n\n      case 'RIMG':\n        this.IFF.currentSurface.attributes.reflectionMap = this.IFF.reader.getVariableLengthIndex()\n        break\n\n      case 'RIND':\n        this.IFF.currentSurface.attributes.refractiveIndex = this.IFF.reader.getFloat32()\n        this.IFF.reader.skip(2)\n        break\n\n      case 'TIMG':\n        this.IFF.currentSurface.attributes.refractionMap = this.IFF.reader.getVariableLengthIndex()\n        break\n\n      case 'IMAP':\n        this.IFF.reader.skip(2)\n        break\n\n      case 'TMAP':\n        this.IFF.debugger.skipped = true\n        this.IFF.reader.skip(length) // needs implementing\n        break\n\n      case 'IUVI': // uv channel name\n        this.IFF.currentNode.UVChannel = this.IFF.reader.getString(length)\n        break\n\n      case 'IUTL': // widthWrappingMode: 0 = Reset, 1 = Repeat, 2 = Mirror, 3 = Edge\n        this.IFF.currentNode.widthWrappingMode = this.IFF.reader.getUint32()\n        break\n      case 'IVTL': // heightWrappingMode\n        this.IFF.currentNode.heightWrappingMode = this.IFF.reader.getUint32()\n        break\n\n      // LWO2 USE\n      case 'BLOK':\n        // skip\n        break\n\n      default:\n        this.IFF.parseUnknownCHUNK(blockID, length)\n    }\n\n    if (blockID != 'FORM') {\n      this.IFF.debugger.node = 1\n      this.IFF.debugger.nodeID = blockID\n      this.IFF.debugger.log()\n    }\n\n    if (this.IFF.reader.offset >= this.IFF.currentFormEnd) {\n      this.IFF.currentForm = this.IFF.parentForm\n    }\n  }\n}\n\nexport { LWO2Parser }\n"], "names": [], "mappings": ";;AAAA,MAAM,WAAW;AAAA,EACf,YAAY,WAAW;AACrB,SAAK,MAAM;AAAA,EACZ;AAAA,EAED,aAAa;AACX,SAAK,IAAI,SAAS,SAAS,KAAK,IAAI,OAAO;AAC3C,SAAK,IAAI,SAAS,WAAY;AAE9B,UAAM,UAAU,KAAK,IAAI,OAAO,SAAU;AAC1C,QAAI,SAAS,KAAK,IAAI,OAAO,UAAW;AACxC,QAAI,SAAS,KAAK,IAAI,OAAO,GAAG,aAAa,KAAK,IAAI,OAAO,QAAQ;AACnE,WAAK,IAAI,OAAO,UAAU;AAC1B,eAAS,KAAK,IAAI,OAAO,UAAW;AAAA,IACrC;AAED,SAAK,IAAI,SAAS,aAAa,KAAK,IAAI,OAAO;AAC/C,SAAK,IAAI,SAAS,SAAS;AAG3B,YAAQ,SAAO;AAAA,MACb,KAAK;AACH,aAAK,IAAI,UAAU,MAAM;AACzB;AAAA,MAMF,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MAKL,KAAK;AAAA,MAGL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MAGL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MAGL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MAGL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MAGL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MAGL,KAAK;AAAA,MAGL,KAAK;AAAA,MACL,KAAK;AAAA,MAIL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MAGL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MAEL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,aAAK,IAAI,SAAS,UAAU;AAC5B,aAAK,IAAI,OAAO,KAAK,MAAM;AAC3B;AAAA,MAEF,KAAK;AACH,aAAK,IAAI,iBAAiB,MAAM;AAChC;AAAA,MAEF,KAAK;AACH,aAAK,IAAI,cAAc,MAAM;AAC7B;AAAA,MAGF,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,YAAI,WAAW;AAAG,eAAK,IAAI,YAAY,OAAO,IAAI,KAAK,IAAI,OAAO,SAAU;AAAA;AACvE,eAAK,IAAI,OAAO,KAAK,MAAM;AAChC;AAAA,MAEF,KAAK;AACH,aAAK,IAAI,eAAgB;AACzB;AAAA,MAEF,KAAK;AACH,aAAK,IAAI,WAAW,MAAM;AAC1B;AAAA,MAEF,KAAK;AACH,aAAK,IAAI,YAAY,MAAM;AAC3B;AAAA,MAEF,KAAK;AACH,aAAK,IAAI,mBAAmB,MAAM;AAClC;AAAA,MAEF,KAAK;AAAA,MACL,KAAK;AACH,aAAK,IAAI,OAAO,KAAK,SAAS,CAAC;AAC/B,aAAK,IAAI,OAAO,uBAAwB;AACxC;AAAA,MAEF,KAAK;AACH,aAAK,IAAI,iBAAiB,MAAM;AAChC;AAAA,MAEF,KAAK;AACH,aAAK,IAAI,gBAAgB,MAAM;AAC/B;AAAA,MAEF,KAAK;AACH,aAAK,IAAI,uBAAuB,MAAM;AACtC;AAAA,MAEF,KAAK;AACH,aAAK,IAAI,mBAAmB,QAAQ,IAAI;AACxC;AAAA,MAGF,KAAK;AACH,aAAK,IAAI,YAAY,cAAc,KAAK,IAAI,OAAO,UAAW;AAC9D;AAAA,MAEF,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,aAAK,IAAI,YAAY,UAAU,KAAK,IAAI,OAAO,UAAW;AAC1D;AAAA,MAGF,KAAK;AACH,aAAK,IAAI,YAAY,cAAc,KAAK,IAAI,OAAO,UAAW;AAC9D;AAAA,MAGF,KAAK;AACH,aAAK,IAAI,YAAY,OAAO,EAAE,GAAG,KAAK,IAAI,OAAO,UAAS,GAAI,GAAG,KAAK,IAAI,OAAO,YAAa;AAC9F;AAAA,MAEF,KAAK;AACH,cAAM,QAAQ,KAAK,IAAI,OAAO,uBAAwB;AACtD,aAAK,IAAI,YAAY,aAAa;AAClC;AAAA,MAGF,KAAK;AACH,aAAK,IAAI,YAAY,kBAAkB,KAAK,IAAI,OAAO,UAAW;AAClE;AAAA,MAEF,KAAK;AACH,aAAK,IAAI,YAAY,oBAAoB,KAAK,IAAI,OAAO,UAAW;AACpE;AAAA,MAGF,KAAK;AACH,aAAK,IAAI,eAAe,oBAAoB,KAAK,IAAI,OAAO,UAAW;AACvE;AAAA,MAEF,KAAK;AACH,aAAK,IAAI,eAAe,uBAAuB,KAAK,IAAI,OAAO,UAAW;AAC1E;AAAA,MAGF,KAAK;AACH,aAAK,IAAI,YAAY,WAAW,KAAK,IAAI,OAAO,UAAW;AAC3D;AAAA,MAEF,KAAK;AACH,aAAK,IAAI,YAAY,WAAW,KAAK,IAAI,OAAO,UAAW;AAC3D;AAAA,MAEF,KAAK;AACH,aAAK,IAAI,YAAY,UAAU,KAAK,IAAI,OAAO,UAAW;AAC1D,aAAK,IAAI,eAAe,MAAM,KAAK,IAAI,YAAY,OAAO,IAAI,KAAK,IAAI;AACvE;AAAA,MAGF,KAAK;AACH,YAAI,CAAC,KAAK,IAAI,YAAY;AAAU,eAAK,IAAI,YAAY,WAAW,CAAE;AACtE,aAAK,IAAI,YAAY,SAAS,KAAK,KAAK,IAAI,OAAO,WAAW;AAC9D;AAAA,MAEF,KAAK;AACH,YAAI,CAAC,KAAK,IAAI,YAAY;AAAe,eAAK,IAAI,YAAY,gBAAgB,CAAE;AAChF,aAAK,IAAI,YAAY,cAAc,KAAK,KAAK,IAAI,OAAO,WAAW;AACnE;AAAA,MAEF,KAAK;AACH,YAAI,CAAC,KAAK,IAAI,YAAY;AAAW,eAAK,IAAI,YAAY,YAAY,CAAE;AACxE,aAAK,IAAI,YAAY,UAAU,KAAK,KAAK,IAAI,OAAO,WAAW;AAC/D;AAAA,MAEF,KAAK;AACH,YAAI,CAAC,KAAK,IAAI,YAAY;AAAiB,eAAK,IAAI,YAAY,kBAAkB,CAAE;AACpF,aAAK,IAAI,YAAY,gBAAgB,KAAK,KAAK,IAAI,OAAO,WAAW;AACrE;AAAA,MAEF,KAAK;AACH,aAAK,IAAI,YAAY,WAAW,KAAK,IAAI,OAAO,UAAW;AAC3D;AAAA,MAEF,KAAK;AACH,YAAI,WAAW;AAAG,eAAK,IAAI,YAAY,iBAAiB,KAAK,IAAI,OAAO,SAAU;AAAA;AAC7E,eAAK,IAAI,OAAO,KAAK,MAAM;AAChC;AAAA,MAGF,KAAK;AACH,cAAM,oBAAoB,KAAK,IAAI,OAAO,WAAY;AACtD,aAAK,IAAI,eAAe,WAAW,SAAS,oBAAoB,IAAI,QAAQ;AAC5E;AAAA,MAGF,KAAK;AACH,aAAK,IAAI,eAAe,WAAW,QAAQ,EAAE,OAAO,KAAK,IAAI,OAAO,gBAAgB,CAAC,EAAG;AACxF,aAAK,IAAI,OAAO,KAAK,CAAC;AACtB;AAAA,MAEF,KAAK;AACH,aAAK,IAAI,eAAe,WAAW,aAAa,EAAE,OAAO,KAAK,IAAI,OAAO,WAAU,EAAI;AACvF,aAAK,IAAI,OAAO,KAAK,CAAC;AACtB;AAAA,MAEF,KAAK;AACH,aAAK,IAAI,eAAe,WAAW,WAAW,EAAE,OAAO,KAAK,IAAI,OAAO,WAAU,EAAI;AACrF,aAAK,IAAI,OAAO,KAAK,CAAC;AACtB;AAAA,MAEF,KAAK;AACH,aAAK,IAAI,eAAe,WAAW,UAAU,EAAE,OAAO,KAAK,IAAI,OAAO,WAAU,EAAI;AACpF,aAAK,IAAI,OAAO,KAAK,CAAC;AACtB;AAAA,MAEF,KAAK;AACH,aAAK,IAAI,eAAe,WAAW,aAAa,EAAE,OAAO,KAAK,IAAI,OAAO,WAAU,EAAI;AACvF,aAAK,IAAI,OAAO,KAAK,CAAC;AACtB;AAAA,MAEF,KAAK;AACH,aAAK,IAAI,eAAe,WAAW,aAAa,EAAE,OAAO,KAAK,IAAI,OAAO,WAAU,EAAI;AACvF,aAAK,IAAI,OAAO,KAAK,CAAC;AACtB;AAAA,MAEF,KAAK;AACH,aAAK,IAAI,eAAe,WAAW,UAAU,KAAK,IAAI,OAAO,WAAY;AACzE,aAAK,IAAI,OAAO,KAAK,CAAC;AACtB;AAAA,MAEF,KAAK;AACH,aAAK,IAAI,eAAe,WAAW,eAAe,KAAK,IAAI,OAAO,WAAY;AAC9E,aAAK,IAAI,OAAO,KAAK,CAAC;AACtB;AAAA,MAEF,KAAK;AACH,aAAK,IAAI,eAAe,WAAW,OAAO,KAAK,IAAI,OAAO,UAAW;AACrE;AAAA,MAEF,KAAK;AACH,aAAK,IAAI,eAAe,WAAW,gBAAgB,KAAK,IAAI,OAAO,uBAAwB;AAC3F;AAAA,MAEF,KAAK;AACH,aAAK,IAAI,eAAe,WAAW,kBAAkB,KAAK,IAAI,OAAO,WAAY;AACjF,aAAK,IAAI,OAAO,KAAK,CAAC;AACtB;AAAA,MAEF,KAAK;AACH,aAAK,IAAI,eAAe,WAAW,gBAAgB,KAAK,IAAI,OAAO,uBAAwB;AAC3F;AAAA,MAEF,KAAK;AACH,aAAK,IAAI,OAAO,KAAK,CAAC;AACtB;AAAA,MAEF,KAAK;AACH,aAAK,IAAI,SAAS,UAAU;AAC5B,aAAK,IAAI,OAAO,KAAK,MAAM;AAC3B;AAAA,MAEF,KAAK;AACH,aAAK,IAAI,YAAY,YAAY,KAAK,IAAI,OAAO,UAAU,MAAM;AACjE;AAAA,MAEF,KAAK;AACH,aAAK,IAAI,YAAY,oBAAoB,KAAK,IAAI,OAAO,UAAW;AACpE;AAAA,MACF,KAAK;AACH,aAAK,IAAI,YAAY,qBAAqB,KAAK,IAAI,OAAO,UAAW;AACrE;AAAA,MAGF,KAAK;AAEH;AAAA,MAEF;AACE,aAAK,IAAI,kBAAkB,SAAS,MAAM;AAAA,IAC7C;AAED,QAAI,WAAW,QAAQ;AACrB,WAAK,IAAI,SAAS,OAAO;AACzB,WAAK,IAAI,SAAS,SAAS;AAC3B,WAAK,IAAI,SAAS,IAAK;AAAA,IACxB;AAED,QAAI,KAAK,IAAI,OAAO,UAAU,KAAK,IAAI,gBAAgB;AACrD,WAAK,IAAI,cAAc,KAAK,IAAI;AAAA,IACjC;AAAA,EACF;AACH;;"}