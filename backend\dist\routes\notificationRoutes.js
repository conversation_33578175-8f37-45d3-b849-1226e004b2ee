"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const notificationController_1 = require("../controllers/notificationController");
const authMiddleware_1 = require("../middleware/authMiddleware"); // Protect notification routes
const router = express_1.default.Router();
router.get('/', authMiddleware_1.protect, notificationController_1.getNotifications); // GET /api/notifications
router.put('/mark-read/:id', authMiddleware_1.protect, notificationController_1.markAsRead); // PUT /api/notifications/mark-read/:id
router.put('/mark-all-read', authMiddleware_1.protect, notificationController_1.markAllAsRead); // PUT /api/notifications/mark-all-read
router.get('/count', authMiddleware_1.protect, notificationController_1.getUnreadCount); // GET /api/notifications/count
exports.default = router;
