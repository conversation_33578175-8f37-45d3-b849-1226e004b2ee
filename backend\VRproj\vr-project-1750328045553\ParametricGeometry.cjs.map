{"version": 3, "file": "ParametricGeometry.cjs", "sources": ["../../src/geometries/ParametricGeometry.js"], "sourcesContent": ["import { <PERSON><PERSON>erGeo<PERSON>, Float32BufferAttribute, Vector3 } from 'three'\n\n/**\n * Parametric Surfaces Geometry\n * based on the brilliant article by @prideout https://prideout.net/blog/old/blog/index.html@p=44.html\n */\nclass ParametricGeometry extends BufferGeometry {\n  constructor(func = (u, v, target) => target.set(u, v, Math.cos(u) * Math.sin(v)), slices = 8, stacks = 8) {\n    super()\n\n    this.type = 'ParametricGeometry'\n\n    this.parameters = {\n      func: func,\n      slices: slices,\n      stacks: stacks,\n    }\n\n    // buffers\n\n    const indices = []\n    const vertices = []\n    const normals = []\n    const uvs = []\n\n    const EPS = 0.00001\n\n    const normal = new Vector3()\n\n    const p0 = new Vector3(),\n      p1 = new Vector3()\n    const pu = new Vector3(),\n      pv = new Vector3()\n\n    // generate vertices, normals and uvs\n\n    const sliceCount = slices + 1\n\n    for (let i = 0; i <= stacks; i++) {\n      const v = i / stacks\n\n      for (let j = 0; j <= slices; j++) {\n        const u = j / slices\n\n        // vertex\n\n        func(u, v, p0)\n        vertices.push(p0.x, p0.y, p0.z)\n\n        // normal\n\n        // approximate tangent vectors via finite differences\n\n        if (u - EPS >= 0) {\n          func(u - EPS, v, p1)\n          pu.subVectors(p0, p1)\n        } else {\n          func(u + EPS, v, p1)\n          pu.subVectors(p1, p0)\n        }\n\n        if (v - EPS >= 0) {\n          func(u, v - EPS, p1)\n          pv.subVectors(p0, p1)\n        } else {\n          func(u, v + EPS, p1)\n          pv.subVectors(p1, p0)\n        }\n\n        // cross product of tangent vectors returns surface normal\n\n        normal.crossVectors(pu, pv).normalize()\n        normals.push(normal.x, normal.y, normal.z)\n\n        // uv\n\n        uvs.push(u, v)\n      }\n    }\n\n    // generate indices\n\n    for (let i = 0; i < stacks; i++) {\n      for (let j = 0; j < slices; j++) {\n        const a = i * sliceCount + j\n        const b = i * sliceCount + j + 1\n        const c = (i + 1) * sliceCount + j + 1\n        const d = (i + 1) * sliceCount + j\n\n        // faces one and two\n\n        indices.push(a, b, d)\n        indices.push(b, c, d)\n      }\n    }\n\n    // build geometry\n\n    this.setIndex(indices)\n    this.setAttribute('position', new Float32BufferAttribute(vertices, 3))\n    this.setAttribute('normal', new Float32BufferAttribute(normals, 3))\n    this.setAttribute('uv', new Float32BufferAttribute(uvs, 2))\n  }\n}\n\nexport { ParametricGeometry }\n"], "names": ["BufferGeometry", "Vector3", "Float32BufferAttribute"], "mappings": ";;;AAMA,MAAM,2BAA2BA,MAAAA,eAAe;AAAA,EAC9C,YAAY,OAAO,CAAC,GAAG,GAAG,WAAW,OAAO,IAAI,GAAG,GAAG,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,GAAG,SAAS,GAAG,SAAS,GAAG;AACxG,UAAO;AAEP,SAAK,OAAO;AAEZ,SAAK,aAAa;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAID,UAAM,UAAU,CAAE;AAClB,UAAM,WAAW,CAAE;AACnB,UAAM,UAAU,CAAE;AAClB,UAAM,MAAM,CAAE;AAEd,UAAM,MAAM;AAEZ,UAAM,SAAS,IAAIC,cAAS;AAE5B,UAAM,KAAK,IAAIA,cAAS,GACtB,KAAK,IAAIA,MAAAA,QAAS;AACpB,UAAM,KAAK,IAAIA,cAAS,GACtB,KAAK,IAAIA,MAAAA,QAAS;AAIpB,UAAM,aAAa,SAAS;AAE5B,aAAS,IAAI,GAAG,KAAK,QAAQ,KAAK;AAChC,YAAM,IAAI,IAAI;AAEd,eAAS,IAAI,GAAG,KAAK,QAAQ,KAAK;AAChC,cAAM,IAAI,IAAI;AAId,aAAK,GAAG,GAAG,EAAE;AACb,iBAAS,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAM9B,YAAI,IAAI,OAAO,GAAG;AAChB,eAAK,IAAI,KAAK,GAAG,EAAE;AACnB,aAAG,WAAW,IAAI,EAAE;AAAA,QAC9B,OAAe;AACL,eAAK,IAAI,KAAK,GAAG,EAAE;AACnB,aAAG,WAAW,IAAI,EAAE;AAAA,QACrB;AAED,YAAI,IAAI,OAAO,GAAG;AAChB,eAAK,GAAG,IAAI,KAAK,EAAE;AACnB,aAAG,WAAW,IAAI,EAAE;AAAA,QAC9B,OAAe;AACL,eAAK,GAAG,IAAI,KAAK,EAAE;AACnB,aAAG,WAAW,IAAI,EAAE;AAAA,QACrB;AAID,eAAO,aAAa,IAAI,EAAE,EAAE,UAAW;AACvC,gBAAQ,KAAK,OAAO,GAAG,OAAO,GAAG,OAAO,CAAC;AAIzC,YAAI,KAAK,GAAG,CAAC;AAAA,MACd;AAAA,IACF;AAID,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,cAAM,IAAI,IAAI,aAAa;AAC3B,cAAM,IAAI,IAAI,aAAa,IAAI;AAC/B,cAAM,KAAK,IAAI,KAAK,aAAa,IAAI;AACrC,cAAM,KAAK,IAAI,KAAK,aAAa;AAIjC,gBAAQ,KAAK,GAAG,GAAG,CAAC;AACpB,gBAAQ,KAAK,GAAG,GAAG,CAAC;AAAA,MACrB;AAAA,IACF;AAID,SAAK,SAAS,OAAO;AACrB,SAAK,aAAa,YAAY,IAAIC,MAAAA,uBAAuB,UAAU,CAAC,CAAC;AACrE,SAAK,aAAa,UAAU,IAAIA,MAAAA,uBAAuB,SAAS,CAAC,CAAC;AAClE,SAAK,aAAa,MAAM,IAAIA,MAAAA,uBAAuB,KAAK,CAAC,CAAC;AAAA,EAC3D;AACH;;"}