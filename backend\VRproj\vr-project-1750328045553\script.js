// VR Experience JavaScript
console.log('VR Experience script loaded');

// Configuration
const VR_CONFIG = {
    enablePhysics: false,
    enableSound: true,
    debugMode: false
};

// Initialize VR experience
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing VR experience');
    
    initializeVRExperience();
    setupInteractions();
    setupVREventHandlers();
});

function initializeVRExperience() {
    console.log('Initializing VR experience...');
    
    // Add loading indicator
    showLoading();
    
    // Wait for A-Frame to be ready
    const scene = document.querySelector('a-scene');
    if (scene.hasLoaded) {
        onSceneLoaded();
    } else {
        scene.addEventListener('loaded', onSceneLoaded);
    }
}

function onSceneLoaded() {
    console.log('A-Frame scene loaded');
    hideLoading();
    
    // Add welcome animation
    animateWelcome();
    
    // Setup performance monitoring
    if (VR_CONFIG.debugMode) {
        setupPerformanceMonitoring();
    }
}

function setupInteractions() {
    console.log('Setting up interactions...');
    
    const clickableElements = document.querySelectorAll('.clickable');
    
    clickableElements.forEach((element, index) => {
        // Mouse/touch interactions
        element.addEventListener('click', function(event) {
            console.log(`Clicked element ${index}:`, this.tagName);
            handleElementClick(this, event);
        });
        
        element.addEventListener('mouseenter', function() {
            this.emit('mouseenter');
            if (VR_CONFIG.enableSound) {
                playHoverSound();
            }
        });
        
        element.addEventListener('mouseleave', function() {
            this.emit('mouseleave');
        });
        
        // VR controller interactions
        element.addEventListener('raycaster-intersected', function() {
            console.log('VR controller intersected:', this.tagName);
            this.emit('mouseenter');
        });
        
        element.addEventListener('raycaster-intersected-cleared', function() {
            this.emit('mouseleave');
        });
    });
}

function handleElementClick(element, event) {
    // Trigger click animation
    element.emit('click');
    
    // Add visual feedback
    addClickEffect(element);
    
    // Play sound
    if (VR_CONFIG.enableSound) {
        playClickSound();
    }
    
    // Custom behavior based on element type
    const tagName = element.tagName.toLowerCase();
    switch(tagName) {
        case 'a-box':
            console.log('Box clicked - spinning!');
            break;
        case 'a-sphere':
            console.log('Sphere clicked - bouncing!');
            break;
        case 'a-cylinder':
            console.log('Cylinder clicked - scaling!');
            break;
        default:
            console.log('Unknown element clicked');
    }
}

function addClickEffect(element) {
    // Add temporary glow effect
    const originalColor = element.getAttribute('color');
    element.setAttribute('color', '#ffffff');
    
    setTimeout(() => {
        element.setAttribute('color', originalColor);
    }, 200);
}

function setupVREventHandlers() {
    console.log('Setting up VR event handlers...');
    
    const scene = document.querySelector('a-scene');
    
    scene.addEventListener('enter-vr', function() {
        console.log('Entered VR mode');
        document.body.classList.add('vr-mode');
        showVRWelcome();
    });
    
    scene.addEventListener('exit-vr', function() {
        console.log('Exited VR mode');
        document.body.classList.remove('vr-mode');
    });
    
    // Handle VR controller events
    const controllers = document.querySelectorAll('[laser-controls]');
    controllers.forEach(controller => {
        controller.addEventListener('triggerdown', function() {
            console.log('VR trigger pressed');
        });
        
        controller.addEventListener('gripdown', function() {
            console.log('VR grip pressed');
        });
    });
}

function animateWelcome() {
    const welcomeText = document.querySelector('a-text');
    if (welcomeText) {
        welcomeText.setAttribute('animation', 
            'property: position; to: 0 3.5 -6; dur: 2000; easing: easeOutBounce');
    }
}

function showVRWelcome() {
    // Create VR-specific welcome message
    const scene = document.querySelector('a-scene');
    const vrWelcome = document.createElement('a-text');
    vrWelcome.setAttribute('value', 'Welcome to VR Mode!');
    vrWelcome.setAttribute('position', '0 2 -3');
    vrWelcome.setAttribute('align', 'center');
    vrWelcome.setAttribute('color', '#4CC3D9');
    vrWelcome.setAttribute('animation', 
        'property: scale; from: 0 0 0; to: 1 1 1; dur: 1000; easing: easeOutElastic');
    
    scene.appendChild(vrWelcome);
    
    // Remove after 3 seconds
    setTimeout(() => {
        if (vrWelcome.parentNode) {
            vrWelcome.parentNode.removeChild(vrWelcome);
        }
    }, 3000);
}

function showLoading() {
    const loading = document.createElement('div');
    loading.className = 'loading';
    loading.innerHTML = '🥽 Loading VR Experience...';
    document.body.appendChild(loading);
}

function hideLoading() {
    const loading = document.querySelector('.loading');
    if (loading) {
        loading.remove();
    }
}

function playClickSound() {
    // Simple audio feedback using Web Audio API
    if (typeof AudioContext !== 'undefined' || typeof webkitAudioContext !== 'undefined') {
        const audioContext = new (AudioContext || webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);
        
        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.1);
    }
}

function playHoverSound() {
    // Subtle hover sound
    if (typeof AudioContext !== 'undefined' || typeof webkitAudioContext !== 'undefined') {
        const audioContext = new (AudioContext || webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.setValueAtTime(600, audioContext.currentTime);
        gainNode.gain.setValueAtTime(0.05, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.05);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.05);
    }
}

function setupPerformanceMonitoring() {
    console.log('Setting up performance monitoring...');
    
    const scene = document.querySelector('a-scene');
    let frameCount = 0;
    let lastTime = performance.now();
    
    scene.addEventListener('renderstart', function() {
        frameCount++;
        const currentTime = performance.now();
        
        if (currentTime - lastTime >= 1000) {
            const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
            console.log(`FPS: ${fps}`);
            frameCount = 0;
            lastTime = currentTime;
        }
    });
}

// Export for global access
window.VRExperience = {
    config: VR_CONFIG,
    playClickSound,
    playHoverSound,
    showVRWelcome
};

console.log('VR Experience initialized successfully!');
