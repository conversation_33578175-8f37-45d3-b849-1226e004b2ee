"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteQuiz = exports.updateQuiz = exports.getQuizById = exports.getQuizzes = exports.createQuiz = void 0;
const Quiz_1 = __importDefault(require("../models/Quiz"));
// Create a new quiz
const createQuiz = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const newQuiz = new Quiz_1.default(req.body);
        const savedQuiz = yield newQuiz.save();
        res.status(201).json(savedQuiz);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.createQuiz = createQuiz;
// Get all quizzes
const getQuizzes = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const quizzes = yield Quiz_1.default.find();
        res.status(200).json(quizzes);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.getQuizzes = getQuizzes;
// Get a single quiz by ID
const getQuizById = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const quiz = yield Quiz_1.default.findById(req.params.id);
        if (!quiz) {
            return res.status(404).json({ message: 'Quiz not found' });
        }
        res.status(200).json(quiz);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.getQuizById = getQuizById;
// Update a quiz by ID
const updateQuiz = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const updatedQuiz = yield Quiz_1.default.findByIdAndUpdate(req.params.id, req.body, { new: true });
        if (!updatedQuiz) {
            return res.status(404).json({ message: 'Quiz not found' });
        }
        res.status(200).json(updatedQuiz);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.updateQuiz = updateQuiz;
// Delete a quiz by ID
const deleteQuiz = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const deletedQuiz = yield Quiz_1.default.findByIdAndDelete(req.params.id);
        if (!deletedQuiz) {
            return res.status(404).json({ message: 'Quiz not found' });
        }
        res.status(200).json({ message: 'Quiz deleted' });
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.deleteQuiz = deleteQuiz;
