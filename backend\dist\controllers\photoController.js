"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updatePhotoStatus = exports.deletePhoto = exports.updatePhoto = exports.getPhotoById = exports.getPhotos = exports.createPhoto = void 0;
const Photo_1 = __importDefault(require("../models/Photo"));
// Create a new photo
const createPhoto = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const newPhoto = new Photo_1.default(req.body);
        const savedPhoto = yield newPhoto.save();
        res.status(201).json(savedPhoto);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.createPhoto = createPhoto;
// Get all photos with filtering, pagination, and sorting
const getPhotos = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page = 1, limit = 10, status, category, search, sortBy, sortOrder } = req.query;
        const query = {};
        if (status) {
            query.status = status;
        }
        if (category) {
            query.category = category;
        }
        if (search) {
            query.$or = [
                { title: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } },
            ];
        }
        const sort = {};
        if (sortBy) {
            sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
        }
        else {
            sort.createdAt = -1; // Default sort by creation date descending
        }
        const options = {
            skip: (Number(page) - 1) * Number(limit),
            limit: Number(limit),
            sort,
        };
        const photos = yield Photo_1.default.find(query, null, options);
        const total = yield Photo_1.default.countDocuments(query);
        res.status(200).json({
            data: photos,
            pagination: {
                total,
                page: Number(page),
                limit: Number(limit),
                pages: Math.ceil(total / Number(limit)),
            },
        });
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.getPhotos = getPhotos;
// Get a single photo by ID
const getPhotoById = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const photo = yield Photo_1.default.findById(req.params.id);
        if (!photo) {
            return res.status(404).json({ message: 'Photo not found' });
        }
        res.status(200).json(photo);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.getPhotoById = getPhotoById;
// Update a photo by ID
const updatePhoto = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const updatedPhoto = yield Photo_1.default.findByIdAndUpdate(req.params.id, Object.assign(Object.assign({}, req.body), { updatedAt: new Date() }), { new: true });
        if (!updatedPhoto) {
            return res.status(404).json({ message: 'Photo not found' });
        }
        res.status(200).json(updatedPhoto);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.updatePhoto = updatePhoto;
// Delete a photo by ID
const deletePhoto = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const deletedPhoto = yield Photo_1.default.findByIdAndDelete(req.params.id);
        if (!deletedPhoto) {
            return res.status(404).json({ message: 'Photo not found' });
        }
        res.status(200).json({ message: 'Photo deleted' });
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.deletePhoto = deletePhoto;
// Update photo status by ID
const updatePhotoStatus = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { status } = req.body;
        const updatedPhoto = yield Photo_1.default.findByIdAndUpdate(req.params.id, { status, updatedAt: new Date() }, { new: true });
        if (!updatedPhoto) {
            return res.status(404).json({ message: 'Photo not found' });
        }
        res.status(200).json(updatedPhoto);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.updatePhotoStatus = updatePhotoStatus;
