"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const slugify_1 = __importDefault(require("slugify")); // Import slugify
const ArticleSchema = new mongoose_1.Schema({
    title: { type: String, required: true },
    slug: { type: String, unique: true }, // Added slug field with unique constraint
    description: { type: String, required: true },
    content: { type: String, required: true },
    excerpt: { type: String },
    category: { type: String },
    tags: [{ type: String }],
    images: [{ type: String }],
    status: { type: String, enum: ['draft', 'published', 'review', 'archived'], default: 'draft' },
    author: { type: String },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
});
// Generate slug before saving
ArticleSchema.pre('save', function (next) {
    const article = this; // Cast 'this' to unknown first, then to IArticle
    if (article.isModified('title') && !article.slug) {
        article.slug = (0, slugify_1.default)(article.title, { lower: true, strict: true });
    }
    article.updatedAt = new Date();
    next();
});
const Article = mongoose_1.default.model('Article', ArticleSchema);
exports.default = Article;
