"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUnreadCount = exports.markAllAsRead = exports.markAsRead = exports.getNotifications = void 0;
const Notification_1 = __importDefault(require("../models/Notification"));
const mongoose_1 = __importDefault(require("mongoose"));
// Get notifications with filtering and pagination
const getNotifications = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page = 1, limit = 10, type, read } = req.query;
        const userId = req.user._id; // Assuming user ID is attached by auth middleware
        const query = { userId: new mongoose_1.default.Types.ObjectId(userId) };
        if (type) {
            query.type = type;
        }
        if (read !== undefined) {
            query.read = read === 'true';
        }
        const options = {
            skip: (Number(page) - 1) * Number(limit),
            limit: Number(limit),
            sort: { createdAt: -1 }, // Sort by latest first
        };
        const notifications = yield Notification_1.default.find(query, null, options);
        const total = yield Notification_1.default.countDocuments(query);
        res.status(200).json({
            data: notifications,
            pagination: {
                total,
                page: Number(page),
                limit: Number(limit),
                pages: Math.ceil(total / Number(limit)),
            },
        });
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.getNotifications = getNotifications;
// Mark a single notification as read
const markAsRead = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const notificationId = req.params.id;
        const userId = req.user._id; // Assuming user ID is attached by auth middleware
        const notification = yield Notification_1.default.findOneAndUpdate({ _id: notificationId, userId: new mongoose_1.default.Types.ObjectId(userId) }, { read: true }, { new: true });
        if (!notification) {
            return res.status(404).json({ message: 'Notification not found or does not belong to user' });
        }
        res.status(200).json(notification);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.markAsRead = markAsRead;
// Mark all notifications as read for the current user
const markAllAsRead = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user._id; // Assuming user ID is attached by auth middleware
        yield Notification_1.default.updateMany({ userId: new mongoose_1.default.Types.ObjectId(userId), read: false }, { read: true });
        res.status(200).json({ message: 'All notifications marked as read' });
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.markAllAsRead = markAllAsRead;
// Get unread notification count for the current user
const getUnreadCount = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.user._id; // Assuming user ID is attached by auth middleware
        const unreadCount = yield Notification_1.default.countDocuments({
            userId: new mongoose_1.default.Types.ObjectId(userId),
            read: false,
        });
        res.status(200).json({ count: unreadCount });
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.getUnreadCount = getUnreadCount;
