import { Request, Response } from 'express';
import Experience, { IExperience } from '../models/Experience';
import { organizeVRFiles } from '../middleware/vrUploadMiddleware';

// Create a new experience
export const createExperience = async (req: Request, res: Response) => {
  try {
    const newExperience: IExperience = new Experience(req.body);
    const savedExperience = await newExperience.save();
    res.status(201).json(savedExperience);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// Get all experiences with filtering, pagination, and sorting
export const getExperiences = async (req: Request, res: Response) => {
  try {
    const { page = 1, limit = 10, status, category, search, isFeatured, isVR, isAR, sortBy, sortOrder } = req.query;

    const query: any = {};
    if (status) {
      query.status = status;
    }
    if (category) {
      query.category = category;
    }
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
      ];
    }
    if (isFeatured !== undefined) {
      query.isFeatured = isFeatured === 'true';
    }
     if (isVR !== undefined) {
      query.isVR = isVR === 'true';
    }
    if (isAR !== undefined) {
      query.isAR = isAR === 'true';
    }


    const sort: any = {};
    if (sortBy) {
      sort[sortBy as string] = sortOrder === 'desc' ? -1 : 1;
    } else {
      sort.createdAt = -1; // Default sort by creation date descending
    }

    const options = {
      skip: (Number(page) - 1) * Number(limit),
      limit: Number(limit),
      sort,
    };

    const experiences = await Experience.find(query, null, options);
    const total = await Experience.countDocuments(query);

    res.status(200).json({
      data: experiences,
      pagination: {
        total,
        page: Number(page),
        limit: Number(limit),
        pages: Math.ceil(total / Number(limit)),
      },
    });
  } catch (error: any) {
    console.error('Error fetching experiences:', error); // Added specific logging
    res.status(500).json({ message: error.message });
  }
};

// Get a single experience by ID
export const getExperienceById = async (req: Request, res: Response) => {
  try {
    const experience = await Experience.findById(req.params.id);
    if (!experience) {
      return res.status(404).json({ message: 'Experience not found' });
    }
    res.status(200).json(experience);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// Update an experience by ID
export const updateExperience = async (req: Request, res: Response) => {
  try {
    const updatedExperience = await Experience.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!updatedExperience) {
      return res.status(404).json({ message: 'Experience not found' });
    }
    res.status(200).json(updatedExperience);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// Delete an experience by ID
export const deleteExperience = async (req: Request, res: Response) => {
  try {
    const deletedExperience = await Experience.findByIdAndDelete(req.params.id);
    if (!deletedExperience) {
      return res.status(404).json({ message: 'Experience not found' });
    }
    res.status(200).json({ message: 'Experience deleted' });
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// Update experience status by ID
export const updateExperienceStatus = async (req: Request, res: Response) => {
  try {
    const { status } = req.body;
    const updatedExperience = await Experience.findByIdAndUpdate(req.params.id, { status }, { new: true });
    if (!updatedExperience) {
      return res.status(404).json({ message: 'Experience not found' });
    }
    res.status(200).json(updatedExperience);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// Toggle experience featured status by ID
export const toggleExperienceFeatured = async (req: Request, res: Response) => {
  try {
    const { isFeatured } = req.body;
    const updatedExperience = await Experience.findByIdAndUpdate(req.params.id, { isFeatured }, { new: true });
    if (!updatedExperience) {
      return res.status(404).json({ message: 'Experience not found' });
    }
    res.status(200).json(updatedExperience);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// Create a VR experience with file uploads
export const createVRExperience = async (req: Request, res: Response) => {
  try {
    console.log('=== VR Experience Upload Debug ===');
    console.log('VR UPLOAD ENDPOINT CALLED!');
    console.log('Request method:', req.method);
    console.log('Request URL:', req.url);
    console.log('Request headers:', req.headers);
    console.log('Request body keys:', Object.keys(req.body));
    console.log('Request files:', req.files ? `${(req.files as any[]).length} files` : 'No files');
    console.log('Project ID:', req.body.projectId);
    console.log('Title:', req.body.title);
    console.log('Description:', req.body.description);

    const files = req.files as Express.Multer.File[];
    const { projectId, vrDescription, vrControls, title, description } = req.body;

    console.log('Parsed files:', files ? files.length : 0);
    console.log('Parsed projectId:', projectId);
    console.log('Parsed title:', title);
    console.log('Parsed description:', description);

    if (!files || files.length === 0) {
      console.log('ERROR: No files received');
      return res.status(400).json({ message: 'No VR project files uploaded' });
    }

    if (!title) {
      console.log('ERROR: No title provided');
      return res.status(400).json({ message: 'Title is required for VR experience' });
    }

    // Organize uploaded files
    const organizedFiles = organizeVRFiles(files, projectId);

    // Find main HTML file name (just the filename, not full path)
    let mainFileName = 'index.html'; // default
    if (organizedFiles.mainFile) {
      mainFileName = organizedFiles.mainFile.split('/').pop() || 'index.html';
    } else if (organizedFiles.htmlFiles.length > 0) {
      mainFileName = organizedFiles.htmlFiles[0].split('/').pop() || 'index.html';
    }

    // Create experience data with VR project info (only store folder path)
    const experienceData = {
      title: title,
      description: description || 'VR Experience',
      isVR: true,
      status: req.body.status || 'published', // Use valid enum value
      isFeatured: req.body.isFeatured || false, // Use correct field name
      vrProject: {
        folderPath: projectId, // Just store the project folder name
        mainFile: mainFileName, // Store just the main file name
        description: vrDescription || '',
        controls: vrControls || 'Use VR headset or mouse/keyboard to navigate',
      }
    };

    console.log('Creating experience with data:', experienceData);

    // Save to database immediately, before any potential server restart
    const newExperience: IExperience = new Experience(experienceData);
    console.log('Saving experience to database...');

    // Use a more robust save with error handling
    let savedExperience;
    try {
      savedExperience = await newExperience.save();
      console.log('Experience saved successfully:', savedExperience._id);
    } catch (saveError: any) {
      console.error('Database save error:', saveError);
      // Try to save again with a simplified approach
      const simpleExperienceData = {
        title: title,
        description: description || 'VR Experience',
        isVR: true,
        status: 'published', // Use valid enum value
        isFeatured: false, // Use correct field name
        vrProject: {
          folderPath: projectId,
          mainFile: mainFileName,
          description: vrDescription || '',
          controls: vrControls || 'Use VR headset or mouse/keyboard to navigate',
        }
      };

      const retryExperience = new Experience(simpleExperienceData);
      savedExperience = await retryExperience.save();
      console.log('Experience saved on retry:', savedExperience._id);
    }

    console.log('Sending success response...');
    res.status(201).json({
      ...savedExperience.toObject(),
      message: `VR project uploaded successfully to VRproj/${projectId}`,
      filesUploaded: files.length,
      vrLaunchUrl: `/vr/${projectId}/${mainFileName}`
    });
  } catch (error: any) {
    console.error('VR Experience creation error:', error);
    console.error('Error stack:', error.stack);

    // Try to provide more specific error messages
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map((err: any) => err.message);
      return res.status(400).json({
        message: 'Validation error',
        errors: validationErrors
      });
    }

    res.status(500).json({
      message: error.message || 'Failed to create VR experience',
      error: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};