"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const photoController_1 = require("../controllers/photoController");
const router = express_1.default.Router();
router.post('/', photoController_1.createPhoto);
router.get('/', photoController_1.getPhotos);
router.get('/:id', photoController_1.getPhotoById);
router.put('/:id', photoController_1.updatePhoto);
router.delete('/:id', photoController_1.deletePhoto);
router.patch('/:id/status', photoController_1.updatePhotoStatus); // Add route for updatePhotoStatus
exports.default = router;
