{"version": 3, "file": "DigitalGlitch.cjs", "sources": ["../../src/shaders/DigitalGlitch.ts"], "sourcesContent": ["export const DigitalGlitch = {\n  uniforms: {\n    tDiffuse: { value: null }, //diffuse texture\n    tDisp: { value: null }, //displacement texture for digital glitch squares\n    byp: { value: 0 }, //apply the glitch ?\n    amount: { value: 0.08 },\n    angle: { value: 0.02 },\n    seed: { value: 0.02 },\n    seed_x: { value: 0.02 }, //-1,1\n    seed_y: { value: 0.02 }, //-1,1\n    distortion_x: { value: 0.5 },\n    distortion_y: { value: 0.6 },\n    col_s: { value: 0.05 },\n  },\n\n  vertexShader: /* glsl */ `\n\t\tvarying vec2 vUv;\n\n\t\tvoid main() {\n\t\t\tvUv = uv;\n\t\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\t\t}\n\t`,\n\n  fragmentShader: /* glsl */ `\n\t\tuniform int byp; //should we apply the glitch ?\n\t\tuniform sampler2D tDiffuse;\n\t\tuniform sampler2D tDisp;\n\t\tuniform float amount;\n\t\tuniform float angle;\n\t\tuniform float seed;\n\t\tuniform float seed_x;\n\t\tuniform float seed_y;\n\t\tuniform float distortion_x;\n\t\tuniform float distortion_y;\n\t\tuniform float col_s;\n\n\t\tvarying vec2 vUv;\n\n\t\tfloat rand(vec2 co){\n\t\t\treturn fract(sin(dot(co.xy ,vec2(12.9898,78.233))) * 43758.5453);\n\t\t}\n\n\t\tvoid main() {\n\t\t\tif(byp<1) {\n\t\t\t\tvec2 p = vUv;\n\t\t\t\tfloat xs = floor(gl_FragCoord.x / 0.5);\n\t\t\t\tfloat ys = floor(gl_FragCoord.y / 0.5);\n\t\t//based on staffantans glitch shader for unity https://github.com/staffantan/unityglitch\n\t\t\t\tvec4 normal = texture2D (tDisp, p*seed*seed);\n\t\t\t\tif(p.y<distortion_x+col_s && p.y>distortion_x-col_s*seed) {\n\t\t\t\t\tif(seed_x>0.){\n\t\t\t\t\t\tp.y = 1. - (p.y + distortion_y);\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tp.y = distortion_y;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif(p.x<distortion_y+col_s && p.x>distortion_y-col_s*seed) {\n\t\t\t\t\tif(seed_y>0.){\n\t\t\t\t\t\tp.x=distortion_x;\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tp.x = 1. - (p.x + distortion_x);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tp.x+=normal.x*seed_x*(seed/5.);\n\t\t\t\tp.y+=normal.y*seed_y*(seed/5.);\n\t\t//base from RGB shift shader\n\t\t\t\tvec2 offset = amount * vec2( cos(angle), sin(angle));\n\t\t\t\tvec4 cr = texture2D(tDiffuse, p + offset);\n\t\t\t\tvec4 cga = texture2D(tDiffuse, p);\n\t\t\t\tvec4 cb = texture2D(tDiffuse, p - offset);\n\t\t\t\tgl_FragColor = vec4(cr.r, cga.g, cb.b, cga.a);\n\t\t//add noise\n\t\t\t\tvec4 snow = 200.*amount*vec4(rand(vec2(xs * seed,ys * seed*50.))*0.2);\n\t\t\t\tgl_FragColor = gl_FragColor+ snow;\n\t\t\t}\n\t\t\telse {\n\t\t\t\tgl_FragColor=texture2D (tDiffuse, vUv);\n\t\t\t}\n\t\t}\n`,\n}\n"], "names": [], "mappings": ";;AAAO,MAAM,gBAAgB;AAAA,EAC3B,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA;AAAA,IACxB,OAAO,EAAE,OAAO,KAAK;AAAA;AAAA,IACrB,KAAK,EAAE,OAAO,EAAE;AAAA;AAAA,IAChB,QAAQ,EAAE,OAAO,KAAK;AAAA,IACtB,OAAO,EAAE,OAAO,KAAK;AAAA,IACrB,MAAM,EAAE,OAAO,KAAK;AAAA,IACpB,QAAQ,EAAE,OAAO,KAAK;AAAA;AAAA,IACtB,QAAQ,EAAE,OAAO,KAAK;AAAA;AAAA,IACtB,cAAc,EAAE,OAAO,IAAI;AAAA,IAC3B,cAAc,EAAE,OAAO,IAAI;AAAA,IAC3B,OAAO,EAAE,OAAO,KAAK;AAAA,EACvB;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA2D7B;;"}