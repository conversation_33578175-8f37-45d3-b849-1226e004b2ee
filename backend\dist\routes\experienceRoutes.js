"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const experienceController_1 = require("../controllers/experienceController");
const authMiddleware_1 = require("../middleware/authMiddleware"); // Protect experience routes
const vrUploadMiddleware_1 = require("../middleware/vrUploadMiddleware");
const Experience_1 = __importDefault(require("../models/Experience")); // Import Experience model
const vrSyncService_1 = require("../services/vrSyncService"); // Import VR sync service
const router = express_1.default.Router();
router.post('/', authMiddleware_1.protect, experienceController_1.createExperience);
// VR sync route - manually sync VR projects from filesystem to database
router.post('/vr-sync', authMiddleware_1.protect, (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        console.log('Manual VR sync requested');
        yield vrSyncService_1.vrSyncService.syncVRProjects();
        res.json({ message: 'VR projects sync completed successfully' });
    }
    catch (error) {
        console.error('VR sync error:', error);
        res.status(500).json({ message: 'VR sync failed', error: error.message });
    }
}));
// VR upload route with multer
router.post('/vr', authMiddleware_1.protect, vrUploadMiddleware_1.uploadVRProject, experienceController_1.createVRExperience);
// Test upload endpoint (no auth for testing)
router.post('/test-upload', vrUploadMiddleware_1.uploadVRProject, (req, res) => {
    console.log('=== TEST UPLOAD ===');
    console.log('Files received:', req.files ? req.files.length : 0);
    console.log('Body:', req.body);
    if (req.files && req.files.length > 0) {
        req.files.slice(0, 3).forEach((file, index) => {
            console.log(`Test File ${index}:`, {
                originalname: file.originalname,
                filename: file.filename,
                path: file.path,
                size: file.size
            });
        });
    }
    res.json({
        message: 'Test upload successful',
        filesReceived: req.files ? req.files.length : 0
    });
});
// Route to get VR project info and launch URL
router.get('/:id/vr-launch', authMiddleware_1.protect, (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const experience = yield Experience_1.default.findById(req.params.id);
        if (!experience || !experience.isVR || !experience.vrProject) {
            return res.status(404).json({ message: 'VR experience not found' });
        }
        const vrProject = experience.vrProject;
        const launchUrl = `/vr/${vrProject.folderPath}/${vrProject.mainFile || 'index.html'}`;
        res.json({
            id: experience._id,
            title: experience.title,
            description: experience.description,
            vrProject: {
                folderPath: vrProject.folderPath,
                mainFile: vrProject.mainFile,
                description: vrProject.description,
                controls: vrProject.controls,
                launchUrl: launchUrl
            }
        });
    }
    catch (error) {
        console.error('Error getting VR launch info:', error);
        res.status(500).json({ message: error.message });
    }
}));
router.get('/', authMiddleware_1.protect, experienceController_1.getExperiences);
router.get('/:id', authMiddleware_1.protect, experienceController_1.getExperienceById);
router.put('/:id', authMiddleware_1.protect, experienceController_1.updateExperience);
router.delete('/:id', authMiddleware_1.protect, experienceController_1.deleteExperience);
router.patch('/:id/status', authMiddleware_1.protect, experienceController_1.updateExperienceStatus);
router.patch('/:id/featured', authMiddleware_1.protect, experienceController_1.toggleExperienceFeatured);
exports.default = router;
