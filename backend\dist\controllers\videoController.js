"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateVideoStatus = exports.deleteVideo = exports.updateVideo = exports.getVideoById = exports.getVideos = exports.createVideo = void 0;
const Video_1 = __importDefault(require("../models/Video"));
// Create a new video
const createVideo = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const newVideo = new Video_1.default(req.body);
        const savedVideo = yield newVideo.save();
        res.status(201).json(savedVideo);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.createVideo = createVideo;
// Get all videos with filtering, pagination, and sorting
const getVideos = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page = 1, limit = 10, status, category, search, isVR, isAR, sortBy, sortOrder } = req.query;
        const query = {};
        if (status) {
            query.status = status;
        }
        if (category) {
            query.category = category;
        }
        if (search) {
            query.$or = [
                { title: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } },
            ];
        }
        if (isVR !== undefined) {
            query.isVR = isVR === 'true';
        }
        if (isAR !== undefined) {
            query.isAR = isAR === 'true';
        }
        const sort = {};
        if (sortBy) {
            sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
        }
        else {
            sort.createdAt = -1; // Default sort by creation date descending
        }
        const options = {
            skip: (Number(page) - 1) * Number(limit),
            limit: Number(limit),
            sort,
        };
        const videos = yield Video_1.default.find(query, null, options);
        const total = yield Video_1.default.countDocuments(query);
        res.status(200).json({
            data: videos,
            pagination: {
                total,
                page: Number(page),
                limit: Number(limit),
                pages: Math.ceil(total / Number(limit)),
            },
        });
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.getVideos = getVideos;
// Get a single video by ID
const getVideoById = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const video = yield Video_1.default.findById(req.params.id);
        if (!video) {
            return res.status(404).json({ message: 'Video not found' });
        }
        res.status(200).json(video);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.getVideoById = getVideoById;
// Update a video by ID
const updateVideo = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const updatedVideo = yield Video_1.default.findByIdAndUpdate(req.params.id, req.body, { new: true });
        if (!updatedVideo) {
            return res.status(404).json({ message: 'Video not found' });
        }
        res.status(200).json(updatedVideo);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.updateVideo = updateVideo;
// Delete a video by ID
const deleteVideo = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const deletedVideo = yield Video_1.default.findByIdAndDelete(req.params.id);
        if (!deletedVideo) {
            return res.status(404).json({ message: 'Video not found' });
        }
        res.status(200).json({ message: 'Video deleted' });
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.deleteVideo = deleteVideo;
// Update video status by ID
const updateVideoStatus = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { status } = req.body;
        const updatedVideo = yield Video_1.default.findByIdAndUpdate(req.params.id, { status }, { new: true });
        if (!updatedVideo) {
            return res.status(404).json({ message: 'Video not found' });
        }
        res.status(200).json(updatedVideo);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.updateVideoStatus = updateVideoStatus;
