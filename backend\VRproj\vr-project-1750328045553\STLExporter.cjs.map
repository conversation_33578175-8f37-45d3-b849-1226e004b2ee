{"version": 3, "file": "STLExporter.cjs", "sources": ["../../src/exporters/STLExporter.ts"], "sourcesContent": ["import {\n  BufferAttribute,\n  BufferGeometry,\n  InterleavedBufferAttribute,\n  Mesh,\n  Object3D,\n  SkinnedMesh,\n  Vector3,\n} from 'three'\n\nexport interface STLExporterOptionsBinary {\n  binary: true\n}\n\nexport interface STLExporterOptionsString {\n  binary?: false\n}\n\nexport interface STLExporterOptions {\n  binary?: boolean\n}\n\nconst isMesh = (object: unknown): object is Mesh => (object as any).isMesh\n\nexport class STLExporter {\n  private binary = false\n\n  private output: string | DataView = ''\n  private offset: number = 80 // skip header\n\n  private objects: { object3d: Object3D; geometry: BufferGeometry }[] = []\n  private triangles: number = 0\n\n  private vA = new Vector3()\n  private vB = new Vector3()\n  private vC = new Vector3()\n  private cb = new Vector3()\n  private ab = new Vector3()\n  private normal = new Vector3()\n\n  parse(scene: Object3D, options: STLExporterOptionsBinary): DataView\n  parse(scene: Object3D, options?: STLExporterOptionsString): string\n  parse(scene: Object3D, options?: STLExporterOptions): string | DataView {\n    this.binary = options?.binary !== undefined ? options?.binary : false\n\n    scene.traverse((object: Object3D) => {\n      if (isMesh(object)) {\n        const geometry = object.geometry\n\n        if (!geometry.isBufferGeometry) {\n          throw new Error('THREE.STLExporter: Geometry is not of type THREE.BufferGeometry.')\n        }\n\n        const index = geometry.index\n        const positionAttribute = geometry.getAttribute('position') || null\n        if (!positionAttribute) return\n\n        this.triangles += index !== null ? index.count / 3 : positionAttribute.count / 3\n\n        this.objects.push({\n          object3d: object,\n          geometry: geometry,\n        })\n      }\n    })\n\n    if (this.binary) {\n      const bufferLength = this.triangles * 2 + this.triangles * 3 * 4 * 4 + 80 + 4\n      const arrayBuffer = new ArrayBuffer(bufferLength)\n      this.output = new DataView(arrayBuffer)\n      this.output.setUint32(this.offset, this.triangles, true)\n      this.offset += 4\n    } else {\n      this.output = ''\n      this.output += 'solid exported\\n'\n    }\n\n    for (let i = 0, il = this.objects.length; i < il; i++) {\n      const object = this.objects[i].object3d\n      const geometry = this.objects[i].geometry\n\n      const index = geometry.index\n      const positionAttribute = geometry.getAttribute('position')\n\n      if (index !== null) {\n        // indexed geometry\n        for (let j = 0; j < index.count; j += 3) {\n          const a = index.getX(j + 0)\n          const b = index.getX(j + 1)\n          const c = index.getX(j + 2)\n\n          this.writeFace(a, b, c, positionAttribute, object as SkinnedMesh)\n        }\n      } else {\n        // non-indexed geometry\n        for (let j = 0; j < positionAttribute.count; j += 3) {\n          const a = j + 0\n          const b = j + 1\n          const c = j + 2\n\n          this.writeFace(a, b, c, positionAttribute, object as SkinnedMesh)\n        }\n      }\n    }\n\n    if (!this.binary) {\n      this.output += 'endsolid exported\\n'\n    }\n\n    return this.output\n  }\n\n  private writeFace(\n    a: number,\n    b: number,\n    c: number,\n    positionAttribute: BufferAttribute | InterleavedBufferAttribute,\n    object: SkinnedMesh,\n  ): void {\n    this.vA.fromBufferAttribute(positionAttribute, a)\n    this.vB.fromBufferAttribute(positionAttribute, b)\n    this.vC.fromBufferAttribute(positionAttribute, c)\n\n    if (object.isSkinnedMesh) {\n      const mesh = object as Omit<SkinnedMesh, 'boneTransform' | 'applyBoneTransform'> &\n        (\n          | {\n              boneTransform(index: number, vector: Vector3): Vector3\n            }\n          | {\n              applyBoneTransform(index: number, vector: Vector3): Vector3\n            }\n        )\n\n      // r151 https://github.com/mrdoob/three.js/pull/25586\n      if ('applyBoneTransform' in mesh) {\n        mesh.applyBoneTransform(a, this.vA)\n        mesh.applyBoneTransform(b, this.vB)\n        mesh.applyBoneTransform(c, this.vC)\n      } else {\n        mesh.boneTransform(a, this.vA)\n        mesh.boneTransform(b, this.vB)\n        mesh.boneTransform(c, this.vC)\n      }\n    }\n\n    this.vA.applyMatrix4(object.matrixWorld)\n    this.vB.applyMatrix4(object.matrixWorld)\n    this.vC.applyMatrix4(object.matrixWorld)\n\n    this.writeNormal(this.vA, this.vB, this.vC)\n\n    this.writeVertex(this.vA)\n    this.writeVertex(this.vB)\n    this.writeVertex(this.vC)\n\n    if (this.binary && this.output instanceof DataView) {\n      this.output.setUint16(this.offset, 0, true)\n      this.offset += 2\n    } else {\n      this.output += '\\t\\tendloop\\n'\n      this.output += '\\tendfacet\\n'\n    }\n  }\n\n  private writeNormal(vA: Vector3, vB: Vector3, vC: Vector3): void {\n    this.cb.subVectors(vC, vB)\n    this.ab.subVectors(vA, vB)\n    this.cb.cross(this.ab).normalize()\n\n    this.normal.copy(this.cb).normalize()\n\n    if (this.binary && this.output instanceof DataView) {\n      this.output.setFloat32(this.offset, this.normal.x, true)\n      this.offset += 4\n      this.output.setFloat32(this.offset, this.normal.y, true)\n      this.offset += 4\n      this.output.setFloat32(this.offset, this.normal.z, true)\n      this.offset += 4\n    } else {\n      this.output += `\\tfacet normal ${this.normal.x} ${this.normal.y} ${this.normal.z}\\n`\n      this.output += '\\t\\touter loop\\n'\n    }\n  }\n\n  private writeVertex(vertex: Vector3): void {\n    if (this.binary && this.output instanceof DataView) {\n      this.output.setFloat32(this.offset, vertex.x, true)\n      this.offset += 4\n      this.output.setFloat32(this.offset, vertex.y, true)\n      this.offset += 4\n      this.output.setFloat32(this.offset, vertex.z, true)\n      this.offset += 4\n    } else {\n      this.output += `\\t\\t\\tvertex ${vertex.x} ${vertex.y} ${vertex.z}\\n`\n    }\n  }\n}\n"], "names": ["Vector3"], "mappings": ";;;;;;;;;AAsBA,MAAM,SAAS,CAAC,WAAqC,OAAe;AAE7D,MAAM,YAAY;AAAA,EAAlB;AACG,kCAAS;AAET,kCAA4B;AAC5B,kCAAiB;AAEjB;AAAA,mCAA8D,CAAA;AAC9D,qCAAoB;AAEpB,8BAAK,IAAIA,MAAAA;AACT,8BAAK,IAAIA,MAAAA;AACT,8BAAK,IAAIA,MAAAA;AACT,8BAAK,IAAIA,MAAAA;AACT,8BAAK,IAAIA,MAAAA;AACT,kCAAS,IAAIA,MAAAA;;EAIrB,MAAM,OAAiB,SAAiD;AACtE,SAAK,UAAS,mCAAS,YAAW,SAAY,mCAAS,SAAS;AAE1D,UAAA,SAAS,CAAC,WAAqB;AAC/B,UAAA,OAAO,MAAM,GAAG;AAClB,cAAM,WAAW,OAAO;AAEpB,YAAA,CAAC,SAAS,kBAAkB;AACxB,gBAAA,IAAI,MAAM,kEAAkE;AAAA,QACpF;AAEA,cAAM,QAAQ,SAAS;AACvB,cAAM,oBAAoB,SAAS,aAAa,UAAU,KAAK;AAC/D,YAAI,CAAC;AAAmB;AAExB,aAAK,aAAa,UAAU,OAAO,MAAM,QAAQ,IAAI,kBAAkB,QAAQ;AAE/E,aAAK,QAAQ,KAAK;AAAA,UAChB,UAAU;AAAA,UACV;AAAA,QAAA,CACD;AAAA,MACH;AAAA,IAAA,CACD;AAED,QAAI,KAAK,QAAQ;AACT,YAAA,eAAe,KAAK,YAAY,IAAI,KAAK,YAAY,IAAI,IAAI,IAAI,KAAK;AACtE,YAAA,cAAc,IAAI,YAAY,YAAY;AAC3C,WAAA,SAAS,IAAI,SAAS,WAAW;AACtC,WAAK,OAAO,UAAU,KAAK,QAAQ,KAAK,WAAW,IAAI;AACvD,WAAK,UAAU;AAAA,IAAA,OACV;AACL,WAAK,SAAS;AACd,WAAK,UAAU;AAAA,IACjB;AAES,aAAA,IAAI,GAAG,KAAK,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK;AACrD,YAAM,SAAS,KAAK,QAAQ,CAAC,EAAE;AAC/B,YAAM,WAAW,KAAK,QAAQ,CAAC,EAAE;AAEjC,YAAM,QAAQ,SAAS;AACjB,YAAA,oBAAoB,SAAS,aAAa,UAAU;AAE1D,UAAI,UAAU,MAAM;AAElB,iBAAS,IAAI,GAAG,IAAI,MAAM,OAAO,KAAK,GAAG;AACvC,gBAAM,IAAI,MAAM,KAAK,IAAI,CAAC;AAC1B,gBAAM,IAAI,MAAM,KAAK,IAAI,CAAC;AAC1B,gBAAM,IAAI,MAAM,KAAK,IAAI,CAAC;AAE1B,eAAK,UAAU,GAAG,GAAG,GAAG,mBAAmB,MAAqB;AAAA,QAClE;AAAA,MAAA,OACK;AAEL,iBAAS,IAAI,GAAG,IAAI,kBAAkB,OAAO,KAAK,GAAG;AACnD,gBAAM,IAAI,IAAI;AACd,gBAAM,IAAI,IAAI;AACd,gBAAM,IAAI,IAAI;AAEd,eAAK,UAAU,GAAG,GAAG,GAAG,mBAAmB,MAAqB;AAAA,QAClE;AAAA,MACF;AAAA,IACF;AAEI,QAAA,CAAC,KAAK,QAAQ;AAChB,WAAK,UAAU;AAAA,IACjB;AAEA,WAAO,KAAK;AAAA,EACd;AAAA,EAEQ,UACN,GACA,GACA,GACA,mBACA,QACM;AACD,SAAA,GAAG,oBAAoB,mBAAmB,CAAC;AAC3C,SAAA,GAAG,oBAAoB,mBAAmB,CAAC;AAC3C,SAAA,GAAG,oBAAoB,mBAAmB,CAAC;AAEhD,QAAI,OAAO,eAAe;AACxB,YAAM,OAAO;AAWb,UAAI,wBAAwB,MAAM;AAC3B,aAAA,mBAAmB,GAAG,KAAK,EAAE;AAC7B,aAAA,mBAAmB,GAAG,KAAK,EAAE;AAC7B,aAAA,mBAAmB,GAAG,KAAK,EAAE;AAAA,MAAA,OAC7B;AACA,aAAA,cAAc,GAAG,KAAK,EAAE;AACxB,aAAA,cAAc,GAAG,KAAK,EAAE;AACxB,aAAA,cAAc,GAAG,KAAK,EAAE;AAAA,MAC/B;AAAA,IACF;AAEK,SAAA,GAAG,aAAa,OAAO,WAAW;AAClC,SAAA,GAAG,aAAa,OAAO,WAAW;AAClC,SAAA,GAAG,aAAa,OAAO,WAAW;AAEvC,SAAK,YAAY,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE;AAErC,SAAA,YAAY,KAAK,EAAE;AACnB,SAAA,YAAY,KAAK,EAAE;AACnB,SAAA,YAAY,KAAK,EAAE;AAExB,QAAI,KAAK,UAAU,KAAK,kBAAkB,UAAU;AAClD,WAAK,OAAO,UAAU,KAAK,QAAQ,GAAG,IAAI;AAC1C,WAAK,UAAU;AAAA,IAAA,OACV;AACL,WAAK,UAAU;AACf,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA,EAEQ,YAAY,IAAa,IAAa,IAAmB;AAC1D,SAAA,GAAG,WAAW,IAAI,EAAE;AACpB,SAAA,GAAG,WAAW,IAAI,EAAE;AACzB,SAAK,GAAG,MAAM,KAAK,EAAE,EAAE;AAEvB,SAAK,OAAO,KAAK,KAAK,EAAE,EAAE;AAE1B,QAAI,KAAK,UAAU,KAAK,kBAAkB,UAAU;AAClD,WAAK,OAAO,WAAW,KAAK,QAAQ,KAAK,OAAO,GAAG,IAAI;AACvD,WAAK,UAAU;AACf,WAAK,OAAO,WAAW,KAAK,QAAQ,KAAK,OAAO,GAAG,IAAI;AACvD,WAAK,UAAU;AACf,WAAK,OAAO,WAAW,KAAK,QAAQ,KAAK,OAAO,GAAG,IAAI;AACvD,WAAK,UAAU;AAAA,IAAA,OACV;AACA,WAAA,UAAU,iBAAkB,KAAK,OAAO,KAAK,KAAK,OAAO,KAAK,KAAK,OAAO;AAAA;AAC/E,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA,EAEQ,YAAY,QAAuB;AACzC,QAAI,KAAK,UAAU,KAAK,kBAAkB,UAAU;AAClD,WAAK,OAAO,WAAW,KAAK,QAAQ,OAAO,GAAG,IAAI;AAClD,WAAK,UAAU;AACf,WAAK,OAAO,WAAW,KAAK,QAAQ,OAAO,GAAG,IAAI;AAClD,WAAK,UAAU;AACf,WAAK,OAAO,WAAW,KAAK,QAAQ,OAAO,GAAG,IAAI;AAClD,WAAK,UAAU;AAAA,IAAA,OACV;AACL,WAAK,UAAU,aAAgB,OAAO,KAAK,OAAO,KAAK,OAAO;AAAA;AAAA,IAChE;AAAA,EACF;AACF;;"}