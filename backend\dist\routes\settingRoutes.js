"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const settingController_1 = require("../controllers/settingController");
const router = express_1.default.Router();
router.get('/:section?', settingController_1.getSettings); // Optional section parameter
router.put('/:section', settingController_1.updateSettings);
router.post('/:section/reset', settingController_1.resetSettingsToDefault);
router.get('/export/:section?', settingController_1.exportSettings); // Optional section parameter for export
router.post('/import', settingController_1.importSettings);
exports.default = router;
