<!-- markdownlint-disable heading-increment no-duplicate-heading no-inline-html -->
<!-- spellchecker:ignore () CICD Deno EditorConfig chglog deps discoverability gitattributes maint markdownlint rivy typeof -->

# CHANGELOG <br/> [xdg-app-paths](https://github.com/rivy/js.xdg-app-paths)

---

## [v4.4.0](https://github.com/rivy/js.xdg-app-paths/compare/v4.3.0...v4.4.0) <small>(2020-12-15)</small>

#### Changes

* change ~ normalize all paths and strip any trailing path separators &ac; [`943a0ac`](https://github.com/rivy/js.xdg-app-paths/commit/943a0ac2ba431f1b0d5260e3123e42d5840df556)

#### Fixes

* fix examples (restore Node-v6 compatibility) &ac; [`d934170`](https://github.com/rivy/js.xdg-app-paths/commit/d934170ee21c8a2fefcabda9a2d9cc340fa26923)

#### Documentation

* docs ~ update README (mention path normalization) &ac; [`b9d1b55`](https://github.com/rivy/js.xdg-app-paths/commit/b9d1b55a103e8e69d81666f4ba1aee1106028f91)
* docs ~ refactor example &ac; [`214873f`](https://github.com/rivy/js.xdg-app-paths/commit/214873f970a0118c9fd30c315987118c62b4120e)
* docs ~ polish README &ac; [`9d70297`](https://github.com/rivy/js.xdg-app-paths/commit/9d70297981250102d404d3ea7858e857a8116bba)
* docs ~ simplify example (removing extra developer deps) &ac; [`2be36d3`](https://github.com/rivy/js.xdg-app-paths/commit/2be36d3c4bb41731f074b1db9365abd4085b0009)

#### Maintenance

* maint/**build**: fix package keywords &ac; [`3c06c4a`](https://github.com/rivy/js.xdg-app-paths/commit/3c06c4a60289fa1e426210a9bfbec07f0af31253)
* maint/**build**: add CHANGELOG.mkd to distribution file list &ac; [`888775c`](https://github.com/rivy/js.xdg-app-paths/commit/888775cb3bced9207b9535e8e9c1dfc42d05e24b)

#### Refactoring

* refactor ~ use common normalizing function for returned paths &ac; [`c56de7a`](https://github.com/rivy/js.xdg-app-paths/commit/c56de7a45164a1b6543245f40ae4dfd13dfe0fe5)

---

## [v4.3.0](https://github.com/rivy/js.xdg-app-paths/compare/v4.2.0...v4.3.0) <small>(2020-12-13)</small>

#### Documentation

* docs ~ polish README &ac; [`ac1e371`](https://github.com/rivy/js.xdg-app-paths/commit/ac1e371e58b5fb6538134e3726d73e2bb7fc9478)
* docs ~ add spell-checker exceptions &ac; [`dce522c`](https://github.com/rivy/js.xdg-app-paths/commit/dce522cd5df9307af582eb87a8d23879cae0a471)
* docs ~ README fix and polish &ac; [`43bc088`](https://github.com/rivy/js.xdg-app-paths/commit/43bc088e544a9eed5ae7dea982b64dc55c60a0db)
* docs ~ `git-changelog > CHANGELOG.mkd` &ac; [`19520e5`](https://github.com/rivy/js.xdg-app-paths/commit/19520e50c34fed1ff209e1a0de56f295f3a58702)

#### Maintenance

* maint/**CICD**: add GitHub Actions (GHA) CI &ac; [`4202298`](https://github.com/rivy/js.xdg-app-paths/commit/4202298cd17f31816415965b26a7dc5a9d23d4c3)
* maint/**build**: update eslintrc (and convert to JS file) &ac; [`06e0167`](https://github.com/rivy/js.xdg-app-paths/commit/06e01677f365e38b55dfab5cec11720add36f462)
* maint/**build**: add explanation for NPMrc `package-lock=false` &ac; [`ada889c`](https://github.com/rivy/js.xdg-app-paths/commit/ada889cdcb823535ae7c37567d5c5af7988deea2)
* maint/**build**: add LICENSE + README to set of distributed files &ac; [`e020a4a`](https://github.com/rivy/js.xdg-app-paths/commit/e020a4a3a61d53be3f7fda3cc8a09ca3494eab88)
* maint/**build**: remove ESlint config forcing ESM-type unit.test.js &ac; [`69b155b`](https://github.com/rivy/js.xdg-app-paths/commit/69b155b42d33183451940ed0101410b8790f9d10)
* maint/**build**: fix `tsd` complaint (types specification missing from package "files" list) &ac; [`fd87126`](https://github.com/rivy/js.xdg-app-paths/commit/fd871263bf9435d6024b348f3f02561c9180ebfc)
* maint/**build**: revise and polish npm scripts (include deps) &ac; [`2b23875`](https://github.com/rivy/js.xdg-app-paths/commit/2b238755789a9a85c0d1119617dd63e499f4090b)
* maint/**build**: refactor/reorganize package.json &ac; [`2d6f509`](https://github.com/rivy/js.xdg-app-paths/commit/2d6f509fae07dc4f8554958dfc1402147904f605)
* maint/**build**: refine package 'engine' value (best legibility/specificity) &ac; [`be185d0`](https://github.com/rivy/js.xdg-app-paths/commit/be185d0c8170afc7498d81c3ea8b8e9fdaabc46c)
* maint/**dev**: npm script polish &ac; [`a7212d5`](https://github.com/rivy/js.xdg-app-paths/commit/a7212d57999935fb4a83a8d3c3835777b1ecf3cd)
* maint/**dev**: add Prettier ignore file (to simplify automation) &ac; [`fb7b8c6`](https://github.com/rivy/js.xdg-app-paths/commit/fb7b8c68756f63a6eb2f0a1451e0eec08479c386)
* maint/**dev**: add Prettier configuration &ac; [`c5819ed`](https://github.com/rivy/js.xdg-app-paths/commit/c5819ed99252549419796a9234f34fe488e2fbef)
* maint/**dev**: add `Prettier` &ac; [`94ffae4`](https://github.com/rivy/js.xdg-app-paths/commit/94ffae4e3021c60a69b14cfc4445c7284421aadd)
* maint/**dev**: add notation about `ava` and `nyc` version restrictions with NodeJS-v6 &ac; [`71ed02a`](https://github.com/rivy/js.xdg-app-paths/commit/71ed02adcaac3ea2c76c6f02d90e924650f46c3c)
* maint/**dev**: add .history (for VSCode plugin) to ESlint config ignore list &ac; [`039acdb`](https://github.com/rivy/js.xdg-app-paths/commit/039acdbbd48a8ee59f5b70c06d531f8fc8d75970)
* maint/**dev**: add `ESLint` (with plugins) &ac; [`3f82e96`](https://github.com/rivy/js.xdg-app-paths/commit/3f82e96d3778bc18a992213d4179207909b310b4)
* maint/**dev**: remove `XO` &ac; [`b4db615`](https://github.com/rivy/js.xdg-app-paths/commit/b4db615ecb7006541028e92435024be14b7fe9cb)
* maint/**dev**: add .history (for VSCode plugin) to Prettier ignore list &ac; [`e4eff74`](https://github.com/rivy/js.xdg-app-paths/commit/e4eff74cd805002a5b6a27bf57191a3c9d73e286)
* maint/**dev**: add VSCode settings (ENABLE auto-format on save) &ac; [`74d2ef5`](https://github.com/rivy/js.xdg-app-paths/commit/74d2ef5da26bd3548c83cf7ee3e684ea32f02c6f)
* maint/**dev**: polish package.json format &ac; [`52d4f0c`](https://github.com/rivy/js.xdg-app-paths/commit/52d4f0cdab6d819cfb1f3cd4bd50f82bb272573a)
* maint/**dev**: add pre-test and CHANGELOG update to `npm version ...` &ac; [`2553998`](https://github.com/rivy/js.xdg-app-paths/commit/2553998e33ac4feaab6599d09939966acbec6a87)
* maint/**dev**: `npm version ...` CHANGELOG update only if `git-changelog` available &ac; [`c6cddab`](https://github.com/rivy/js.xdg-app-paths/commit/c6cddab770e94a02bb08107a5aa93436c32f8f5e)
* maint/**dev**: revise gitignore files to include build artifacts &ac; [`95db0d4`](https://github.com/rivy/js.xdg-app-paths/commit/95db0d4ed3f0ab8395f9cf14693302ebc854229c)
* maint/**dev**: revise gitattributes &ac; [`dd6647a`](https://github.com/rivy/js.xdg-app-paths/commit/dd6647aeac291a37a92afc8abb5c00dd9294c46d)
* maint/**dev**: update EditorConfig (include more file types and commentary) &ac; [`3ae7869`](https://github.com/rivy/js.xdg-app-paths/commit/3ae7869f9958a5fe496f14926e32bb050c187de6)
* maint/**dev**: add .history (for VSCode plugin) to .gitignore &ac; [`0251a8b`](https://github.com/rivy/js.xdg-app-paths/commit/0251a8b4c9023ddd6c41f9456dadedb059183377)
* maint/**dev**: reconfigure for `git-changelog` (from GH:rivy-go) &ac; [`4f96b41`](https://github.com/rivy/js.xdg-app-paths/commit/4f96b4125df64559bf001f28b09b14a5be4cb2cf)

#### Refactoring

* refactor ~ change LICENSE and README file names to UPPERCASE for improved discoverability &ac; [`68904bb`](https://github.com/rivy/js.xdg-app-paths/commit/68904bb8e971600ae72197933cb7371c5e1c2d81)
* refactor ~ partition code into 'src' and 'test' directories &ac; [`1f93e19`](https://github.com/rivy/js.xdg-app-paths/commit/1f93e19d486fc53ccb184fec6751ff7aaeba43c0)
* refactor/**polish**: fix ESLint complaint ('no-unused-disable') &ac; [`d5b9386`](https://github.com/rivy/js.xdg-app-paths/commit/d5b9386086ee4047b128958ee942d06a021283e9)
* refactor/**polish**: `prettier` reformat &ac; [`5680208`](https://github.com/rivy/js.xdg-app-paths/commit/5680208a8d1de76337c11c4eb974c42118a0da51)

---

## [v4.2.0](https://github.com/rivy/js.xdg-app-paths/compare/v4.1.0...v4.2.0) <small>(2019-10-17)</small>

#### Documentation

* docs ~ polish keywords, comments, and README documentation &ac; [`348d5a7`](https://github.com/rivy/js.xdg-app-paths/commit/348d5a7113957634bfb12f2e02dd6f375e50d77d)

#### Maintenance

* maint/**build**: add local coverage and prepublish test scripts &ac; [`7c16178`](https://github.com/rivy/js.xdg-app-paths/commit/7c1617869abfe74b07ad56789e1ea359b57af0b1)

---

## [v4.1.0](https://github.com/rivy/js.xdg-app-paths/compare/v4.0.1...v4.1.0) <small>(2019-10-04)</small>

#### Documentation

* docs ~ improve README badges &ac; [`a354862`](https://github.com/rivy/js.xdg-app-paths/commit/a3548620a0856d55565d388388fd349bdb47c148)

#### Maintenance

* maint/**build**: fix/update dev dependencies &ac; [`de871cb`](https://github.com/rivy/js.xdg-app-paths/commit/de871cb5456609c199a8d3fcd2f94a52f25ead76)

---

## [v4.0.1](https://github.com/rivy/js.xdg-app-paths/compare/v4.0.0...v4.0.1) <small>(2019-10-04)</small>

#### Documentation

* docs ~ fix CHANGELOG title and URLs &ac; [`5458236`](https://github.com/rivy/js.xdg-app-paths/commit/54582364a6230ad8c667208dc99766e98985a78d)

#### Maintenance

* maint/**build**: fix CHANGELOG generator configuration &ac; [`acb315d`](https://github.com/rivy/js.xdg-app-paths/commit/acb315d45e55942b67b2c89d11aef5418d2050c1)

---

## [v4.0.0](https://github.com/rivy/js.xdg-app-paths/compare/v3.0.2...v4.0.0) <small>(2019-10-02)</small>

#### Changes

* change/**API!**: change module focus to only OS-associated paths, using a method-based API &ac; [`a18b948`](https://github.com/rivy/js.xdg-app-paths/commit/a18b94877d5b8f8030805124be2e5af3b4423332)

#### Documentation

* docs ~ add example script &ac; [`43ea4ca`](https://github.com/rivy/js.xdg-app-paths/commit/43ea4cabc5ba9ee04e14d6fce34c8b78defd9063)
* docs ~ update README &ac; [`ea09e79`](https://github.com/rivy/js.xdg-app-paths/commit/ea09e7987e47bfbeb15a17508c9b59f31c5e3492)
* docs ~ change package description and keywords &ac; [`f993fb9`](https://github.com/rivy/js.xdg-app-paths/commit/f993fb90f7d3bf3b49d2ffbb67319c3c08e416d7)
* docs ~ add CHANGELOG &ac; [`d8b964c`](https://github.com/rivy/js.xdg-app-paths/commit/d8b964c6c09477ae200a7d2e81e164d82f31e63d)

#### Maintenance

* maint ~ improve linting support for IDEs (using 'eslint') &ac; [`c64ce55`](https://github.com/rivy/js.xdg-app-paths/commit/c64ce55abe3c0e9650b14567df815fecc0c9a380)
* maint ~ update EditorConfig configuration for better TAB display &ac; [`15ac24f`](https://github.com/rivy/js.xdg-app-paths/commit/15ac24fda5b3cb743bb91d2dbf2c83686c830653)
* maint/**CI**: add AppVeyor CI configuration &ac; [`4f3b933`](https://github.com/rivy/js.xdg-app-paths/commit/4f3b933bf13e8f43f0c75ef46ea5b098281eb5af)
* maint/**CI**: add Travis CI configuration &ac; [`0c5dfb7`](https://github.com/rivy/js.xdg-app-paths/commit/0c5dfb7544014e4ae458251b0930a309bb440e5f)
* maint/**CI**: add code coverage support and reporting &ac; [`27f5df4`](https://github.com/rivy/js.xdg-app-paths/commit/27f5df44ea4df0b715e40d07479c1448d8065969)
* maint/**build**: add tests for correct spelling &ac; [`ab911b1`](https://github.com/rivy/js.xdg-app-paths/commit/ab911b197a205e22b706bf380cf59ba7690c566d)
* maint/**build**: refactor testing commands &ac; [`e962f1d`](https://github.com/rivy/js.xdg-app-paths/commit/e962f1d96bbe460237e2048e04bb368733852dd5)
* maint/**build**: add CHANGELOG (`git-chglog`) configuration &ac; [`e5bb024`](https://github.com/rivy/js.xdg-app-paths/commit/e5bb024c35bd73918b5469d1b393f38dfd7b0b7c)
* maint/**build**: add 'lint' run-script command &ac; [`6d279b8`](https://github.com/rivy/js.xdg-app-paths/commit/6d279b8f9e067c1efe14648f96861f2d417b2016)
* maint/**build**: refactor run-scripts to use `npm-run-all` &ac; [`2ca1ddf`](https://github.com/rivy/js.xdg-app-paths/commit/2ca1ddf25073e66d7e5c708d6e4d7e34496511de)
* maint/**build**: expand file set for spell checking &ac; [`334683b`](https://github.com/rivy/js.xdg-app-paths/commit/334683bc7ae5e60a36d3e4f26c40c3850f5e14ad)
* maint/**build**: gate spell-check to NodeJS >= v8 &ac; [`f737d15`](https://github.com/rivy/js.xdg-app-paths/commit/f737d15b399e9362fd6f18fbb592427f6ccdc0b3)
* maint/**build**: refactor run-scripts &ac; [`c453ad7`](https://github.com/rivy/js.xdg-app-paths/commit/c453ad7b5567e7329028bfb0e18e4d9227e93a6f)

---

## [v3.0.2](https://github.com/rivy/js.xdg-app-paths/compare/v3.0.1...v3.0.2) <small>(2019-06-29)</small>

#### Documentation

* docs ~ polish and add XDG references &ac; [`1696b46`](https://github.com/rivy/js.xdg-app-paths/commit/1696b462d336a047b5041685b4fade914eeebd6a)

---

## [v3.0.1](https://github.com/rivy/js.xdg-app-paths/compare/v2.2.0...v3.0.1) <small>(2019-06-29)</small>

#### Changes

* add improved XDG support (CONFIG_DIRS and DATA_DIRS) &ac; [`c6a250b`](https://github.com/rivy/js.xdg-app-paths/commit/c6a250bdcb899b83179b2414b9f5607fbf0e29bc)
* add cross-platform XDG support (plus comment polish) &ac; [`4d87f8d`](https://github.com/rivy/js.xdg-app-paths/commit/4d87f8d06d39a3c87d8dc49b5b00a720fbcf75e7)
* Add note about the user needing to create the actual directories &ac; [`294db55`](https://github.com/rivy/js.xdg-app-paths/commit/294db5514d82a39424b4325d8e59879241174365)

#### Fixes

* fix ~ windows 'data' should roam with user &ac; [`a0b2f75`](https://github.com/rivy/js.xdg-app-paths/commit/a0b2f75b9a6ff09a74b2e49899863e844257c885)

#### Maintenance

* maint ~ comment polish &ac; [`dab0324`](https://github.com/rivy/js.xdg-app-paths/commit/dab0324f2302eb87a7631044c4a997b935583dcd)
* maint ~ add README linting and corrections &ac; [`aaf1e6c`](https://github.com/rivy/js.xdg-app-paths/commit/aaf1e6ca0b7407a095adbf1877b6fd5c85061eac)
* maint ~ add spell-checker exceptions &ac; [`bf9d759`](https://github.com/rivy/js.xdg-app-paths/commit/bf9d7595a99f9eae2c8db1e05d504cc912b5baaf)

#### Refactoring

* refactor ~ reorganize properties &ac; [`f376e0c`](https://github.com/rivy/js.xdg-app-paths/commit/f376e0c142b303a1313710914490ff521b4b9dd7)

---

## [v2.2.0](https://github.com/rivy/js.xdg-app-paths/compare/v2.1.0...v2.2.0) <small>(2019-04-01)</small>

#### Refactoring

* Refactor TypeScript definition to CommonJS compatible export ([#12](https://github.com/rivy/js.xdg-app-paths/issues/12)) &ac; [`dacf4e9`](https://github.com/rivy/js.xdg-app-paths/commit/dacf4e91cf27b1dccf5f2341bb2bec766307de0f)

---

## [v2.1.0](https://github.com/rivy/js.xdg-app-paths/compare/v2.0.0...v2.1.0) <small>(2019-03-04)</small>

#### Changes

* Add TypeScript definition ([#11](https://github.com/rivy/js.xdg-app-paths/issues/11)) &ac; [`949cd22`](https://github.com/rivy/js.xdg-app-paths/commit/949cd224975f15bfeb1fd2d3a2e7ad284d4cbeab)

---

## [v2.0.0](https://github.com/rivy/js.xdg-app-paths/compare/v1.0.0...v2.0.0) <small>(2018-11-05)</small>

*No changelog for this release.*

---

## [v1.0.0](https://github.com/rivy/js.xdg-app-paths/compare/v0.3.1...v1.0.0) <small>(2017-01-10)</small>

#### Fixes

* Fix incorrect paths on Linux ([#6](https://github.com/rivy/js.xdg-app-paths/issues/6)) &ac; [`3a2ba84`](https://github.com/rivy/js.xdg-app-paths/commit/3a2ba84dc8be3103158225b4f0a3bd36ba9288b6)

---

## [v0.3.1](https://github.com/rivy/js.xdg-app-paths/compare/v0.3.0...v0.3.1) <small>(2016-10-18)</small>

*No changelog for this release.*

---

## [v0.3.0](https://github.com/rivy/js.xdg-app-paths/compare/v0.2.0...v0.3.0) <small>(2016-07-02)</small>

#### Fixes

* fix usage example &ac; [`88a5908`](https://github.com/rivy/js.xdg-app-paths/commit/88a5908a9409422fa21cab38a4965701f74281fe)

---

## [v0.2.0](https://github.com/rivy/js.xdg-app-paths/compare/v0.1.0...v0.2.0) <small>(2016-06-24)</small>

#### Changes

* add suffix to prevent possible conflict with native apps &ac; [`c2fda19`](https://github.com/rivy/js.xdg-app-paths/commit/c2fda19d629e56f308c8265506a1baf0c5c7e6dc)

---

## v0.1.0 <small>(2016-06-21)</small>

*No changelog for this release.*
