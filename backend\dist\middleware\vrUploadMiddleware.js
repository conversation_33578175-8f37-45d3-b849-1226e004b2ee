"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.organizeVRFiles = exports.uploadVRProject = exports.fixThreeJSImports = void 0;
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
// Helper function to fix Three.js import paths in HTML files
const fixThreeJSImports = (htmlFilePath) => {
    try {
        let htmlContent = fs_1.default.readFileSync(htmlFilePath, 'utf8');
        // Fix importmap to use CDN
        htmlContent = htmlContent.replace(/"three": "\.\.\/build\/three\.module\.js"/g, '"three": "https://unpkg.com/three@0.158.0/build/three.module.js"');
        htmlContent = htmlContent.replace(/"three\/addons\/": "\.\/jsm\/"/g, '"three/addons/": "https://unpkg.com/three@0.158.0/examples/jsm/"');
        // Fix DRACO loader path
        htmlContent = htmlContent.replace(/setDecoderPath\(\s*['"`]jsm\/libs\/draco\/gltf\/['"`]\s*\)/g, 'setDecoderPath( "https://unpkg.com/three@0.158.0/examples/jsm/libs/draco/gltf/" )');
        // Fix other common local paths
        htmlContent = htmlContent.replace(/setDecoderPath\(\s*['"`]\.\/jsm\/libs\/draco\/gltf\/['"`]\s*\)/g, 'setDecoderPath( "https://unpkg.com/three@0.158.0/examples/jsm/libs/draco/gltf/" )');
        // Fix relative imports that might be missing
        htmlContent = htmlContent.replace(/"three": "\.\.\/\.\.\/build\/three\.module\.js"/g, '"three": "https://unpkg.com/three@0.158.0/build/three.module.js"');
        fs_1.default.writeFileSync(htmlFilePath, htmlContent, 'utf8');
        console.log('Fixed Three.js imports in:', htmlFilePath);
        return true;
    }
    catch (error) {
        console.error('Error fixing Three.js imports:', error);
        return false;
    }
};
exports.fixThreeJSImports = fixThreeJSImports;
// Create VRproj directory if it doesn't exist
const vrProjectsDir = './VRproj/';
if (!fs_1.default.existsSync(vrProjectsDir)) {
    fs_1.default.mkdirSync(vrProjectsDir, { recursive: true });
}
// Set up storage engine for VR projects
const vrStorage = multer_1.default.diskStorage({
    destination: function (req, file, cb) {
        console.log('Multer destination called for file:', file.originalname);
        // Create a unique folder for each VR project
        const projectId = req.body.projectId || `vr-project-${Date.now()}`;
        console.log('Using project ID:', projectId);
        // Extract folder structure from file path
        let relativePath = '';
        if (file.originalname.includes('/')) {
            // Handle folder structure from webkitdirectory
            const pathParts = file.originalname.split('/');
            pathParts.pop(); // Remove filename
            relativePath = pathParts.join('/');
            console.log('Relative path:', relativePath);
        }
        const projectDir = path_1.default.join(vrProjectsDir, projectId, relativePath);
        console.log('Target directory:', projectDir);
        if (!fs_1.default.existsSync(projectDir)) {
            fs_1.default.mkdirSync(projectDir, { recursive: true });
            console.log('Created directory:', projectDir);
        }
        // Store project ID for later use
        req.body.projectId = projectId;
        cb(null, projectDir);
    },
    filename: function (req, file, cb) {
        // Extract just the filename (without folder path)
        const filename = file.originalname.includes('/')
            ? file.originalname.split('/').pop()
            : file.originalname;
        console.log('Multer filename called:', file.originalname, '->', filename);
        cb(null, filename || file.originalname);
    }
});
// Check file type for VR projects
function checkVRFileType(file, cb) {
    // Allowed file types for VR projects
    const allowedTypes = /html|js|css|json|gltf|glb|obj|mtl|fbx|dae|png|jpg|jpeg|gif|svg|mp3|wav|ogg|mp4|webm|txt|md/;
    // Check extension
    const extname = allowedTypes.test(path_1.default.extname(file.originalname).toLowerCase());
    // Check mime type (more permissive for VR assets)
    const allowedMimeTypes = [
        'text/html',
        'application/javascript',
        'text/javascript',
        'text/css',
        'application/json',
        'model/gltf+json',
        'model/gltf-binary',
        'application/octet-stream', // For binary files like .glb
        'image/png',
        'image/jpeg',
        'image/gif',
        'image/svg+xml',
        'audio/mpeg',
        'audio/wav',
        'audio/ogg',
        'video/mp4',
        'video/webm',
        'text/plain',
        'text/markdown'
    ];
    const mimetypeAllowed = allowedMimeTypes.includes(file.mimetype) || file.mimetype.startsWith('text/') || file.mimetype.startsWith('application/');
    if (extname || mimetypeAllowed) {
        return cb(null, true);
    }
    else {
        cb(`Error: File type not allowed. Allowed types: ${allowedTypes}`);
    }
}
// VR project upload configuration
const vrUpload = (0, multer_1.default)({
    storage: vrStorage,
    limits: {
        fileSize: 50 * 1024 * 1024, // 50MB limit per file
        files: 500, // Maximum 500 files per upload (increased for VR projects)
        fieldSize: 10 * 1024 * 1024, // 10MB field size limit
        fieldNameSize: 1000, // Increased field name size for long paths
        fields: 100 // Maximum number of non-file fields
    },
    fileFilter: function (req, file, cb) {
        checkVRFileType(file, cb);
    }
});
// Middleware to handle VR project uploads
exports.uploadVRProject = vrUpload.array('vrFiles', 500);
// Helper function to organize uploaded files with folder structure
const organizeVRFiles = (files, projectId) => {
    const organizedFiles = {
        mainFile: '',
        assets: [],
        htmlFiles: [],
        jsFiles: [],
        cssFiles: [],
        modelFiles: [],
        textureFiles: [],
        audioFiles: [],
        otherFiles: [],
        folderStructure: {}
    };
    files.forEach(file => {
        // Reconstruct the full relative path including folder structure
        let fullRelativePath = '';
        if (file.originalname.includes('/')) {
            // File came from folder upload - preserve structure
            const pathParts = file.originalname.split('/');
            const filename = pathParts.pop();
            const folderPath = pathParts.join('/');
            fullRelativePath = `VRproj/${projectId}/${folderPath}/${filename}`;
            // Build folder structure object
            let currentLevel = organizedFiles.folderStructure;
            pathParts.forEach(folder => {
                if (!currentLevel[folder]) {
                    currentLevel[folder] = {};
                }
                currentLevel = currentLevel[folder];
            });
        }
        else {
            // Single file upload
            fullRelativePath = `VRproj/${projectId}/${file.filename}`;
        }
        const ext = path_1.default.extname(file.filename).toLowerCase();
        const filename = file.filename;
        // Add to assets array
        organizedFiles.assets.push(fullRelativePath);
        // Categorize files
        if (ext === '.html') {
            organizedFiles.htmlFiles.push(fullRelativePath);
            // If it's index.html or main.html, set as main file
            if (filename.toLowerCase().includes('index') || filename.toLowerCase().includes('main')) {
                organizedFiles.mainFile = fullRelativePath;
            }
            // Fix Three.js imports in HTML files
            const actualFilePath = file.path;
            (0, exports.fixThreeJSImports)(actualFilePath);
        }
        else if (ext === '.js') {
            organizedFiles.jsFiles.push(fullRelativePath);
        }
        else if (ext === '.css') {
            organizedFiles.cssFiles.push(fullRelativePath);
        }
        else if (['.gltf', '.glb', '.obj', '.mtl', '.fbx', '.dae'].includes(ext)) {
            organizedFiles.modelFiles.push(fullRelativePath);
        }
        else if (['.png', '.jpg', '.jpeg', '.gif', '.svg'].includes(ext)) {
            organizedFiles.textureFiles.push(fullRelativePath);
        }
        else if (['.mp3', '.wav', '.ogg'].includes(ext)) {
            organizedFiles.audioFiles.push(fullRelativePath);
        }
        else {
            organizedFiles.otherFiles.push(fullRelativePath);
        }
    });
    // If no main file was found, use the first HTML file
    if (!organizedFiles.mainFile && organizedFiles.htmlFiles.length > 0) {
        organizedFiles.mainFile = organizedFiles.htmlFiles[0];
    }
    return organizedFiles;
};
exports.organizeVRFiles = organizeVRFiles;
exports.default = vrUpload;
