"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.autoSyncVRProjects = exports.vrSyncService = exports.VRSyncService = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const Experience_1 = __importDefault(require("../models/Experience"));
// Service to sync VR projects from filesystem to database
class VRSyncService {
    constructor() {
        this.vrProjectsDir = './VRproj/';
    }
    // Check for VR projects in filesystem that aren't in database
    syncVRProjects() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                console.log('Starting VR projects sync...');
                if (!fs_1.default.existsSync(this.vrProjectsDir)) {
                    console.log('VRproj directory does not exist');
                    return;
                }
                // Get all VR project folders
                const projectFolders = fs_1.default.readdirSync(this.vrProjectsDir)
                    .filter(item => {
                    const fullPath = path_1.default.join(this.vrProjectsDir, item);
                    return fs_1.default.statSync(fullPath).isDirectory() && item.startsWith('vr-project-');
                });
                console.log(`Found ${projectFolders.length} VR project folders`);
                // Get existing VR experiences from database
                const existingVRExperiences = yield Experience_1.default.find({ isVR: true });
                const existingFolderPaths = existingVRExperiences.map(exp => { var _a; return (_a = exp.vrProject) === null || _a === void 0 ? void 0 : _a.folderPath; });
                console.log(`Found ${existingVRExperiences.length} VR experiences in database`);
                // Find projects that exist in filesystem but not in database
                const missingProjects = projectFolders.filter(folder => !existingFolderPaths.includes(folder));
                console.log(`Found ${missingProjects.length} missing VR projects in database`);
                // Create database entries for missing projects
                for (const projectFolder of missingProjects) {
                    yield this.createDatabaseEntryForProject(projectFolder);
                }
                console.log('VR projects sync completed');
            }
            catch (error) {
                console.error('Error syncing VR projects:', error);
            }
        });
    }
    // Create a database entry for a VR project folder
    createDatabaseEntryForProject(projectFolder) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                console.log(`Creating database entry for project: ${projectFolder}`);
                const projectPath = path_1.default.join(this.vrProjectsDir, projectFolder);
                // Find the main HTML file
                let mainFile = 'index.html';
                const files = fs_1.default.readdirSync(projectPath);
                const htmlFiles = files.filter(file => file.endsWith('.html'));
                if (htmlFiles.length > 0) {
                    // Prefer index.html or main.html
                    const indexFile = htmlFiles.find(file => file.toLowerCase().includes('index') || file.toLowerCase().includes('main'));
                    mainFile = indexFile || htmlFiles[0];
                }
                // Extract timestamp from folder name for title
                const timestamp = projectFolder.replace('vr-project-', '');
                const date = new Date(parseInt(timestamp));
                const formattedDate = date.toLocaleString('fr-FR');
                // Create experience data
                const experienceData = {
                    title: `VR Experience - ${formattedDate}`,
                    description: `Projet VR uploadé le ${formattedDate}`,
                    isVR: true,
                    status: 'published', // Use valid enum value
                    isFeatured: false, // Use correct field name
                    vrProject: {
                        folderPath: projectFolder,
                        mainFile: mainFile,
                        description: 'Projet VR Three.js',
                        controls: 'Utilisez un casque VR ou souris/clavier pour naviguer',
                    }
                };
                const newExperience = new Experience_1.default(experienceData);
                const savedExperience = yield newExperience.save();
                console.log(`✅ Created database entry for ${projectFolder}:`, savedExperience._id);
                return savedExperience;
            }
            catch (error) {
                console.error(`❌ Error creating database entry for ${projectFolder}:`, error);
                return null;
            }
        });
    }
    // Get VR project info
    getVRProjectInfo(projectFolder) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const projectPath = path_1.default.join(this.vrProjectsDir, projectFolder);
                if (!fs_1.default.existsSync(projectPath)) {
                    return null;
                }
                const files = fs_1.default.readdirSync(projectPath);
                const htmlFiles = files.filter(file => file.endsWith('.html'));
                const jsFiles = files.filter(file => file.endsWith('.js'));
                const modelFiles = files.filter(file => file.endsWith('.glb') || file.endsWith('.gltf') || file.endsWith('.obj'));
                return {
                    folderPath: projectFolder,
                    totalFiles: files.length,
                    htmlFiles: htmlFiles.length,
                    jsFiles: jsFiles.length,
                    modelFiles: modelFiles.length,
                    mainFile: htmlFiles.find(file => file.toLowerCase().includes('index') || file.toLowerCase().includes('main')) || htmlFiles[0] || 'index.html'
                };
            }
            catch (error) {
                console.error(`Error getting VR project info for ${projectFolder}:`, error);
                return null;
            }
        });
    }
}
exports.VRSyncService = VRSyncService;
// Create singleton instance
exports.vrSyncService = new VRSyncService();
// Auto-sync function that can be called periodically
const autoSyncVRProjects = () => __awaiter(void 0, void 0, void 0, function* () {
    yield exports.vrSyncService.syncVRProjects();
});
exports.autoSyncVRProjects = autoSyncVRProjects;
