"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createVRExperience = exports.toggleExperienceFeatured = exports.updateExperienceStatus = exports.deleteExperience = exports.updateExperience = exports.getExperienceById = exports.getExperiences = exports.createExperience = void 0;
const Experience_1 = __importDefault(require("../models/Experience"));
const vrUploadMiddleware_1 = require("../middleware/vrUploadMiddleware");
// Create a new experience
const createExperience = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const newExperience = new Experience_1.default(req.body);
        const savedExperience = yield newExperience.save();
        res.status(201).json(savedExperience);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.createExperience = createExperience;
// Get all experiences with filtering, pagination, and sorting
const getExperiences = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page = 1, limit = 10, status, category, search, isFeatured, isVR, isAR, sortBy, sortOrder } = req.query;
        const query = {};
        if (status) {
            query.status = status;
        }
        if (category) {
            query.category = category;
        }
        if (search) {
            query.$or = [
                { title: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } },
            ];
        }
        if (isFeatured !== undefined) {
            query.isFeatured = isFeatured === 'true';
        }
        if (isVR !== undefined) {
            query.isVR = isVR === 'true';
        }
        if (isAR !== undefined) {
            query.isAR = isAR === 'true';
        }
        const sort = {};
        if (sortBy) {
            sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
        }
        else {
            sort.createdAt = -1; // Default sort by creation date descending
        }
        const options = {
            skip: (Number(page) - 1) * Number(limit),
            limit: Number(limit),
            sort,
        };
        const experiences = yield Experience_1.default.find(query, null, options);
        const total = yield Experience_1.default.countDocuments(query);
        res.status(200).json({
            data: experiences,
            pagination: {
                total,
                page: Number(page),
                limit: Number(limit),
                pages: Math.ceil(total / Number(limit)),
            },
        });
    }
    catch (error) {
        console.error('Error fetching experiences:', error); // Added specific logging
        res.status(500).json({ message: error.message });
    }
});
exports.getExperiences = getExperiences;
// Get a single experience by ID
const getExperienceById = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const experience = yield Experience_1.default.findById(req.params.id);
        if (!experience) {
            return res.status(404).json({ message: 'Experience not found' });
        }
        res.status(200).json(experience);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.getExperienceById = getExperienceById;
// Update an experience by ID
const updateExperience = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const updatedExperience = yield Experience_1.default.findByIdAndUpdate(req.params.id, req.body, { new: true });
        if (!updatedExperience) {
            return res.status(404).json({ message: 'Experience not found' });
        }
        res.status(200).json(updatedExperience);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.updateExperience = updateExperience;
// Delete an experience by ID
const deleteExperience = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const deletedExperience = yield Experience_1.default.findByIdAndDelete(req.params.id);
        if (!deletedExperience) {
            return res.status(404).json({ message: 'Experience not found' });
        }
        res.status(200).json({ message: 'Experience deleted' });
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.deleteExperience = deleteExperience;
// Update experience status by ID
const updateExperienceStatus = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { status } = req.body;
        const updatedExperience = yield Experience_1.default.findByIdAndUpdate(req.params.id, { status }, { new: true });
        if (!updatedExperience) {
            return res.status(404).json({ message: 'Experience not found' });
        }
        res.status(200).json(updatedExperience);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.updateExperienceStatus = updateExperienceStatus;
// Toggle experience featured status by ID
const toggleExperienceFeatured = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { isFeatured } = req.body;
        const updatedExperience = yield Experience_1.default.findByIdAndUpdate(req.params.id, { isFeatured }, { new: true });
        if (!updatedExperience) {
            return res.status(404).json({ message: 'Experience not found' });
        }
        res.status(200).json(updatedExperience);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.toggleExperienceFeatured = toggleExperienceFeatured;
// Create a VR experience with file uploads
const createVRExperience = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        console.log('=== VR Experience Upload Debug ===');
        console.log('VR UPLOAD ENDPOINT CALLED!');
        console.log('Request method:', req.method);
        console.log('Request URL:', req.url);
        console.log('Request headers:', req.headers);
        console.log('Request body keys:', Object.keys(req.body));
        console.log('Request files:', req.files ? `${req.files.length} files` : 'No files');
        if (req.files && req.files.length > 0) {
            console.log('First few files:');
            req.files.slice(0, 5).forEach((file, index) => {
                console.log(`File ${index}:`, {
                    originalname: file.originalname,
                    filename: file.filename,
                    path: file.path,
                    size: file.size,
                    mimetype: file.mimetype
                });
            });
        }
        console.log('Project ID:', req.body.projectId);
        console.log('Title:', req.body.title);
        console.log('Description:', req.body.description);
        const files = req.files;
        const { projectId, vrDescription, vrControls, title, description } = req.body;
        console.log('Parsed files:', files ? files.length : 0);
        console.log('Parsed projectId:', projectId);
        console.log('Parsed title:', title);
        console.log('Parsed description:', description);
        if (!files || files.length === 0) {
            console.log('ERROR: No files received');
            return res.status(400).json({ message: 'No VR project files uploaded' });
        }
        if (!title) {
            console.log('ERROR: No title provided');
            return res.status(400).json({ message: 'Title is required for VR experience' });
        }
        // Organize uploaded files
        const organizedFiles = (0, vrUploadMiddleware_1.organizeVRFiles)(files, projectId);
        // Find main HTML file name (just the filename, not full path)
        let mainFileName = 'index.html'; // default
        if (organizedFiles.mainFile) {
            mainFileName = organizedFiles.mainFile.split('/').pop() || 'index.html';
        }
        else if (organizedFiles.htmlFiles.length > 0) {
            mainFileName = organizedFiles.htmlFiles[0].split('/').pop() || 'index.html';
        }
        // Create experience data with VR project info (only store folder path)
        const experienceData = {
            title: title,
            description: description || 'VR Experience',
            isVR: true,
            status: req.body.status || 'published', // Use valid enum value
            isFeatured: req.body.isFeatured || false, // Use correct field name
            vrProject: {
                folderPath: projectId, // Just store the project folder name
                mainFile: mainFileName, // Store just the main file name
                description: vrDescription || '',
                controls: vrControls || 'Use VR headset or mouse/keyboard to navigate',
            }
        };
        console.log('Creating experience with data:', experienceData);
        // Save to database immediately, before any potential server restart
        const newExperience = new Experience_1.default(experienceData);
        console.log('Saving experience to database...');
        // Use a more robust save with error handling
        let savedExperience;
        try {
            savedExperience = yield newExperience.save();
            console.log('Experience saved successfully:', savedExperience._id);
        }
        catch (saveError) {
            console.error('Database save error:', saveError);
            // Try to save again with a simplified approach
            const simpleExperienceData = {
                title: title,
                description: description || 'VR Experience',
                isVR: true,
                status: 'published', // Use valid enum value
                isFeatured: false, // Use correct field name
                vrProject: {
                    folderPath: projectId,
                    mainFile: mainFileName,
                    description: vrDescription || '',
                    controls: vrControls || 'Use VR headset or mouse/keyboard to navigate',
                }
            };
            const retryExperience = new Experience_1.default(simpleExperienceData);
            savedExperience = yield retryExperience.save();
            console.log('Experience saved on retry:', savedExperience._id);
        }
        console.log('Sending success response...');
        res.status(201).json(Object.assign(Object.assign({}, savedExperience.toObject()), { message: `VR project uploaded successfully to VRproj/${projectId}`, filesUploaded: files.length, vrLaunchUrl: `/vr/${projectId}/${mainFileName}` }));
    }
    catch (error) {
        console.error('VR Experience creation error:', error);
        console.error('Error stack:', error.stack);
        // Try to provide more specific error messages
        if (error.name === 'ValidationError') {
            const validationErrors = Object.values(error.errors).map((err) => err.message);
            return res.status(400).json({
                message: 'Validation error',
                errors: validationErrors
            });
        }
        res.status(500).json({
            message: error.message || 'Failed to create VR experience',
            error: process.env.NODE_ENV === 'development' ? error.stack : undefined
        });
    }
});
exports.createVRExperience = createVRExperience;
