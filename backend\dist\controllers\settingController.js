"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.importSettings = exports.exportSettings = exports.resetSettingsToDefault = exports.updateSettings = exports.getSettings = void 0;
const Setting_1 = __importDefault(require("../models/Setting"));
// Helper function to get the single settings document
const getSettingsDocument = () => __awaiter(void 0, void 0, void 0, function* () {
    let settings = yield Setting_1.default.findOne();
    if (!settings) {
        // Create a default settings document if none exists
        settings = new Setting_1.default({});
        yield settings.save();
    }
    return settings;
});
// Get settings by section
const getSettings = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { section } = req.params;
        const settings = yield getSettingsDocument();
        if (section && settings.get(section) !== undefined) {
            res.status(200).json(settings.get(section));
        }
        else if (!section) {
            // If no section is specified, return all settings
            res.status(200).json(settings.toObject());
        }
        else {
            res.status(404).json({ message: 'Settings section not found' });
        }
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.getSettings = getSettings;
// Update settings by section
const updateSettings = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { section } = req.params;
        const updates = req.body;
        // Basic validation
        if (!section || Object.keys(updates).length === 0) {
            return res.status(400).json({ message: 'Section and updates are required' });
        }
        const settings = yield getSettingsDocument();
        settings.set(section, updates);
        const updatedSettings = yield settings.save();
        res.status(200).json(updatedSettings.get(section));
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.updateSettings = updateSettings;
// Reset settings section to default (requires defining default settings logic)
const resetSettingsToDefault = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { section } = req.params;
        // TODO: Implement logic to load default settings for the given section
        // For now, this will just remove the section
        const settings = yield getSettingsDocument();
        settings.set(section, undefined); // Or set to a predefined default object
        yield settings.save();
        res.status(200).json({ message: `Settings section '${section}' reset to default (or removed)` });
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.resetSettingsToDefault = resetSettingsToDefault;
// Export settings (basic implementation)
const exportSettings = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { section } = req.params;
        const settings = yield getSettingsDocument();
        let dataToExport;
        if (section && settings.get(section) !== undefined) {
            dataToExport = settings.get(section);
        }
        else if (!section) {
            dataToExport = settings.toObject();
        }
        else {
            return res.status(404).json({ message: 'Settings section not found' });
        }
        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Content-Disposition', `attachment; filename=settings${section ? '_' + section : ''}.json`);
        res.send(JSON.stringify(dataToExport, null, 2));
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.exportSettings = exportSettings;
// Import settings (basic implementation)
const importSettings = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const importedData = req.body;
        // Basic validation
        if (Object.keys(importedData).length === 0) {
            return res.status(400).json({ message: 'Import data is required' });
        }
        const settings = yield getSettingsDocument();
        // This will overwrite existing settings with imported data
        // More sophisticated merge logic might be needed
        Object.assign(settings, importedData);
        yield settings.save();
        res.status(200).json({ message: 'Settings imported successfully' });
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.importSettings = importSettings;
