import { Line2NodeMaterial, Mesh } from "three/webgpu";
import { LineSegmentsGeometry } from "../LineSegmentsGeometry.js";

declare class LineSegments2 extends Mesh {
    geometry: LineSegmentsGeometry;
    material: Line2NodeMaterial;

    readonly isLineSegments2: true;

    constructor(geometry?: LineSegmentsGeometry, material?: Line2NodeMaterial);

    computeLineDistances(): this;
}

export { LineSegments2 };
nstead of individual segments.
 */
export class LineSegments2 extends Mesh {
    geometry: LineSegmentsGeometry;
    material: LineMaterial;

    /**
     * Read-only flag to check if a given object is of type LineSegments2.
     */
    readonly isLineSegments2: true;

    resolution: Vector2;

    /**
     * @param geometry (optional) Pair(s) of vertices representing each line segment.
     * @param material (optional) Material for the line. Default is a {@link LineMaterial} with random color.
     */
    constructor(geometry?: LineSegmentsGeometry, material?: LineMaterial);

    computeLineDistances(): this;

    /**
     * Called by the framework to update the material's resolution property, needed for screen-scaled widths.
     *
     * If your object is not visible to a camera (e.g. by [layers]{@link Object3D.layers} or
     * [visible]{@link Object3D.visible}), you must call this manually whenever the viewport changes.
     * @param renderer
     */
    onBeforeRender(renderer: WebGLRenderer): void;
}
