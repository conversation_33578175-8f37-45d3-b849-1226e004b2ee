!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).webgl_sdf_generator=t()}(this,(function(){"use strict";return function(){var e=function(e){function t(e,t,n,r,a,i,o,l,c,u){var s=1-c;u.x=s*s*s*e+3*s*s*c*n+3*s*c*c*a+c*c*c*o,u.y=s*s*s*t+3*s*s*c*r+3*s*c*c*i+c*c*c*l}function n(e,t){for(var n,r,a,i,o,l=/([MLQCZ])([^MLQCZ]*)/g;n=l.exec(e);){var c=n[2].replace(/^\s*|\s*$/g,"").split(/[,\s]+/).map((function(e){return parseFloat(e)}));switch(n[1]){case"M":i=r=c[0],o=a=c[1];break;case"L":c[0]===i&&c[1]===o||t("L",i,o,i=c[0],o=c[1]);break;case"Q":t("Q",i,o,i=c[2],o=c[3],c[0],c[1]);break;case"C":t("C",i,o,i=c[4],o=c[5],c[0],c[1],c[2],c[3]);break;case"Z":i===r&&o===a||t("L",i,o,r,a)}}}function r(e,r,a){void 0===a&&(a=16);var i={x:0,y:0};n(e,(function(e,n,o,l,c,u,s,f,v){switch(e){case"L":r(n,o,l,c);break;case"Q":for(var d=n,g=o,p=1;p<a;p++)E=o,b=s,y=c,_=void 0,_=1-(T=p/(a-1)),(A=i).x=_*_*n+2*_*T*u+T*T*l,A.y=_*_*E+2*_*T*b+T*T*y,r(d,g,i.x,i.y),d=i.x,g=i.y;break;case"C":for(var m=n,h=o,x=1;x<a;x++)t(n,o,u,s,f,v,l,c,x/(a-1),i),r(m,h,i.x,i.y),m=i.x,h=i.y}var E,b,y,T,A,_}))}var a="precision highp float;attribute vec2 aUV;varying vec2 vUV;void main(){vUV=aUV;gl_Position=vec4(mix(vec2(-1.0),vec2(1.0),aUV),0.0,1.0);}",i=new WeakMap,o={premultipliedAlpha:!1,preserveDrawingBuffer:!0,antialias:!1,depth:!1};function l(e,t){var n=e.getContext?e.getContext("webgl",o):e,r=i.get(n);if(!r){var a="undefined"!=typeof WebGL2RenderingContext&&n instanceof WebGL2RenderingContext,l={},c={},u={},s=-1,f=[];function e(e){var t=l[e];if(!t&&!(t=l[e]=n.getExtension(e)))throw new Error(e+" not supported");return t}function t(e,t){var r=n.createShader(t);return n.shaderSource(r,e),n.compileShader(r),r}function o(r,i,o,l){if(!c[r]){var u={},s={},f=n.createProgram();n.attachShader(f,t(i,n.VERTEX_SHADER)),n.attachShader(f,t(o,n.FRAGMENT_SHADER)),n.linkProgram(f),c[r]={program:f,transaction:function(t){n.useProgram(f),t({setUniform:function(e,t){for(var r=[],a=arguments.length-2;a-- >0;)r[a]=arguments[a+2];var i=s[t]||(s[t]=n.getUniformLocation(f,t));n["uniform"+e].apply(n,[i].concat(r))},setAttribute:function(t,r,i,o,l){var c=u[t];c||(c=u[t]={buf:n.createBuffer(),loc:n.getAttribLocation(f,t),data:null}),n.bindBuffer(n.ARRAY_BUFFER,c.buf),n.vertexAttribPointer(c.loc,r,n.FLOAT,!1,0,0),n.enableVertexAttribArray(c.loc),a?n.vertexAttribDivisor(c.loc,o):e("ANGLE_instanced_arrays").vertexAttribDivisorANGLE(c.loc,o),l!==c.data&&(n.bufferData(n.ARRAY_BUFFER,l,i),c.data=l)}})}}}c[r].transaction(l)}function v(e,t){s++;try{n.activeTexture(n.TEXTURE0+s);var r=u[e];r||(r=u[e]=n.createTexture(),n.bindTexture(n.TEXTURE_2D,r),n.texParameteri(n.TEXTURE_2D,n.TEXTURE_MIN_FILTER,n.NEAREST),n.texParameteri(n.TEXTURE_2D,n.TEXTURE_MAG_FILTER,n.NEAREST)),n.bindTexture(n.TEXTURE_2D,r),t(r,s)}finally{s--}}function d(e,t,r){var a=n.createFramebuffer();f.push(a),n.bindFramebuffer(n.FRAMEBUFFER,a),n.activeTexture(n.TEXTURE0+t),n.bindTexture(n.TEXTURE_2D,e),n.framebufferTexture2D(n.FRAMEBUFFER,n.COLOR_ATTACHMENT0,n.TEXTURE_2D,e,0);try{r(a)}finally{n.deleteFramebuffer(a),n.bindFramebuffer(n.FRAMEBUFFER,f[--f.length-1]||null)}}function g(){l={},c={},u={},s=-1,f.length=0}n.canvas.addEventListener("webglcontextlost",(function(e){g(),e.preventDefault()}),!1),i.set(n,r={gl:n,isWebGL2:a,getExtension:e,withProgram:o,withTexture:v,withTextureFramebuffer:d,handleContextLoss:g})}t(r)}function c(e,t,n,r,i,o,c,u){void 0===c&&(c=15),void 0===u&&(u=null),l(e,(function(e){var l=e.gl,s=e.withProgram;(0,e.withTexture)("copy",(function(e,f){l.texImage2D(l.TEXTURE_2D,0,l.RGBA,i,o,0,l.RGBA,l.UNSIGNED_BYTE,t),s("copy",a,"precision highp float;uniform sampler2D tex;varying vec2 vUV;void main(){gl_FragColor=texture2D(tex,vUV);}",(function(e){var t=e.setUniform;(0,e.setAttribute)("aUV",2,l.STATIC_DRAW,0,new Float32Array([0,0,2,0,0,2])),t("1i","image",f),l.bindFramebuffer(l.FRAMEBUFFER,u||null),l.disable(l.BLEND),l.colorMask(8&c,4&c,2&c,1&c),l.viewport(n,r,i,o),l.scissor(n,r,i,o),l.drawArrays(l.TRIANGLES,0,3)}))}))}))}var u=Object.freeze({__proto__:null,withWebGLContext:l,renderImageData:c,resizeWebGLCanvasWithoutClearing:function(e,t,n){var r=e.width,a=e.height;l(e,(function(i){var o=i.gl,l=new Uint8Array(r*a*4);o.readPixels(0,0,r,a,o.RGBA,o.UNSIGNED_BYTE,l),e.width=t,e.height=n,c(o,l,0,0,r,a)}))}});function s(e,t,n,a,i,o){void 0===o&&(o=1);var l=new Uint8Array(e*t),c=a[2]-a[0],u=a[3]-a[1],s=[];r(n,(function(e,t,n,r){s.push({x1:e,y1:t,x2:n,y2:r,minX:Math.min(e,n),minY:Math.min(t,r),maxX:Math.max(e,n),maxY:Math.max(t,r)})})),s.sort((function(e,t){return e.maxX-t.maxX}));for(var f=0;f<e;f++)for(var v=0;v<t;v++){var g=m(a[0]+c*(f+.5)/e,a[1]+u*(v+.5)/t),p=Math.pow(1-Math.abs(g)/i,o)/2;g<0&&(p=1-p),p=Math.max(0,Math.min(255,Math.round(255*p))),l[v*e+f]=p}return l;function m(e,t){for(var n=1/0,r=1/0,a=s.length;a--;){var i=s[a];if(i.maxX+r<=e)break;if(e+r>i.minX&&t-r<i.maxY&&t+r>i.minY){var o=d(e,t,i.x1,i.y1,i.x2,i.y2);o<n&&(n=o,r=Math.sqrt(n))}}return function(e,t){for(var n=0,r=s.length;r--;){var a=s[r];if(a.maxX<=e)break;a.y1>t!=a.y2>t&&e<(a.x2-a.x1)*(t-a.y1)/(a.y2-a.y1)+a.x1&&(n+=a.y1<a.y2?1:-1)}return 0!==n}(e,t)&&(r=-r),r}}function f(e,t,n,r,a,i,o,l,c,u){void 0===i&&(i=1),void 0===l&&(l=0),void 0===c&&(c=0),void 0===u&&(u=0),v(e,t,n,r,a,i,o,null,l,c,u)}function v(e,t,n,r,a,i,o,l,u,f,v){void 0===i&&(i=1),void 0===u&&(u=0),void 0===f&&(f=0),void 0===v&&(v=0);for(var d=s(e,t,n,r,a,i),g=new Uint8Array(4*d.length),p=0;p<d.length;p++)g[4*p+v]=d[p];c(o,g,u,f,e,t,1<<3-v,l)}function d(e,t,n,r,a,i){var o=a-n,l=i-r,c=o*o+l*l,u=c?Math.max(0,Math.min(1,((e-n)*o+(t-r)*l)/c)):0,s=e-(n+u*o),f=t-(r+u*l);return s*s+f*f}var g=Object.freeze({__proto__:null,generate:s,generateIntoCanvas:f,generateIntoFramebuffer:v}),p=new Float32Array([0,0,2,0,0,2]),m=null,h=!1,x={},E=new WeakMap;function b(e){if(!h&&!_(e))throw new Error("WebGL generation not supported")}function y(e,t,n,r,a,i,o){if(void 0===i&&(i=1),void 0===o&&(o=null),!o&&!(o=m)){var c="function"==typeof OffscreenCanvas?new OffscreenCanvas(1,1):"undefined"!=typeof document?document.createElement("canvas"):null;if(!c)throw new Error("OffscreenCanvas or DOM canvas not supported");o=m=c.getContext("webgl",{depth:!1})}b(o);var u=new Uint8Array(e*t*4);l(o,(function(o){var l=o.gl,c=o.withTexture,s=o.withTextureFramebuffer;c("readable",(function(o,c){l.texImage2D(l.TEXTURE_2D,0,l.RGBA,e,t,0,l.RGBA,l.UNSIGNED_BYTE,null),s(o,c,(function(o){A(e,t,n,r,a,i,l,o,0,0,0),l.readPixels(0,0,e,t,l.RGBA,l.UNSIGNED_BYTE,u)}))}))}));for(var s=new Uint8Array(e*t),f=0,v=0;f<u.length;f+=4)s[v++]=u[f];return s}function T(e,t,n,r,a,i,o,l,c,u){void 0===i&&(i=1),void 0===l&&(l=0),void 0===c&&(c=0),void 0===u&&(u=0),A(e,t,n,r,a,i,o,null,l,c,u)}function A(e,t,n,i,o,c,u,s,f,v,d){void 0===c&&(c=1),void 0===f&&(f=0),void 0===v&&(v=0),void 0===d&&(d=0),b(u);var g=[];r(n,(function(e,t,n,r){g.push(e,t,n,r)})),g=new Float32Array(g),l(u,(function(n){var r=n.gl,l=n.isWebGL2,u=n.getExtension,m=n.withProgram,h=n.withTexture,x=n.withTextureFramebuffer,E=n.handleContextLoss;if(h("rawDistances",(function(n,h){e===n._lastWidth&&t===n._lastHeight||r.texImage2D(r.TEXTURE_2D,0,r.RGBA,n._lastWidth=e,n._lastHeight=t,0,r.RGBA,r.UNSIGNED_BYTE,null),m("main","precision highp float;uniform vec4 uGlyphBounds;attribute vec2 aUV;attribute vec4 aLineSegment;varying vec4 vLineSegment;varying vec2 vGlyphXY;void main(){vLineSegment=aLineSegment;vGlyphXY=mix(uGlyphBounds.xy,uGlyphBounds.zw,aUV);gl_Position=vec4(mix(vec2(-1.0),vec2(1.0),aUV),0.0,1.0);}","precision highp float;uniform vec4 uGlyphBounds;uniform float uMaxDistance;uniform float uExponent;varying vec4 vLineSegment;varying vec2 vGlyphXY;float absDistToSegment(vec2 point,vec2 lineA,vec2 lineB){vec2 lineDir=lineB-lineA;float lenSq=dot(lineDir,lineDir);float t=lenSq==0.0 ? 0.0 : clamp(dot(point-lineA,lineDir)/lenSq,0.0,1.0);vec2 linePt=lineA+t*lineDir;return distance(point,linePt);}void main(){vec4 seg=vLineSegment;vec2 p=vGlyphXY;float dist=absDistToSegment(p,seg.xy,seg.zw);float val=pow(1.0-clamp(dist/uMaxDistance,0.0,1.0),uExponent)*0.5;bool crossing=(seg.y>p.y!=seg.w>p.y)&&(p.x<(seg.z-seg.x)*(p.y-seg.y)/(seg.w-seg.y)+seg.x);bool crossingUp=crossing&&vLineSegment.y<vLineSegment.w;gl_FragColor=vec4(crossingUp ? 1.0/255.0 : 0.0,crossing&&!crossingUp ? 1.0/255.0 : 0.0,0.0,val);}",(function(a){var s=a.setAttribute,f=a.setUniform,v=!l&&u("ANGLE_instanced_arrays"),d=!l&&u("EXT_blend_minmax");s("aUV",2,r.STATIC_DRAW,0,p),s("aLineSegment",4,r.DYNAMIC_DRAW,1,g),f.apply(void 0,["4f","uGlyphBounds"].concat(i)),f("1f","uMaxDistance",o),f("1f","uExponent",c),x(n,h,(function(n){r.enable(r.BLEND),r.colorMask(!0,!0,!0,!0),r.viewport(0,0,e,t),r.scissor(0,0,e,t),r.blendFunc(r.ONE,r.ONE),r.blendEquationSeparate(r.FUNC_ADD,l?r.MAX:d.MAX_EXT),r.clear(r.COLOR_BUFFER_BIT),l?r.drawArraysInstanced(r.TRIANGLES,0,3,g.length/4):v.drawArraysInstancedANGLE(r.TRIANGLES,0,3,g.length/4)}))})),m("post",a,"precision highp float;uniform sampler2D tex;varying vec2 vUV;void main(){vec4 color=texture2D(tex,vUV);bool inside=color.r!=color.g;float val=inside ? 1.0-color.a : color.a;gl_FragColor=vec4(val);}",(function(n){n.setAttribute("aUV",2,r.STATIC_DRAW,0,p),n.setUniform("1i","tex",h),r.bindFramebuffer(r.FRAMEBUFFER,s),r.disable(r.BLEND),r.colorMask(0===d,1===d,2===d,3===d),r.viewport(f,v,e,t),r.scissor(f,v,e,t),r.drawArrays(r.TRIANGLES,0,3)}))})),r.isContextLost())throw E(),new Error("webgl context lost")}))}function _(e){var t=e&&e!==m?e.canvas||e:x,n=E.get(t);if(void 0===n){h=!0;var r=null;try{var a=[97,106,97,61,99,137,118,80,80,118,137,99,61,97,106,97],i=y(4,4,"M8,8L16,8L24,24L16,24Z",[0,0,32,32],24,1,e);(n=i&&a.length===i.length&&i.every((function(e,t){return e===a[t]})))||(r="bad trial run results",console.info(a,i))}catch(e){n=!1,r=e.message}r&&console.warn("WebGL SDF generation not supported:",r),h=!1,E.set(t,n)}return n}var w=Object.freeze({__proto__:null,generate:y,generateIntoCanvas:T,generateIntoFramebuffer:A,isSupported:_});return e.forEachPathCommand=n,e.generate=function(e,t,n,r,a,i){void 0===a&&(a=Math.max(r[2]-r[0],r[3]-r[1])/2),void 0===i&&(i=1);try{return y.apply(w,arguments)}catch(e){return console.info("WebGL SDF generation failed, falling back to JS",e),s.apply(g,arguments)}},e.generateIntoCanvas=function(e,t,n,r,a,i,o,l,c,u){void 0===a&&(a=Math.max(r[2]-r[0],r[3]-r[1])/2),void 0===i&&(i=1),void 0===l&&(l=0),void 0===c&&(c=0),void 0===u&&(u=0);try{return T.apply(w,arguments)}catch(e){return console.info("WebGL SDF generation failed, falling back to JS",e),f.apply(g,arguments)}},e.javascript=g,e.pathToLineSegments=r,e.webgl=w,e.webglUtils=u,Object.defineProperty(e,"__esModule",{value:!0}),e}({});return e}}));
