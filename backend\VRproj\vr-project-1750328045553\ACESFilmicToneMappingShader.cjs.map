{"version": 3, "file": "ACESFilmicToneMappingShader.cjs", "sources": ["../../src/shaders/ACESFilmicToneMappingShader.ts"], "sourcesContent": ["/**\n * ACES Filmic Tone Mapping Shader by <PERSON>\n * source: https://github.com/selfshadow/ltc_code/blob/master/webgl/shaders/ltc/ltc_blit.fs\n *\n * this implementation of ACES is modified to accommodate a brighter viewing environment.\n * the scale factor of 1/0.6 is subjective. see discussion in #19621.\n */\n\nimport type { IUniform, Texture } from 'three'\nimport type { IShader } from './types'\n\nexport type ACESFilmicToneMappingShaderUniforms = {\n  exposure: IUniform<number>\n  tDiffuse: IUniform<Texture | null>\n}\n\nexport interface IACESFilmicToneMappingShader extends IShader<ACESFilmicToneMappingShaderUniforms> {}\n\nexport const ACESFilmicToneMappingShader: IACESFilmicToneMappingShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    exposure: { value: 1.0 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;,\n\n    void main() {,\n\n    \tvUv = uv;,\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );,\n\n    },\n  `,\n  fragmentShader: /* glsl */ `\n    #define saturate(a) clamp( a, 0.0, 1.0 ),\n\n    uniform sampler2D tDiffuse;,\n\n    uniform float exposure;,\n\n    varying vec2 vUv;,\n\n    vec3 RRTAndODTFit( vec3 v ) {,\n\n    \tvec3 a = v * ( v + 0.0245786 ) - 0.000090537;,\n    \tvec3 b = v * ( 0.983729 * v + 0.4329510 ) + 0.238081;,\n    \treturn a / b;,\n\n    },\n\n    vec3 ACESFilmicToneMapping( vec3 color ) {,\n\n      // sRGB => XYZ => D65_2_D60 => AP1 => RRT_SAT\n    \tconst mat3 ACESInputMat = mat3(,\n    \t\tvec3( 0.59719, 0.07600, 0.02840 ),, // transposed from source\n    \t\tvec3( 0.35458, 0.90834, 0.13383 ),,\n    \t\tvec3( 0.04823, 0.01566, 0.83777 ),\n    \t);,\n\n      // ODT_SAT => XYZ => D60_2_D65 => sRGB\n    \tconst mat3 ACESOutputMat = mat3(,\n    \t\tvec3(  1.60475, -0.10208, -0.00327 ),, // transposed from source\n    \t\tvec3( -0.53108,  1.10813, -0.07276 ),,\n    \t\tvec3( -0.07367, -0.00605,  1.07602 ),\n    \t);,\n\n    \tcolor = ACESInputMat * color;,\n\n      // Apply RRT and ODT\n    \tcolor = RRTAndODTFit( color );,\n\n    \tcolor = ACESOutputMat * color;,\n\n      // Clamp to [0, 1]\n    \treturn saturate( color );,\n\n    },\n\n    void main() {,\n\n    \tvec4 tex = texture2D( tDiffuse, vUv );,\n\n    \ttex.rgb *= exposure / 0.6;, // pre-exposed, outside of the tone mapping function\n\n    \tgl_FragColor = vec4( ACESFilmicToneMapping( tex.rgb ), tex.a );,\n\n    },\n  `,\n}\n"], "names": [], "mappings": ";;AAkBO,MAAM,8BAA4D;AAAA,EACvE,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA,IACxB,UAAU,EAAE,OAAO,EAAI;AAAA,EACzB;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuD7B;;"}