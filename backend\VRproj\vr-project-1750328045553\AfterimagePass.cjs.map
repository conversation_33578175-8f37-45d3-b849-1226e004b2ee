{"version": 3, "file": "AfterimagePass.cjs", "sources": ["../../src/postprocessing/AfterimagePass.ts"], "sourcesContent": ["import {\n  LinearFilter,\n  MeshBasicMaterial,\n  NearestFilter,\n  RGBAFormat,\n  WebGLRenderer,\n  ShaderMaterial,\n  UniformsUtils,\n  WebGLRenderTarget,\n} from 'three'\nimport { Pass, FullScreenQuad } from './Pass'\nimport { AfterimageShader } from '../shaders/AfterimageShader'\n\nclass AfterimagePass extends Pass {\n  public shader\n  public uniforms\n  public textureComp: WebGLRenderTarget\n  public textureOld: WebGLRenderTarget\n  public shaderMaterial: ShaderMaterial\n  public compFsQuad: FullScreenQuad<ShaderMaterial>\n  public copyFsQuad: FullScreenQuad<MeshBasicMaterial>\n\n  constructor(damp = 0.96, shader = AfterimageShader) {\n    super()\n\n    this.shader = shader\n    this.uniforms = UniformsUtils.clone(shader.uniforms)\n    this.uniforms['damp'].value = damp\n\n    this.textureComp = new WebGLRenderTarget(window.innerWidth, window.innerHeight, {\n      minFilter: LinearFilter,\n      magFilter: NearestFilter,\n      format: RGBAFormat,\n    })\n\n    this.textureOld = new WebGLRenderTarget(window.innerWidth, window.innerHeight, {\n      minFilter: LinearFilter,\n      magFilter: NearestFilter,\n      format: RGBAFormat,\n    })\n\n    this.shaderMaterial = new ShaderMaterial({\n      uniforms: this.uniforms,\n      vertexShader: this.shader.vertexShader,\n      fragmentShader: this.shader.fragmentShader,\n    })\n\n    this.compFsQuad = new FullScreenQuad(this.shaderMaterial)\n\n    let material = new MeshBasicMaterial()\n    this.copyFsQuad = new FullScreenQuad(material)\n  }\n\n  public render(renderer: WebGLRenderer, writeBuffer: WebGLRenderTarget, readBuffer: WebGLRenderTarget): void {\n    this.uniforms['tOld'].value = this.textureOld.texture\n    this.uniforms['tNew'].value = readBuffer.texture\n\n    renderer.setRenderTarget(this.textureComp)\n    this.compFsQuad.render(renderer)\n\n    this.copyFsQuad.material.map = this.textureComp.texture\n\n    if (this.renderToScreen) {\n      renderer.setRenderTarget(null)\n      this.copyFsQuad.render(renderer)\n    } else {\n      renderer.setRenderTarget(writeBuffer)\n\n      if (this.clear) renderer.clear()\n\n      this.copyFsQuad.render(renderer)\n    }\n\n    // Swap buffers.\n    let temp = this.textureOld\n    this.textureOld = this.textureComp\n    this.textureComp = temp\n    // Now textureOld contains the latest image, ready for the next frame.\n  }\n\n  public setSize(width: number, height: number): void {\n    this.textureComp.setSize(width, height)\n    this.textureOld.setSize(width, height)\n  }\n}\n\nexport { AfterimagePass }\n"], "names": ["Pass", "AfterimageShader", "UniformsUtils", "WebGLRenderTarget", "LinearFilter", "NearestFilter", "RGBAFormat", "ShaderMaterial", "FullScreenQuad", "MeshBasicMaterial"], "mappings": ";;;;;;;;;;;AAaA,MAAM,uBAAuBA,KAAAA,KAAK;AAAA,EAShC,YAAY,OAAO,MAAM,SAASC,iBAAAA,kBAAkB;AAC5C;AATD;AACA;AACA;AACA;AACA;AACA;AACA;AAKL,SAAK,SAAS;AACd,SAAK,WAAWC,MAAA,cAAc,MAAM,OAAO,QAAQ;AAC9C,SAAA,SAAS,MAAM,EAAE,QAAQ;AAE9B,SAAK,cAAc,IAAIC,MAAA,kBAAkB,OAAO,YAAY,OAAO,aAAa;AAAA,MAC9E,WAAWC,MAAA;AAAA,MACX,WAAWC,MAAA;AAAA,MACX,QAAQC,MAAA;AAAA,IAAA,CACT;AAED,SAAK,aAAa,IAAIH,MAAA,kBAAkB,OAAO,YAAY,OAAO,aAAa;AAAA,MAC7E,WAAWC,MAAA;AAAA,MACX,WAAWC,MAAA;AAAA,MACX,QAAQC,MAAA;AAAA,IAAA,CACT;AAEI,SAAA,iBAAiB,IAAIC,qBAAe;AAAA,MACvC,UAAU,KAAK;AAAA,MACf,cAAc,KAAK,OAAO;AAAA,MAC1B,gBAAgB,KAAK,OAAO;AAAA,IAAA,CAC7B;AAED,SAAK,aAAa,IAAIC,KAAe,eAAA,KAAK,cAAc;AAEpD,QAAA,WAAW,IAAIC,MAAAA;AACd,SAAA,aAAa,IAAID,KAAA,eAAe,QAAQ;AAAA,EAC/C;AAAA,EAEO,OAAO,UAAyB,aAAgC,YAAqC;AAC1G,SAAK,SAAS,MAAM,EAAE,QAAQ,KAAK,WAAW;AAC9C,SAAK,SAAS,MAAM,EAAE,QAAQ,WAAW;AAEhC,aAAA,gBAAgB,KAAK,WAAW;AACpC,SAAA,WAAW,OAAO,QAAQ;AAE/B,SAAK,WAAW,SAAS,MAAM,KAAK,YAAY;AAEhD,QAAI,KAAK,gBAAgB;AACvB,eAAS,gBAAgB,IAAI;AACxB,WAAA,WAAW,OAAO,QAAQ;AAAA,IAAA,OAC1B;AACL,eAAS,gBAAgB,WAAW;AAEpC,UAAI,KAAK;AAAO,iBAAS,MAAM;AAE1B,WAAA,WAAW,OAAO,QAAQ;AAAA,IACjC;AAGA,QAAI,OAAO,KAAK;AAChB,SAAK,aAAa,KAAK;AACvB,SAAK,cAAc;AAAA,EAErB;AAAA,EAEO,QAAQ,OAAe,QAAsB;AAC7C,SAAA,YAAY,QAAQ,OAAO,MAAM;AACjC,SAAA,WAAW,QAAQ,OAAO,MAAM;AAAA,EACvC;AACF;;"}