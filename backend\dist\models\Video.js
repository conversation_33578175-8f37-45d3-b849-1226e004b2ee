"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
// Utility function to extract YouTube video ID
function extractYouTubeVideoId(url) {
    if (!url)
        return null;
    const patterns = [
        /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/|m\.youtube\.com\/watch\?v=)([^&\n?#]+)/,
        /youtube\.com\/v\/([^&\n?#]+)/,
        /youtube\.com\/.*[?&]v=([^&\n?#]+)/
    ];
    for (const pattern of patterns) {
        const match = url.match(pattern);
        if (match && match[1]) {
            return match[1];
        }
    }
    return null;
}
// Utility function to generate YouTube thumbnail URL
function generateYouTubeThumbnail(url) {
    const videoId = extractYouTubeVideoId(url);
    if (!videoId)
        return null;
    return `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
}
// Check if URL is a YouTube URL
function isYouTubeUrl(url) {
    if (!url)
        return false;
    return /(?:youtube\.com|youtu\.be)/.test(url);
}
const VideoSchema = new mongoose_1.Schema({
    title: { type: String, required: true },
    url: { type: String, required: true },
    thumbnailUrl: { type: String },
    description: { type: String },
    category: { type: String },
    duration: { type: String },
    author: { type: String },
    views: { type: Number, default: 0 },
    hasVR: { type: Boolean, default: false },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
});
// Update the updatedAt field and generate thumbnail on save
VideoSchema.pre('save', function (next) {
    this.updatedAt = new Date();
    // Auto-generate thumbnail for YouTube URLs if not provided
    if (this.url && isYouTubeUrl(this.url) && !this.thumbnailUrl) {
        const thumbnail = generateYouTubeThumbnail(this.url);
        if (thumbnail) {
            this.thumbnailUrl = thumbnail;
        }
    }
    next();
});
const Video = mongoose_1.default.model('Video', VideoSchema);
exports.default = Video;
