"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getContentStatistics = exports.getMetricsById = exports.getContentMetrics = exports.getAudienceMetrics = exports.getOverviewMetrics = exports.getAnalyticEvents = exports.getAggregatedAnalytics = exports.recordAnalyticEvent = void 0;
const Analytic_1 = __importDefault(require("../models/Analytic"));
const Article_1 = __importDefault(require("../models/Article"));
const Photo_1 = __importDefault(require("../models/Photo"));
const Video_1 = __importDefault(require("../models/Video"));
const Experience_1 = __importDefault(require("../models/Experience"));
// Record a new analytics event
const recordAnalyticEvent = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { eventType, metadata } = req.body;
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a._id; // Get user ID from authenticated user (optional)
        // Basic validation
        if (!eventType) {
            return res.status(400).json({ message: 'Event type is required' });
        }
        const newAnalytic = new Analytic_1.default({
            userId, // Include user ID
            eventType,
            metadata,
            timestamp: new Date()
        });
        const savedAnalytic = yield newAnalytic.save();
        res.status(201).json(savedAnalytic);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.recordAnalyticEvent = recordAnalyticEvent;
// Get aggregated analytics data (basic example: count events by type)
const getAggregatedAnalytics = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Example aggregation: count events by eventType
        const aggregatedData = yield Analytic_1.default.aggregate([
            {
                $group: {
                    _id: '$eventType',
                    count: { $sum: 1 },
                },
            },
            {
                $sort: { count: -1 },
            },
        ]);
        res.status(200).json(aggregatedData);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.getAggregatedAnalytics = getAggregatedAnalytics;
// Get analytics events with date range filtering
const getAnalyticEvents = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { startDate, endDate, eventType } = req.query;
        const query = {};
        if (startDate || endDate) {
            query.timestamp = {};
            if (startDate) {
                query.timestamp.$gte = new Date(startDate);
            }
            if (endDate) {
                query.timestamp.$lte = new Date(endDate);
            }
        }
        if (eventType) {
            query.eventType = eventType;
        }
        const events = yield Analytic_1.default.find(query).sort({ timestamp: -1 }); // Sort by latest events first
        res.status(200).json(events);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.getAnalyticEvents = getAnalyticEvents;
// Get overview analytics metrics
const getOverviewMetrics = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { startDate, endDate } = req.query; // interval is not used in this basic overview
        const query = {};
        if (startDate || endDate) {
            query.timestamp = {};
            if (startDate) {
                query.timestamp.$gte = new Date(startDate);
            }
            if (endDate) {
                query.timestamp.$lte = new Date(endDate);
            }
        }
        // Calculate total events
        const totalEvents = yield Analytic_1.default.countDocuments(query);
        // Calculate unique users (only for events with a userId)
        const uniqueUsers = yield Analytic_1.default.aggregate([
            {
                $match: Object.assign(Object.assign({}, query), { userId: { $exists: true, $ne: null } // Filter for events with a userId
                 })
            },
            {
                $group: {
                    _id: '$userId'
                }
            },
            {
                $count: 'uniqueUserCount'
            }
        ]);
        const uniqueUserCount = uniqueUsers.length > 0 ? uniqueUsers[0].uniqueUserCount : 0;
        res.status(200).json({
            totalEvents,
            uniqueUsers: uniqueUserCount,
            // Add other overview metrics here if needed
        });
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.getOverviewMetrics = getOverviewMetrics;
// Get audience analytics metrics
const getAudienceMetrics = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { startDate, endDate } = req.query; // interval is not used in this basic implementation
        const query = {};
        if (startDate || endDate) {
            query.timestamp = {};
            if (startDate) {
                query.timestamp.$gte = new Date(startDate);
            }
            if (endDate) {
                query.timestamp.$lte = new Date(endDate);
            }
        }
        // Calculate total unique users (users who have ever logged an event)
        const totalUniqueUsersResult = yield Analytic_1.default.aggregate([
            {
                $match: {
                    userId: { $exists: true, $ne: null } // Only consider events with a user ID
                }
            },
            {
                $group: {
                    _id: '$userId'
                }
            },
            {
                $count: 'totalUniqueUserCount'
            }
        ]);
        const totalUniqueUserCount = totalUniqueUsersResult.length > 0 ? totalUniqueUsersResult[0].totalUniqueUserCount : 0;
        // Calculate active users within the specified date range (users with events in the range)
        const activeUsersResult = yield Analytic_1.default.aggregate([
            {
                $match: Object.assign(Object.assign({}, query), { userId: { $exists: true, $ne: null } // Only consider events with a user ID
                 })
            },
            {
                $group: {
                    _id: '$userId'
                }
            },
            {
                $count: 'activeUserCount'
            }
        ]);
        const activeUserCount = activeUsersResult.length > 0 ? activeUsersResult[0].activeUserCount : 0;
        res.status(200).json({
            totalUniqueUsers: totalUniqueUserCount,
            activeUsers: activeUserCount,
            // Add other audience metrics here if needed
        });
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.getAudienceMetrics = getAudienceMetrics;
// Get content analytics metrics
const getContentMetrics = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { startDate, endDate, category } = req.query; // interval is not used in this basic implementation
        const query = {};
        if (startDate || endDate) {
            query.timestamp = {};
            if (startDate) {
                query.timestamp.$gte = new Date(startDate);
            }
            if (endDate) {
                query.timestamp.$lte = new Date(endDate);
            }
        }
        // Add category filter if provided (assuming category is stored in metadata)
        if (category) {
            query['metadata.category'] = category;
        }
        // Calculate total articles created within the date range and category filter
        const totalArticles = yield Analytic_1.default.countDocuments(Object.assign(Object.assign({}, query), { eventType: 'articleCreated' // Assuming 'articleCreated' is the event type for new articles
         }));
        // Calculate total photos created within the date range and category filter
        const totalPhotos = yield Analytic_1.default.countDocuments(Object.assign(Object.assign({}, query), { eventType: 'photoCreated' // Assuming 'photoCreated' is the event type for new photos
         }));
        // Calculate total videos created within the date range and category filter
        const totalVideos = yield Analytic_1.default.countDocuments(Object.assign(Object.assign({}, query), { eventType: 'videoCreated' // Assuming 'videoCreated' is the event type for new videos
         }));
        res.status(200).json({
            totalArticles,
            totalPhotos,
            totalVideos,
            // Add other content metrics here if needed
        });
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.getContentMetrics = getContentMetrics;
// Get specific analytics metrics by ID
const getMetricsById = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { metricId } = req.params;
        const { startDate, endDate, interval } = req.query;
        const query = {};
        if (startDate || endDate) {
            query.timestamp = {};
            if (startDate) {
                query.timestamp.$gte = new Date(startDate);
            }
            if (endDate) {
                query.timestamp.$lte = new Date(endDate);
            }
        }
        let metricData = [];
        // TODO: Implement specific logic for each metricId
        switch (metricId) {
            case 'eventsOverTime':
                // Example: Count events over time, grouped by interval
                metricData = yield Analytic_1.default.aggregate([
                    { $match: query },
                    {
                        $group: {
                            _id: {
                                $dateToString: {
                                    format: getDateFormat(interval), // Helper function needed for format based on interval
                                    date: '$timestamp'
                                }
                            },
                            count: { $sum: 1 }
                        }
                    },
                    { $sort: { _id: 1 } }
                ]);
                break;
            case 'usersOverTime':
                // Example: Count unique users over time, grouped by interval
                metricData = yield Analytic_1.default.aggregate([
                    { $match: Object.assign(Object.assign({}, query), { userId: { $exists: true, $ne: null } }) },
                    {
                        $group: {
                            _id: {
                                $dateToString: {
                                    format: getDateFormat(interval), // Helper function needed
                                    date: '$timestamp'
                                }
                            },
                            uniqueUsers: { $addToSet: '$userId' }
                        }
                    },
                    {
                        $project: {
                            _id: 1,
                            count: { $size: '$uniqueUsers' }
                        }
                    },
                    { $sort: { _id: 1 } }
                ]);
                break;
            // Add cases for other metricIds
            default:
                return res.status(404).json({ message: 'Metric not found' });
        }
        res.status(200).json({
            metricId,
            data: metricData,
        });
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
});
exports.getMetricsById = getMetricsById;
// Helper function to get date format string for MongoDB aggregation based on interval
const getDateFormat = (interval) => {
    switch (interval) {
        case 'hour':
            return '%Y-%m-%dT%H';
        case 'day':
            return '%Y-%m-%d';
        case 'week':
            // MongoDB's $dateToString doesn't have a direct week format like 'YYYY-Www'
            // A more complex aggregation might be needed for ISO week dates
            return '%Y-%m-%d'; // Fallback to day for simplicity
        case 'month':
            return '%Y-%m';
        default:
            return '%Y-%m-%d'; // Default to day
    }
};
// Get content statistics by status for dashboard
const getContentStatistics = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Get statistics for articles
        const articleStats = yield Article_1.default.aggregate([
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 }
                }
            }
        ]);
        // Get statistics for photos
        const photoStats = yield Photo_1.default.aggregate([
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 }
                }
            }
        ]);
        // Get statistics for videos
        const videoStats = yield Video_1.default.aggregate([
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 }
                }
            }
        ]);
        // Get statistics for experiences
        const experienceStats = yield Experience_1.default.aggregate([
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 }
                }
            }
        ]);
        // Helper function to format stats
        const formatStats = (stats) => {
            const result = {
                total: 0,
                published: 0,
                draft: 0,
                review: 0,
                archived: 0
            };
            stats.forEach(stat => {
                result.total += stat.count;
                switch (stat._id) {
                    case 'published':
                        result.published = stat.count;
                        break;
                    case 'draft':
                        result.draft = stat.count;
                        break;
                    case 'review':
                        result.review = stat.count;
                        break;
                    case 'archived':
                        result.archived = stat.count;
                        break;
                }
            });
            return result;
        };
        const response = {
            article: formatStats(articleStats),
            photo: formatStats(photoStats),
            video: formatStats(videoStats),
            experience: formatStats(experienceStats)
        };
        res.status(200).json(response);
    }
    catch (error) {
        console.error('Error getting content statistics:', error);
        res.status(500).json({ message: error.message });
    }
});
exports.getContentStatistics = getContentStatistics;
// You can add more functions here for different types of analytics queries
// e.g., get data by date range, filter by metadata, etc.
