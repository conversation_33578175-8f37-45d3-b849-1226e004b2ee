"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const authController_1 = require("../controllers/authController");
const authMiddleware_1 = require("../middleware/authMiddleware"); // We will create this middleware next
const router = express_1.default.Router();
router.post('/register', authController_1.register);
router.post('/login', authController_1.login);
router.get('/me', authMiddleware_1.protect, authController_1.getMe); // Protect this route with authentication middleware
router.get('/validate', authMiddleware_1.protect, authController_1.validateToken); // Add validate token endpoint
router.post('/refresh-token', authMiddleware_1.protect, authController_1.refreshToken); // Protect this route
router.post('/forgot-password', authController_1.forgotPassword);
router.put('/reset-password/:resetToken', authController_1.resetPassword);
exports.default = router;
